---
--- Created by xby.
--- DateTime: 2024/6/24
--- Des: 沙盘ui数据 处理ui需要的数据
---

local require = require
local string = string
local pairs = pairs
local os = os
local math = math
local next = next

local SandUiData = {}

local red_const = require "red_const"
local red_system = require "red_system"
local log = require "log"
local net_login_module = require "net_login_module"
local lang = require "lang"

local event = require "event"
local sand_ui_event_define = require "sand_ui_event_define"
local moduleName = "[sand_ui_data]"
local logSwitch = false
local game_scheme = require "game_scheme"
local sandbox_pb = require "sandbox_pb"
local EntityManager = require "entity_manager"

local Screen = CS.UnityEngine.Screen

local function LogWarning(...)
    if logSwitch then
        local log = require "log"
        log.Warning(moduleName, ...)
    end
end
local function Log(...)
    if logSwitch then
        local log = require "log"
        log.LogFormat(moduleName, ...)
    end
end

--region 封装接口



--获取服务器数据可能为int最大值,则返回nil
local function GetTopicValue(value)
    if value == 2147483647 then
        return nil
    end
    return value
end
--endregion



--region 体力系统
--体力数据
local StaminaData = {
    value = 0, --体力
    gettime = 0, --每日领取时间
    getcount = 0, --每日已领取次数
    buycount = 0, --每日已购买次数
    recovery_begintime = 0, --开始恢复体力时间戳
}

--对应SandMapSearch表格中的SearchType索引唯一index
local SearchTypeMap = {
    [sandbox_pb.enSandboxSearchType_Monster_Iron] = 1001,
    [sandbox_pb.enSandboxSearchType_Monster_Food] = 1002,
    [sandbox_pb.enSandboxSearchType_Monster_Gold] = 1003,
    [sandbox_pb.enSandboxSearchType_Monster_JY] = 1004,
    [sandbox_pb.enSandboxSearchType_Monster_MassHuodong] = 1005,
    [sandbox_pb.enSandboxSearchType_Resource_Food] = 2001,
    [sandbox_pb.enSandboxSearchType_Resource_Gold] = 2002,
    [sandbox_pb.enSandboxSearchType_Resource_Iron] = 2003,
}

local staminaGetMaxCount = nil
local staminaRecoveryMaxTime = nil

local stamiaLimitCfg = nil
--体力上限
function SandUiData.GetStaminaLimit()
    if not stamiaLimitCfg then
        local game_scheme = require "game_scheme"
        stamiaLimitCfg = game_scheme:GWMapConstant_0(6)
    end
    return stamiaLimitCfg.szParam.data[0]
end

--原始体力,不包括基础恢复值
function SandUiData.GetOriginStamina()

    return StaminaData.value
end

--当前体力,所有用到体力的用这个接口
function SandUiData.GetStamina()

    if SandUiData.GetOriginStamina() < SandUiData.GetStaminaLimit() then
        return math.min(SandUiData.GetOriginStamina() + SandUiData.GetStaminaBaseRecovery(), SandUiData.GetStaminaLimit())
    else
        return SandUiData.GetOriginStamina()
    end
end

local staminaRecoveryCfg = nil
--体力基础恢复值
function SandUiData.GetStaminaBaseRecovery()
    if not staminaRecoveryCfg then
        local game_scheme = require "game_scheme"
        staminaRecoveryCfg = game_scheme:GWMapConstant_0(7)
    end
    if SandUiData.GetOriginStamina() < SandUiData.GetStaminaLimit() then
        local beginTime = SandUiData.GetStaminaBeginTime()
        if beginTime ~= 0 then
            local recoveryTime = staminaRecoveryCfg.szParam.data[0]
            local serverTime = net_login_module.GetServerTime()
            local offsetTime = serverTime - beginTime
            local recovery = math.floor(offsetTime / recoveryTime)
            return recovery
        end
    end
    return 0
end

--每日已领取次数
function SandUiData.GetStaminaReceivedCount()
    return StaminaData.getcount
end


--开始恢复体力时间戳
function SandUiData.GetStaminaBeginTime()
    return StaminaData.recovery_begintime
end

--购买次数，用来计算每次购买的价格
function SandUiData.GetBuycount()
    return StaminaData.buycount
end

--领取时间戳
function SandUiData.GetGettime()
    return StaminaData.gettime
end

---@deprecated获取体力领取的最大次数
function SandUiData.GetStaminaGetMaxCount()
    staminaGetMaxCount = staminaGetMaxCount or game_scheme:GWMapConstant_0(8).szParam.data[0]
    return staminaGetMaxCount
end

---@deprecated 获取体力恢复的最大时间
function SandUiData.GetStaminaRecoveryMaxTime()
    staminaRecoveryMaxTime = staminaRecoveryMaxTime or game_scheme:GWMapConstant_0(10).szParam.data[0]
    return staminaRecoveryMaxTime
end

--设置体力数据
function SandUiData.SetTaskData(updateData)

    StaminaData.value = GetTopicValue(updateData.stamina) or StaminaData.value
    StaminaData.gettime = GetTopicValue(updateData.gettime) or StaminaData.gettime
    StaminaData.getcount = GetTopicValue(updateData.getcount) or StaminaData.getcount
    StaminaData.buycount = GetTopicValue(updateData.buycount) or StaminaData.buycount
    StaminaData.recovery_begintime = GetTopicValue(updateData.begintime) or StaminaData.recovery_begintime
    LogWarning(Edump(StaminaData))
    event.DelayTrigger(sand_ui_event_define.GW_STAMINA_CHANGE)
    red_system.TriggerRed(red_const.Enum.StaminaMain)

end

function SandUiData.ResetCrossDay()
    StaminaData.getcount = 0
    StaminaData.buycount = 0
    event.DelayTrigger(sand_ui_event_define.GW_STAMINA_CHANGE)
end

---@deprecated 获取体力红点
function SandUiData.GetStaminaRed()
    local coolingTime = SandUiData.GetGettime() - net_login_module.GetServerTime() + SandUiData.GetStaminaRecoveryMaxTime()
    local redNum = SandUiData.GetStaminaGetMaxCount() - StaminaData.getcount
    if redNum > 0 and coolingTime <= 0 then
        --可领取体力
        return redNum
    end
    return 0
end

--初始化体力
function SandUiData.InitStaminaData()
    StaminaData = {
        value = 0, --体力
        gettime = 0, --每日领取时间
        getcount = 0, --每日已领取次数
        buycount = 0, --每日已购买次数
        recovery_begintime = 0, --开始恢复体力时间戳
    }
    red_system.RegisterRedFunc(red_const.Enum.StaminaMain, SandUiData.GetStaminaRed)
end

--endregion

function SandUiData.Init()
end

--region 实体

local entityList = {}

---------游荡怪实体的特殊处理-----------
local entityListByMarch = {}
local entityListBySidToLineId = {}
function SandUiData.GetEntityListByMarch(lineSid)
    if EntityManager.NewSrvData then
        local gw_map_util = require "gw_map_util"
        return gw_map_util.GetServerDataBySid(lineSid)
    end

    return entityListByMarch[lineSid] or 0
end

if not EntityManager.NewSrvData then
    function SandUiData.GetIsWanderMonsterBySid(sid)
        return entityListBySidToLineId[sid] or nil
    end

end

function SandUiData.SetEntityListByMarch(lineSid, sid, tp)
    if EntityManager.NewSrvData then
        local gw_map_util = require "gw_map_util"
        return gw_map_util.GetServerDataBySid(lineSid)
    end
    entityListByMarch[lineSid] = sid
    entityListBySidToLineId[sid] = { lineSid = lineSid, type = tp }
end
---------------------------------------

function SandUiData.RemoveEntityData(sid)
    local gw_map_util = require "gw_map_util"
    local selfSid = gw_map_util.GetOwnSid()
    if sid == selfSid then
        return
    end
    entityList[sid] = nil
end

if not EntityManager.NewSrvData then
    function SandUiData.UpdateEntityData(data, isMarchEntity, tp)
        local sid = data.sid
        local gw_comp_entity = require "gw_comp_entity"
        if isMarchEntity then
            --如果是游荡怪类型的实体
            gw_comp_entity = require "gw_march_entity"

            --如果有lineSid，说明是游荡怪实体更新
            if data.lineSid then
                sid = SandUiData.GetEntityListByMarch(data.lineSid) or 0
            end
        end

        local entity = nil
        entity = SandUiData.GetEntityBySid(sid)

        if not entity then
            --一般只有游荡怪才会创建,非游荡怪实体创建的话说明场景里没有缓存该实体，需要与场景沟通
            entity = gw_comp_entity:CreateInstance()
            entity:Create(data)
            if isMarchEntity then
                sid = entity:GetMonsterId() or entity:GetCarriageId()
                local cfgId = entity:GetCfgId()
                -- 因为下面的UpdateData会改变游荡怪的 entity的行军sid 为 nil ，所以在这里强行缓存一下sid
                entity.marchSid = entity.sid
                entity.isWonder = true
                entity.cfg = game_scheme:SandMapMonster_0(cfgId)
                if not data.lineSid then
                    log.Error("MarchEntity is missing", sid)
                    return
                end
                SandUiData.SetEntityListByMarch(data.lineSid, sid, tp)
            else
                --所有雷达任务都是创建的
                if data.type == GWConst.ESEntityType.RadarCollection or
                        data.type == GWConst.ESEntityType.RadarBeastInvasion or
                        data.type == GWConst.ESEntityType.RadarEnvironmentExplorate or
                        data.type == GWConst.ESEntityType.RadarDemonCastle or
                        data.type == GWConst.ESEntityType.RadarChallenge or
                        data.type == GWConst.ESEntityType.RadarTreasure then
                    entity:UpdateDetailData(data)
                else
                    log.Error("异常的非游荡怪实体创建,sid:", sid, "，请排查！！！")
                end

            end
            entityList[sid] = entity
        else
            entity:UpdateDetailData(data)
        end
        --log.Warning("采集者ID", entity.cfg.roleId)
        return entity
    end

end

---@description 根据sid获取entity类型,设置弹窗跟操作按钮的位置
---@param sid number
---@param isOperate boolean 是否操作按钮
function SandUiData.GetScreenPosBySid(sid, isOperate, marchState)
    if marchState then
        return Vector2(Screen.width / 2, Screen.height / 2)
    end
    local entity = SandUiData.GetEntityBySid(sid)
    if entity and entity.type then
        if not entity.isWonder then
            local gw_sand_mgr = require "gw_sand_mgr"
            local offset = nil
            if isOperate then
                if entity.type == sandbox_pb.enSandboxEntity_FixedMonster then
                    offset = Vector3(0, 0, 0.5)
                end
            end
            local v3 = gw_sand_mgr.GetScreenPosBySid(sid, offset) or Vector3.zero
            local v2 = Vector2(v3.x, v3.y)
            return v2
        else
            local gw_sand_mgr = require "gw_sand_mgr"
            local gw_march_common_util = require "gw_march_common_util"
            local worldPos = gw_march_common_util.SetMarchPositionBySid(entity.marchSid or entity.sid)
            if worldPos then
                worldPos = worldPos + Vector3(0, 0, 0.5)
                local v3 = gw_sand_mgr.GetScreenPosWorldPos(worldPos) or Vector3.zero
                local v2 = Vector2(v3.x, v3.y)
                return v2
            end
            --操作按钮位置根据世界坐标做偏移
        end
    end

    return Vector2(Screen.width / 2, Screen.height / 2 + 100)
end

---@description 根据sid获取entity类型,设置弹窗跟操作按钮的位置
---@param sid number
---@param isOperate boolean 是否操作按钮
function SandUiData.GetScreenPosByEntity(entity, isOperate, marchState)
    if marchState then
        return Vector2(Screen.width / 2, Screen.height / 2)
    end
    if entity and entity.type then
        if not entity.isWonder then
            local gw_sand_mgr = require "gw_sand_mgr"
            local offset = nil
            if isOperate then
                if entity.type == sandbox_pb.enSandboxEntity_FixedMonster then
                    offset = Vector3(0, 0, 0.5)
                end
            end
            local v3 = gw_sand_mgr.GetScreenPosWorldPos(entity.worldPos, offset) or Vector3.zero
            local v2 = Vector2(v3.x, v3.y)
            return v2
        else
            local gw_sand_mgr = require "gw_sand_mgr"
            local gw_march_common_util = require "gw_march_common_util"
            local worldPos = gw_march_common_util.SetMarchPositionBySid(entity.marchSid or entity.sid)
            if worldPos then
                worldPos = worldPos + Vector3(0, 0, 0.5)
                local v3 = gw_sand_mgr.GetScreenPosWorldPos(worldPos) or Vector3.zero
                local v2 = Vector2(v3.x, v3.y)
                return v2
            end
        end
    end

    return Vector2(Screen.width / 2, Screen.height / 2 + 100)
end

---获取自己的实体
function SandUiData.GetSelfEntity()
    local gw_map_util = require "gw_map_util"
    local selfSid = gw_map_util.GetOwnSid()
    return SandUiData.GetEntityBySid(selfSid)
end

--根据sid获取entity
function SandUiData.GetEntityBySid(sid)
    local gw_map_util = require "gw_map_util"
    return gw_map_util.GetServerDataBySid(sid)
end

function SandUiData.GetSandType(sid)
    if EntityManager.NewSrvData then
        local gw_map_util = require "gw_map_util"
        local entity = gw_map_util.GetServerDataBySid(sid)
        return entity and entity.type
    end

    local marchData = SandUiData.GetIsWanderMonsterBySid(sid)
    if marchData then
        return marchData.type
    end
    local entity = SandUiData.GetEntityBySid(sid)
    if not entity then
        return nil
    end
    return entity.type
end
---@public 获取沙盘实体的ViewLevelType 对应的是GWConst.ESEntityType
function SandUiData.GetSandViewLevelType(entity)
    if entity then
        return entity:GetFinalKey2()
    end
    return nil
end

---@public 获取沙盘实体是否是通用类型
---@param type number GWConst.ESEntityType
---return boolean 是否是通用类型/通用类型分类    0 通用怪物  其他预留
function SandUiData.IsSandCommonEntityByType(type)
    if type == GWConst.ESEntityType.CommonMonster then
        return true, 0
    end
    return false
end
--endregion

function SandUiData.GetIsSandBoxScene()
    local main_slg_data = require "main_slg_data"
    return main_slg_data.GetIsSandScene()
end


--当前操作的sid
local curSid = nil
function SandUiData.GetCurSid()
    return curSid
end

function SandUiData.SetCurSid(sid)
    curSid = sid
end


--队列窗口是否折叠
local isFold = false
function SandUiData.GetIsFold()
    return isFold
end
function SandUiData.SetIsFold(state)
    isFold = state
end

--当前沙盘服务器id
function SandUiData.GetServerId()
    local gw_sand_data = require "gw_sand_data"
    return gw_sand_data.selfData.GetVisualSandBoxSid()
end

--返回 战区 #660 X:273 Y:132这种字符串
function SandUiData.GetServerIdStr(gridX, gridY, serverid)
    local str = lang.Get(560111) .. (serverid or SandUiData.GetServerId())
    if gridX and gridY then
        str = str .. " " .. string.format2(lang.Get(560057), gridX) .. " " .. string.format2(lang.Get(560058), gridY)
    end
    return str
end

-- 当前盟友信息
local memberInfo = {}

function SandUiData.GetMemberInfo()
    if memberInfo == nil or next(memberInfo) == nil then
        return nil
    else
        return memberInfo
    end
end

function SandUiData.SetMemberInfo(info)
    memberInfo = info
end


--简单通用数据,存储数据比较分散的变量
local commonData = {}


--region 搜索界面数据

---怪物首杀等级
function SandUiData.GetFirstKillMonster()
    return commonData.firstKillMonster or 0
end
---设置怪物首杀等级
function SandUiData.SetFirstKillMonster(level)
    commonData.firstKillMonster = level
end

function SandUiData.GetIndexByType(type)
    return SearchTypeMap[type]
end

--沙盘直接搜索调用入口
function SandUiData.GetSearchIndexEntity(subType, level)
    local searchType = nil
    if subType == 1 then
        searchType = sandbox_pb.enSandboxSearchType_Monster_Iron
    elseif subType == 2 then
        searchType = sandbox_pb.enSandboxSearchType_Monster_Food
    elseif subType == 3 then
        searchType = sandbox_pb.enSandboxSearchType_Monster_Gold
    elseif subType == 4 then
        searchType = sandbox_pb.enSandboxSearchType_Monster_JY
    end

    local index = nil
    local cfg = game_scheme:SandMapSearch_0(SandUiData.GetIndexByType(searchType))
    if not cfg then
        log.Error("没有找到" .. subType .. "类型的配置,请检查")
        return nil
    else
        index = cfg.SearchLevelID.data[level - 1]
    end
    if index == nil then
        log.Error("没有找到" .. level .. "等级的配置,请检查" .. searchType .. "的配置")
        return nil
    end
    return index, SandUiData.GetIndexByType(searchType)
end

--搜索界面入口
function SandUiData.GetSearchIndex(type, subType, level)
    local searchType = nil
    if type == 1 then
        if subType == 1 then
            searchType = sandbox_pb.enSandboxSearchType_Monster_Iron
        elseif subType == 2 then
            searchType = sandbox_pb.enSandboxSearchType_Monster_Food
        elseif subType == 3 then
            searchType = sandbox_pb.enSandboxSearchType_Monster_Gold
        end
    elseif type == 2 then
        if subType == 1 then
            searchType = sandbox_pb.enSandboxSearchType_Resource_Iron
        elseif subType == 2 then
            searchType = sandbox_pb.enSandboxSearchType_Resource_Food
        elseif subType == 3 then
            searchType = sandbox_pb.enSandboxSearchType_Resource_Gold
        end
    elseif type == 3 then
        --集结活动预留,添加判断条件是否集结活动怪,进行怪物type替换
        local ui_war_rally_mgr = require "ui_war_rally_mgr"
        local gw_sand_data = require "gw_sand_data"
        if ui_war_rally_mgr.CheckActivtyState() and not gw_sand_data.selfData.IsCrossServiceState() then
            searchType = sandbox_pb.enSandboxSearchType_Monster_MassHuodong
        else
            searchType = sandbox_pb.enSandboxSearchType_Monster_JY
        end
    end

    local index = nil
    local cfg = game_scheme:SandMapSearch_0(SandUiData.GetIndexByType(searchType))
    if not cfg then
        log.Error("没有找到" .. subType .. "类型的配置,请检查")
        return nil
    else
        index = cfg.SearchLevelID.data[level - 1]
    end
    if index == nil then
        log.Error("没有找到" .. level .. "等级的配置,请检查" .. searchType .. "的配置")
        return nil
    end
    return index, SandUiData.GetIndexByType(searchType)
end

---怪物最大等级
function SandUiData.GetMonsterLevelMax()
    if not commonData.monsterLevelMax then
        local cfg = game_scheme:SandMapSearch_0(SandUiData.GetIndexByType(sandbox_pb.enSandboxSearchType_Monster_Iron))
        if not cfg then
            log.Error("没有找到" .. sandbox_pb.enSandboxSearchType_Monster_Iron .. "类型的配置,请检查")
            return
        else
            commonData.monsterLevelMax = cfg.HighestLevel
        end
    end
    return commonData.monsterLevelMax
end

function SandUiData.GetResourceLevelMax()
    if not commonData.resourceLevelMax then
        local cfg = game_scheme:SandMapSearch_0(SandUiData.GetIndexByType(sandbox_pb.enSandboxSearchType_Resource_Iron))
        if not cfg then
            log.Error("没有找到" .. sandbox_pb.enSandboxSearchType_Resource_Iron .. "类型的配置,请检查")
            return
        else
            commonData.resourceLevelMax = cfg.HighestLevel
        end
    end
    return commonData.resourceLevelMax
end

function SandUiData.GetMonsterEliteLevelMax()
    if not commonData.eliteLevelMax then
        --local level = 1
        --while true do
        --    local cfg = game_scheme:SandMapMonster_1(4, level)
        --    if not cfg then
        --        break
        --    else
        --        commonData.eliteLevelMax = level
        --        level = level + 1
        --    end
        --end
        local ui_war_rally_mgr = require "ui_war_rally_mgr"
        if ui_war_rally_mgr.CheckActivtyState() then
            local gw_home_building_data = require "gw_home_building_data"
            commonData.eliteLevelMax = gw_home_building_data.GetBuildingMainLevel()
        else
            local cfg = game_scheme:SandMapSearch_0(SandUiData.GetIndexByType(sandbox_pb.enSandboxSearchType_Monster_JY))
            if not cfg then
                log.Error("没有找到" .. sandbox_pb.enSandboxSearchType_Monster_JY .. "类型的配置,请检查")
                return
            else
                commonData.eliteLevelMax = cfg.HighestLevel
            end
        end
    end
    return commonData.eliteLevelMax
end

local autoLv = nil
--判断是否自动获取可攻击最大怪物等级
function SandUiData.GetIsMonsterAutoMax(subType)
    --小于等级则必定自动
    local player_mgr = require("player_mgr")
    local lv = player_mgr.GetPlayerLV()
    autoLv = autoLvCfg or game_scheme:GWMapConstant_0(87).szParam.data[0]
    if lv <= autoLv then
        return true
    end
    local isNotAuto = SandUiData.GetMonsterSearchChanged(subType)
    return not isNotAuto
end

---@description 获取是否该次登录是否手动修改过等级
function SandUiData.GetMonsterSearchChanged(subType)
    if not commonData.searchData then
        commonData.searchData = {}
    end
    return commonData.searchData[subType] or false
end

---@description 设置是否该次登录是否手动修改过等级
function SandUiData.SetMonsterSearchChanged(subType)
    if not commonData.searchData then
        commonData.searchData = {}
    end
    commonData.searchData[subType] = true
end

--endregion


--region 迁城数据
local relocationGoodSid = nil
local relocationGoodId = nil

local baseDefFailData = nil

--@description 获取迁城道具id
function SandUiData.GetRelocationGoodID()
    if not relocationGoodId then
        relocationGoodId = game_scheme:GWMapConstant_0(3).szParam.data[0] or 0
    end
    return relocationGoodId
end

--@description 获取迁城道具数量
local player_mgr
function SandUiData.GetRelocationGoodNumById()
    local goodsId = SandUiData.GetRelocationGoodID()
    if goodsId > 0 then
        player_mgr = player_mgr or require "player_mgr"
        return player_mgr.GetPlayerOwnNum(goodsId)
    else
        return 0
    end
    return 0
end

function SandUiData.GetBaseDefFailData()
    return baseDefFailData
end

function SandUiData.SetBaseDefFailData(data)
    baseDefFailData = data
end
--endregion






--退出沙盘清除数据
function SandUiData.ClearSandEntity()
    --清理实体
    for _, entity in pairs(entityList) do
        entity:Dispose()
    end
    entityList = {}
end

function SandUiData.ClearData()
    SandUiData.ClearSandEntity()
    memberInfo = {}
    commonData = {}

    baseDefFailData = nil

    SandUiData.InitStaminaData()
end

function SandUiData.OnSandSceneChange()
    SandUiData.ClearSandEntity()
end

event.Register(sand_ui_event_define.GW_SAND_SCENE_CHANGE, SandUiData.OnSandSceneChange)

event.Register(event.USER_DATA_RESET, SandUiData.ClearData)
SandUiData.InitStaminaData()

return SandUiData
