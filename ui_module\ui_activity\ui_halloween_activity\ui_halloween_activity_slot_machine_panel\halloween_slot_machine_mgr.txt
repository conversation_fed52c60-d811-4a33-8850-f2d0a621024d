local require = require

local table = table
local pairs = pairs
local ipairs = ipairs
local type = type
local string = string
local math = math
local tostring = tostring
local festival_activity_mgr = require "festival_activity_mgr"
local game_scheme = require "game_scheme"
local log = require "log"
local gw_task_data = require "gw_task_data"
local data_mgr = require "data_mgr"
local event = require "event"
local red_system = require "red_system"
local red_const = require "red_const"
local event_task_define = require "event_task_define"
local gw_event_activity_define = require "gw_event_activity_define"
local gw_task_const = require "gw_task_const"
local time_util = require "time_util"
local os = os

local item_data = require "item_data"

local halloween_activity_slot_machine_const = require "halloween_activity_slot_machine_const"
local halloween_activity_slot_machine_setting_data = require "halloween_activity_slot_machine_setting_data"
local halloween_activity_slot_machine_history_data = require "halloween_activity_slot_machine_history_data"
local halloween_activity_slot_machine_rate_data = require "halloween_activity_slot_machine_rate_data"

local data_personalInfo =  require "data_personalInfo"
local event_personalInfo = require "event_personalInfo"
local net_login_module = require "net_login_module"

---@class halloween_slot_machine_mgr
local halloween_slot_machine_mgr = {}

---所有数据存储
local _d = data_mgr:CreateData("halloween_slot_machine_mgr")
---非服务器数据存储
local mc = _d.mde.const

function halloween_slot_machine_mgr.Init()
    event.Register(event.USER_DATA_RESET, halloween_slot_machine_mgr.Clear)

    event.Register(event.ACTOR_LOGIN_FINISH, halloween_slot_machine_mgr.OnLogin)
    event.Register(event_personalInfo.UPDATE_BASE_INFO, halloween_slot_machine_mgr.OnUpdateBaseInfo)
    
    event.Register(event_task_define.REFRESH_TASK, halloween_slot_machine_mgr.TaskPointUpdate) --任务刷新,红点注册
    red_system.RegisterRedFunc(red_const.Enum.HalloweenSlotMachine, halloween_slot_machine_mgr.TaskCanReceiveRewardRed)

end

function halloween_slot_machine_mgr.OnLogin()
    
end

function halloween_slot_machine_mgr.OnUpdateBaseInfo(k, v)
    --local dump = _G["Edump"]
    --log.Log("halloween_slot_machine_mgr.OnUpdateBaseInfo: k:"..tostring(k)..", v:"..tostring(dump(v)))
    if v[data_personalInfo.PropEnum.RoleID] and v[data_personalInfo.PropEnum.RoleID].newValue > 0 then
        halloween_activity_slot_machine_setting_data:Init()
        halloween_activity_slot_machine_rate_data.Init(halloween_slot_machine_mgr.GetActivityID())

        local serverTimeZone = net_login_module.GetServerTimeZone()
        halloween_activity_slot_machine_history_data:Init()
        -- test
        --halloween_activity_slot_machine_history_data:SetTestData()
    end
end



---@public function 设置活动ID
function halloween_slot_machine_mgr.SetActivityID(activityID)
    mc.activityID = activityID
end

function halloween_slot_machine_mgr.SetSlotGameActivityID(activityID)
    mc.slotGameactivityID = activityID
end

function halloween_slot_machine_mgr.SetProgressTaskGameActivityID(activityID)
    mc.progressTaskactivityID = activityID
end

function halloween_slot_machine_mgr.SetDailyGiftActivityID(activityID)
    mc.dailyGiftactivityID = activityID
end

---@public function 获取活动ID
function halloween_slot_machine_mgr.GetActivityID()
    return mc.activityID or halloween_activity_slot_machine_const.act_id.slot_game
end

function halloween_slot_machine_mgr.GetSlotGameActivityId()
    return mc.slotGameactivityID or halloween_activity_slot_machine_const.act_id.slot_game
end

function halloween_slot_machine_mgr.GetActivityCfg()
    return festival_activity_mgr.GetActivityCfgByActivityID(halloween_slot_machine_mgr.GetActivityID())
end

function halloween_slot_machine_mgr.GetProgressTaskActivityID()
    return mc.progressTaskactivityID or halloween_activity_slot_machine_const.act_id.progressReward
end

function halloween_slot_machine_mgr.GetDailyGiftActivityID()
    return mc.dailyGiftactivityID or halloween_activity_slot_machine_const.act_id.dailyPackage
end

function halloween_slot_machine_mgr.GetCoinID()
    local id = halloween_slot_machine_mgr.GetSlotGameActivityId()
    local festivalData = festival_activity_mgr.GetActivityCfgByActivityID(id)
    return festivalData.ctnID1[1][0]
end

---@public function 获取进度任务配置
---@return model_task_cfg_data[]
function halloween_slot_machine_mgr.GetActivityProgressTaskCfgList()
    local festivalCfgData = festival_activity_mgr.GetActivityCfgByActivityID(halloween_slot_machine_mgr.GetProgressTaskActivityID())
    local activityData = festival_activity_mgr.GetActivityDataByActivityID(halloween_slot_machine_mgr.GetProgressTaskActivityID())
    local taskListFromZero = festivalCfgData.ctnID1[1]
    local count = festivalCfgData.ctnID1[2]
    local taskcfgList = {}
    for i = 0, count - 1 do
        table.insert(taskcfgList, taskListFromZero[i])
    end
    return taskcfgList
end

---@public function 获取当前已经抽奖多少次
function halloween_slot_machine_mgr.GetCurDrawCount()
    local taskData = gw_task_data.GetTaskData(halloween_activity_slot_machine_const.task_id_for_show)
    if not taskData then
        log.Error("halloween_slot_machine_mgr.GetCurDrawCount: taskData is nil, id:" .. tostring(halloween_activity_slot_machine_const.task_id_for_show))
        return 0
    end
    return taskData.rate
end

---@public function 获取今天已经抽奖多少次
---@return int 当前次数, int 最大次数
function halloween_slot_machine_mgr.GetTodayDrawCount()
    local festivalCfgData = festival_activity_mgr.GetActivityCfgByActivityID(halloween_slot_machine_mgr.GetSlotGameActivityId())
    local activityData = festival_activity_mgr.GetActivityDataByActivityID(halloween_slot_machine_mgr.GetSlotGameActivityId())


    local bits = activityData.bitData
    local day = bits[4] --距离活动开启第几天
    local count = bits[5]


    local startTimeZero = time_util.GetTimeOfZero(os.server_zone(), activityData.startTimeStamp) - 86400      --当天零点
    local curDay = math.ceil((os.server_time() - startTimeZero)/86400)
    if curDay > day then
        count = 0
    end
    
    local max = game_scheme:InitBattleProp_0(halloween_activity_slot_machine_const.max_day_draw_count_cfg_id)[2][1]
    local cur = count
    return cur, max
end

---@public function 获取进度任务array
---@return model_task_data[], model_task_cfg[]
function halloween_slot_machine_mgr.GetProgressRewardTasks()
    local festivalData = festival_activity_mgr.GetActivityCfgByActivityID(halloween_slot_machine_mgr.GetProgressTaskActivityID())
    local id_array = festivalData.ctnID1[1]
    local count = festivalData.ctnID1[2]
    local task_data_list = {}
    local task_cfg_list = {}
    for i = 0, festivalData.ctnID1.count - 1 do
        local id = id_array[i]
        local cfg = game_scheme:TaskMain_0(id)
        
        if id ~= halloween_activity_slot_machine_const.task_id_for_show then --有一个大数任务是用于展示的
            table.insert(task_cfg_list, cfg)
            local taskData = gw_task_data.GetTaskData(id_array[i])
            if taskData ~= nil then
                table.insert(task_data_list, taskData)
            else
                log.Error("halloween_slot_machine_mgr.GetProgressRewardTasks: taskData is nil, taskID:"..tostring(id))
            end
        end
    end
    
    return task_data_list, task_cfg_list
end


---@public function 获取下一个进度
---@return model_task_data|nil 目标任务, model_task_data|nil 当前已完成的任务
function halloween_slot_machine_mgr.GetNextProgressTask()
    local task_array, task_cfg_list = halloween_slot_machine_mgr.GetProgressRewardTasks()
    local curCount = halloween_slot_machine_mgr.GetCurDrawCount()
    --根据配置的进度要求计算下一个进度奖励ID
    for i = 1, #task_array do
        local taskData = task_array[i]
        if not taskData.status then
            return taskData, task_array[i-1]
        end
    end
    
    -- 没找到，说明已经满了
    return nil, task_array[#task_array]
end

---@public function 获取下一个进度奖励ID
---@return number|nil
function halloween_slot_machine_mgr.GetNextProgressReward()
    local task_array, task_cfg_list = halloween_slot_machine_mgr.GetProgressRewardTasks()
    local curCount = halloween_slot_machine_mgr.GetCurDrawCount()
    --根据配置的进度要求计算下一个进度奖励ID
    for i = 1, task_array.count do
        local taskData = task_array[i]
        if taskData.ConditionValue1 > curCount then
            return halloween_slot_machine_mgr.GetTaskReward(taskData)
        end
    end
end

function halloween_slot_machine_mgr.GetTaskReward(task_data)
    if task_data == nil then
        return {}
    end
    local reward_mgr = require "reward_mgr"
    local task_cfg = game_scheme:TaskMain_0(task_data.taskId)
    if task_cfg == nil then
        log.Error(string.format("task_cfg is nil, taskId:%d", task_data.taskId))
        return nil
    end
    local reward_id = task_cfg.TaskReward
    local rewardCfg = reward_mgr.GetRewardGoodsList(reward_id)
    local dataList = {}
    local dataCount = 0
    for i2,v in pairs(rewardCfg) do
        local item =
        {
            id = v.id,
            count = v.num,
        }
        table.insert(dataList,item)
        dataCount = dataCount + 1
    end
    -- 这里默认只有一个
    return dataList[1], reward_id
end


-- 检测可领取任务列表
function halloween_slot_machine_mgr.CheckReceiveList()
    local task_data_list, task_cfg_list = halloween_slot_machine_mgr.GetProgressRewardTasks()
    local taskIDListReceive = {}
    for i, v in ipairs(task_cfg_list) do
        local taskDataServer = task_data_list[i]
        local taskData = task_cfg_list[i]
        if not taskDataServer then
            log.Warning("[halloween_slot_machine_mgr:CheckReceiveList] taskDataServer is nil, taskID = %d", v)
        end
        if taskDataServer and taskDataServer.rate >= taskData.ConditionValue1 then
            if not taskDataServer.status then
                taskIDListReceive[i] = task_data_list[i].taskId
            end
        end
    end
    return taskIDListReceive
end


--- 通过TaskId 检查是否能领奖
function halloween_slot_machine_mgr.TaskIdCheckReqTask(taskId)
    local taskData = halloween_slot_machine_mgr.GetTaskMainData(taskId)
    local canReq = halloween_slot_machine_mgr.CheckReqTask(taskData)
    return canReq
end


-- 获取任务总表的数据
function halloween_slot_machine_mgr.GetTaskMainData(taskId)
    local taskData = game_scheme:TaskMain_0(taskId)
    return taskData
end

--获取任务数据的图标
function halloween_slot_machine_mgr.GetTaskMainDataIcon(taskId)
    local taskData = halloween_slot_machine_mgr.GetTaskMainData(taskId)
    return taskData.Icon.data[taskData.Icon.count - 1]
end


---检查是否能领取宝箱奖励
function halloween_slot_machine_mgr.CheckReqTask(taskData)
    local len = taskData.ConditionValue2.count
    local data = taskData.ConditionValue2.data
    if data == nil then
        return false
    end
    
    local result = 0
    for i = 0, len do
        local taskData = gw_task_data.GetTaskData(data[i])
        if taskData ~= nil then
            if taskData.rate >= taskData.completeValue then
                result = result + 1
            end
        end
    end
    return result == taskData.ConditionValue2.count
end

---检查宝箱是否已经给领取
function halloween_slot_machine_mgr.CheckBoxReqState(task)
    local taskData =  gw_task_data.GetTaskData(task)
    if taskData == nil then
        return false
    end
    
    return taskData.status
end


--- 某一个宝箱可以领取时红点
function halloween_slot_machine_mgr.GetCurTaskReceiveRewardRed(taskId)
    local canReceive = halloween_slot_machine_mgr.CheckBoxReqState(taskId)
    if canReceive == true then --已经给领取了没有红点
        return 0 
    end
    local gw_task_mgr = require "gw_task_mgr"
    local result =  gw_task_mgr.GetTaskIsReceive(taskId)
    if result == true then
        return 1
    end
    return 0
end

---@public function 任务刷新，更新红点
function halloween_slot_machine_mgr.TaskPointUpdate(_, _,moduleId, moduleList)
    local gw_task_const = require "gw_task_const"
    if moduleList[gw_task_const.TaskModuleType.HalloweenSignIn] then
        red_system.TriggerRed(red_const.Enum.HalloweenSignInReward)
        event.Trigger(gw_event_activity_define.GW_ACTIVITY_RED_NEED_UPDATE,halloween_slot_machine_mgr.GetActivityID())
    end
end

---@public function 获取红点数量
function halloween_slot_machine_mgr.TaskCanReceiveRewardRed()
    local festivalData = festival_activity_mgr.GetActivityCfgByActivityID(halloween_slot_machine_mgr.GetActivityID())
    local receiveCount = 0
    if festivalData then
        local gw_task_mgr = require "gw_task_mgr"
        for i = 0, festivalData.ctnID1.count - 1 do
            local result =  gw_task_mgr.GetTaskIsReceive(festivalData.ctnID1.data[i])
            if result then
                receiveCount = receiveCount + 1
            end
        end
    end
    return receiveCount
end



--其他参数放到otherParam里
function halloween_slot_machine_mgr.ShowReward(rewardData, callBack, onShowCall, _useItemAnim,otherParam)
    otherParam = otherParam or {}
    local heroList = {}
    --local gw_hero_data = require "gw_hero_data"
    for k, v in pairs(rewardData or {}) do
        if v.nType == item_data.Reward_Type_Enum.Hero then
            -- local heroEntity = gw_hero_data.GetHeroCfgId2Entity(v.id)
            -- if not heroEntity then
            --如果英雄不存在
            table.insert(heroList, v.id)
            --end
        end
    end
    if heroList[1] then
        --需要先展示英雄
        local ui_show_hero_card = require "ui_show_hero_card"
        local heroEntityList = {}
        for i, v in ipairs(heroList) do
            local cfg = game_scheme:Hero_0(v)
            if cfg and cfg.rarityType > 2 then
                --蓝色品质不展示(策划易闻)
                heroEntityList = ui_show_hero_card.ParseArrHeroId(v, heroEntityList)
            end
        end
        if heroEntityList[1] then
            local gw_popups_data = require "gw_popups_data"
            gw_popups_data.AddMessage("ui_show_hero_card",function()
                ui_show_hero_card.ShowWithData(heroEntityList, function()
                    gw_popups_data.ShowNextMessage(0)
                    halloween_slot_machine_mgr.ShowRewardUI(rewardData, callBack, onShowCall, _useItemAnim,otherParam)
                end, nil, nil, true)
            end,gw_popups_data.PopupsType.Reward)    
            return
        end
    end
    halloween_slot_machine_mgr.ShowRewardUI(rewardData, callBack, onShowCall, _useItemAnim,otherParam)
end

function halloween_slot_machine_mgr.ShowRewardUI(rewardData, callBack, onShowCall, _useItemAnim,otherParam)
    otherParam = otherParam or {}
    local listData = { title = "", dataList = rewardData, useItemAnim = _useItemAnim ,otherParam = otherParam}
    local showData = {}
    table.insert(showData, listData)
    local gw_popups_data = require "gw_popups_data"

    local ui_name = "ui_halloween_reward_result"

    gw_popups_data.AddMessage(ui_name,function()
        local ui_window_mgr = require "ui_window_mgr"
        local ui_reward_result = require (ui_name)
        ui_reward_result.SetInputParam(showData, nil, nil, nil, nil, nil, nil, nil, nil, nil, otherParam.NotSortList)
        ui_window_mgr:ShowModule(ui_name, onShowCall, function()
            gw_popups_data.ShowNextMessage(0)
            if callBack then
                local f, res = xpcall(function()
                    callBack()
                end, debug.traceback)
                if not f then
                    log.Error(res)
                end
            end
        end)
    end,gw_popups_data.PopupsType.Reward)    
end







function halloween_slot_machine_mgr.Clear()
    _d.mde:clear()
    mc = _d.mde.const
end

return halloween_slot_machine_mgr