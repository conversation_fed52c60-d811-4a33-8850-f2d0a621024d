local print = print
local require = require
local pairs = pairs     
local ipairs = ipairs
local string = string
local table = table
local tostring = tostring
local math = math
local tonumber = tonumber
local type = type
local Common_Util = CS.Common_Util.UIUtil

local enum_define = require "enum_define"
local ui_base = require "ui_base"
local lang = require "lang"
local util = require "util"
local class = require "class"
local player_mgr = require "player_mgr"
local game_scheme = require "game_scheme"
local binding = require "ui_desert_storm_select_player_panel_binding"
local event_DesertStrom_define = require("event_DesertStrom_define")
local os = os
local time_util = require "time_util"
local face_item = require "face_item_new"
local gw_storm_mgr = require "gw_storm_mgr"
local GameObject		= CS.UnityEngine.GameObject
local log = require "log"
local typeof = typeof
local ScrollRectItem = CS.UI.UGUIExtend.ScrollRectItem
local Array = CS.System.Array
local Vector3 	 = CS.UnityEngine.Vector3
local Vector2 = CS.UnityEngine.Vector2
local ImageGray = CS.War.UI.ImageGray
local RectTransform = CS.UnityEngine.RectTransform
local LayoutRebuilder = CS.UnityEngine.UI.LayoutRebuilder

--region View Life
module("ui_desert_storm_select_player_panel")
local ui_path = binding.UIPath
local window = nil
local UIView = {}
local OFFSET = 6

local fullSize = 169
local miniSize = 146
local fullSizeFit = 149
local miniSizeFit = 125

UIView.widget_table = binding.WidgetTable

function UIView:SetVCTypeUI()
    return self.__base.SetVCTypeUI(self, enum_define.enum_ui_vc_type.vc)
end

function UIView:Init()
    self:SetVCTypeUI()
    self.__base.Init(self)
    self.playerData = {}
    self.VData = {}
    self.createObj = {}
    self.srt_item = self.rtf_DesertStormHeaderObj:GetComponent(typeof(ScrollRectItem))
    self.cacheIndex = -1
    self.sr_ScrollView.onValueChanged:AddListener(function(scrollPos)
        local targetPosY = self:GetUIWorldPos(self.rtf_DesertStormHeaderObj,1) --目标位置左下角
        self.targetItem = nil
        local posY = 0
        local index = 0
        for i =5,1,-1 do
            local allianceListObj = self.createObj[i]
            if allianceListObj and allianceListObj.item then
                local item = allianceListObj.item
                local Toggle = item:Get("Toggle")
                if Toggle then
                    local header = item:Get("Bg")
                    local btnPosY = self:GetUIWorldPos(header,1)
                    if btnPosY > targetPosY - 0.5 then
                        if Toggle.isOn then
                            self.targetItem = allianceListObj.item
                            posY = btnPosY
                            index = i
                        else
                            self.targetItem = nil
                            posY = 0
                            index = 0
                        end
                    end
                end
            end
        end
        
        if self.targetItem then
            --log.Error("左上角位置：",curPosY)
            if posY > targetPosY then
                self:OnShowHeaderObj(self.targetItem,index)
            else
                self.targetItem = nil
                self:OnHideHeaderObj()
            end
        else
            self:OnHideHeaderObj()
        end
        
    end)
    self.gray_ConfirmSelectTime = self.btn_ConfirmSelectTime.transform:GetComponent(typeof(ImageGray))
    
    self.rtf_HelpTips = self.ator_HelpTips.transform:GetComponent(typeof(RectTransform))
    self.rtf_DropDownBg = self.ator_DropDownBg.transform:GetComponent(typeof(RectTransform))
    self.rtf_allPlayer = self.ator_DropDownPlayerBg.transform:GetComponent(typeof(RectTransform))
    self.rtf_mask = self.btn_mask.transform:GetComponent(typeof(RectTransform))
    --self.baseHelpPos = self.ator_HelpTips.transform.localPosition.y
    --self.baseDropPos = self.ator_DropDownBg.transform.localPosition.y
    --self.fixHeight = self.obj_ChangeTimeTog:GetComponent(typeof(RectTransform)).rect.height
end

function UIView:OnShowHeaderObj(value,level,isUpdate)
    if level == self.cacheIndex then
        return
    end
    if not isUpdate then
        self.cacheIndex = level
    end
    Common_Util.SetActive(self.rtf_DesertStormHeaderObj,true)
    local baseToggle = self.srt_item:Get("Toggle")
    baseToggle.onValueChanged:RemoveAllListeners()
    
    local PlayerList = value:Get("PlayerList")
    local Toggle = value:Get("Toggle")
    baseToggle.onValueChanged:AddListener(function(value1)
        Common_Util.SetActive(PlayerList,value1)
        Toggle.isOn = value1
    end)
    local basePosIcon = self.srt_item:Get("PosIcon")
    basePosIcon:Switch(level - 1)
    local baseTotalPlayerCount = self.srt_item:Get("TotalPlayerCount")
    local TotalPlayerCount = value:Get("TotalPlayerCount")
    baseTotalPlayerCount.text = TotalPlayerCount.text
    
    local baseBattlePlayerCount = self.srt_item:Get("BattlePlayerCount")
    local BattlePlayerCount = value:Get("BattlePlayerCount")
    baseBattlePlayerCount.text = BattlePlayerCount.text
    
    local baseReplacePlayerCount = self.srt_item:Get("ReplacePlayerCount")
    local ReplacePlayerCount = value:Get("ReplacePlayerCount")
    baseReplacePlayerCount.text = ReplacePlayerCount.text
end

function UIView:OnHideHeaderObj()
    self.cacheIndex = -1
    Common_Util.SetActive(self.rtf_DesertStormHeaderObj,false)
end

function UIView:HideConfirmSelectTimeBtn()
    self.gray_ConfirmSelectTime:SetEnable(true)
    self.gray_ConfirmSelectTimeText:SetEnable(true)
    self.btn_ConfirmSelectTime.interactable = false
end

function UIView:ShowConfirmSelectTimeBtn()
    self.gray_ConfirmSelectTime:SetEnable(false)
    self.gray_ConfirmSelectTimeText:SetEnable(false)
    self.btn_ConfirmSelectTime.interactable = true
end

function UIView:OnShow()
    self.__base.OnShow(self)
end

function UIView:OnHide()
    self.__base.OnHide(self)
end

function UIView:Close(data)   
    if self.VData then
        for i, v in pairs(self.VData) do
            if type(v) == "table" and v.Dispose then
                v:Dispose()
            end
        end
    end
    if self.maskTimer then
        util.RemoveDelayCall(self.maskTimer)
        self.maskTimer = nil
    end
    if self.twincleTimer then
        util.RemoveDelayCall(self.twincleTimer)
        self.twincleTimer = nil
    end
    if self.srt_item then
        local baseToggle = self.srt_item:Get("Toggle")
        if baseToggle then
            baseToggle.onValueChanged:RemoveAllListeners()
        end
    end
    if self.createObj then
        for i = 5,1,-1 do
            local allianceListObj = self.createObj[i]
            if allianceListObj and self.playerData[i] then
                item = allianceListObj.item
                if item then
                    local Toggle = item:Get("Toggle")
                    if Toggle then
                        Toggle.onValueChanged:RemoveAllListeners()
                    end
                end
                for j,k in ipairs(self.playerData[i]) do
                    local tempObj = allianceListObj.playerList[j]
                    local item2 = {}
                    if tempObj then
                        item2 = tempObj.item
                        local battleToggle = item2:Get("battleToggle")
                        local replacementToggle = item2:Get("replacementToggle")
                        battleToggle.onValueChanged:RemoveAllListeners()
                        replacementToggle.onValueChanged:RemoveAllListeners()
                    end
                end
            end

        end
    end
    
    self.VData = nil    
    self.__base.Close(self)
    window = nil
end
--endregion

--region View Logic
function UIView:OnShowPanel(data)
    self.data = data
    local alliance_pb = require "alliance_pb"
    local alliance_mgr = require "alliance_mgr"
    
    local temp, authority = alliance_mgr.GetRoleAuthority(player_mgr.GetPlayerRoleID())
    local isR5 = (authority >= alliance_pb.emAllianceAuthority_R5)
    Common_Util.SetActive(self.btn_Confirm,not isR5)
    Common_Util.SetActive(self.btn_darkMask,false)
    local activityState = self.data.activityData.GetActivityState()
    local isSignUpState = activityState == event_DesertStrom_define.EnDesertStrom_ActivityType.enDesertStrom_ActivityType_SignUp
    Common_Util.SetActive(self.obj_ChangeTimeTog,false)
    LayoutRebuilder.ForceRebuildLayoutImmediate(self.rtf_UpgradeTips)
    self.txt_TeamATxt.text = string.format2("{%s1}{%s2}",lang.Get(1003442),"A")
    --if isSignUpState then
    --    Common_Util.SetLocalPos(self.ator_HelpTips,0,self.baseHelpPos,0)
    --    Common_Util.SetLocalPos(self.ator_DropDownBg,0,self.baseDropPos,0)
    --else
    --    Common_Util.SetLocalPos(self.ator_HelpTips,0,self.baseHelpPos+self.fixHeight,0)
    --    Common_Util.SetLocalPos(self.ator_DropDownBg,0,self.baseDropPos+self.fixHeight,0)
    --end
    self.tog_changeTime_1.isOn = data.isServerDate
    self.tog_changeTime_2.isOn = data.isServerDate
    self.timeList = self.data.activityData.OnGetBattleTimeList()
    self:OnSetTime(data.isServerDate)
    self:SetOrUpdatePlayerList(false)
    self:SetOrUpdateTimeHope()

    --延后0.5秒
    util.DelayCallOnce(0.5,function()
        local sourceWorldPos = self.rtf_TimeSelectDropDown.position

        -- 转换到目标坐标系
        local targetLocalPos1 = self.rtf_mask:InverseTransformPoint(sourceWorldPos)
        
        Common_Util.SetAncoPosition(self.rtf_DropDownBg, targetLocalPos1.x, targetLocalPos1.y - self.rtf_TimeSelectDropDown.rect.height / 2 + 21)
        Common_Util.SetAncoPosition(self.rtf_HelpTips, targetLocalPos1.x, targetLocalPos1.y - self.rtf_TimeSelectDropDown.rect.height / 2)
        Common_Util.SetAncoPosition(self.rtf_allPlayer, targetLocalPos1.x, targetLocalPos1.y - self.rtf_TimeSelectDropDown.rect.height / 2 + 21)
    end)
end

function UIView:SetOrUpdateTimeHope()
    local mySignUpInfo = self.data.activityData.OnGetMySignInfo()
    local hasSelectTime = mySignUpInfo.battle_time_modify
    if mySignUpInfo and mySignUpInfo.battle_time_hope then
        for i,v in ipairs(mySignUpInfo.battle_time_hope) do
            local objPath = string.format2("tog_TimeSelectToggle_{%s1}",v)
            self[objPath].isOn = true
            --hasSelectTime = true
        end
    end
    if hasSelectTime then
        for i=1,3 do
            local objPath = string.format2("tog_TimeSelectToggle_{%s1}",i)
            self[objPath].interactable = false
        end
        self:HideConfirmSelectTimeBtn()
    end
end

function UIView:SetOrUpdatePlayerList(isUpdate,selectTime,selectTeam)
    local signUpInfo = self.data.activityData.OnGetSignUpInfo()
    local activityState = self.data.activityData.GetActivityState()
    local cfg = gw_storm_mgr.GetStormCfg().GetDesertStormConfig()
    self.txt_BattleCount.text = string.format2("{%s1}/{%s2}",signUpInfo.battleCount,cfg.CombatantCap)
    self.txt_ReplacementCount.text = string.format2("{%s1}/{%s2}",signUpInfo.replacementCount,cfg.SubstituteCap)
    
    local isSignUpState = activityState == event_DesertStrom_define.EnDesertStrom_ActivityType.enDesertStrom_ActivityType_SignUp
    Common_Util.SetActive(self.obj_ButtonGroup,isSignUpState == true)
    if not isSignUpState then
        Common_Util.SetSize(self.rtf_PlayerListScrollview,687,716)
    else
        Common_Util.SetSize(self.rtf_PlayerListScrollview,687,544)
    end
    local alliance_user_data = require "alliance_user_data"
    if isSignUpState then
        if selectTime then
            self.txt_selectTime.text = self.isServerTime and self.timeList[selectTime].serverTime or self.timeList[selectTime].selfTime
        else
            self.txt_selectTime.text = lang.Get(1003497)
        end
    else
        if selectTeam then
            self.txt_selectTime.text = string.format2("{%s1}{%s2}",lang.Get(1003442),"A")
        else
            self.txt_selectTime.text = lang.Get(15518)
        end

    end

    self.playerData = {}
    self.selectTime = selectTime
    self.selectTeam = selectTeam or event_DesertStrom_define.EnDesertStrom_Team.enDesertStrom_Team_A
    local selectTimePlayer = {}
    local selectTimePlayerCount = {}
    local allPlayer = 0
    local allSelectPlayer = {}
    for i = 5,1,-1 do
        local playerList = alliance_user_data.GetRankSortData(i, true)
        self.playerData[i] = {}
        local totalCount = 0
        local battleCount = 0
        local replaceCount = 0
        local showSelectPlayer = false --表示当前在筛选状态下是否有至少一个玩家需要显示
        local totalPlayer = 0
        for j,k in pairs(playerList) do
            local isSelect = false
            --if i == 5 then
            --    isSelect = true --盟主必定被选中
            --end
            allPlayer = allPlayer + 1
            local temp = {}
            temp.timeHope = {}
            if signUpInfo.roleData[k.roleId] then
                temp.enTeam = signUpInfo.roleData[k.roleId].enTeam
                temp.isSignUp = temp.enTeam ~= event_DesertStrom_define.EnDesertStrom_Team.enDesertStrom_Team_None;
                temp.isBattle = temp.isSignUp and signUpInfo.roleData[k.roleId].main or false
                for l,m in ipairs(signUpInfo.roleData[k.roleId].battle_time_hope) do
                    table.insert(temp.timeHope,m)
                    if selectTime and m == selectTime then
                        isSelect = true
                        showSelectPlayer = true
                    end
                    if not selectTimePlayerCount[m] then
                        selectTimePlayerCount[m] = 0
                    end
                    selectTimePlayerCount[m] = selectTimePlayerCount[m] + 1
                end
                if temp.isSignUp then
                    if temp.enTeam == self.selectTeam then
                        if not allSelectPlayer[temp.enTeam] then
                            allSelectPlayer[temp.enTeam] = 0
                        end
                        allSelectPlayer[temp.enTeam] = allSelectPlayer[temp.enTeam] + 1
                        if selectTeam then
                            isSelect = true
                        end
                    end
                    if signUpInfo.roleData[k.roleId].main then
                        battleCount = battleCount + 1
                    else
                        replaceCount = replaceCount + 1
                    end
                end
            else
                if isSignUpState then
                    temp.isSignUp = false;
                    temp.isBattle = false;
                    temp.enTeam = nil
                end
            end
            temp.playerInfo = k
            if not selectTime or (isSelect and selectTime) or (isSelect and selectTeam) then
                if isSignUpState and temp.isBattle and selectTime then
                    table.insert(self.playerData[i],temp)
                elseif not isSignUpState then
                    if (isSelect and selectTeam and temp.isSignUp) or not selectTeam then
                        table.insert(self.playerData[i],temp)
                    end
                elseif isSignUpState and not selectTime then
                    table.insert(self.playerData[i],temp)
                end
                if (isSelect and selectTime) or (isSelect and selectTeam) then
                    totalPlayer = totalPlayer + 1
                end
            end
            totalCount = totalCount + 1
        end
        local allianceListObj = self.createObj[i]
        local item = {}
        if not selectTime and not selectTeam then
            totalPlayer = battleCount + replaceCount
        end
        if not allianceListObj then
            local go = GameObject.Instantiate(self.obj_CreateDesertStormHeaderObj,self.tf_Content)
            Common_Util.SetActive(go,true)
            local trans = go.transform
            item = Common_Util.GetComponent(trans,typeof(ScrollRectItem),"")
            allianceListObj = {}
            allianceListObj.playerList = {}
            allianceListObj.rect = Common_Util.GetComponent(trans,typeof(RectTransform),"")
        else
            item = allianceListObj.item
        end
        local icon = item:Get("PosIcon")
        icon:Switch(i-1)
        local TotalPlayerCount = item:Get("TotalPlayerCount")
        TotalPlayerCount.text = string.format2("{%s1}/{%s2}",totalPlayer,totalCount) 
        local BattlePlayerCount = item:Get("BattlePlayerCount")
        BattlePlayerCount.text = battleCount
        local ReplacePlayerCount = item:Get("ReplacePlayerCount")
        ReplacePlayerCount.text = replaceCount
        local Toggle = item:Get("Toggle")

        local PlayerList = item:Get("PlayerList")
        local DesertStormPlayerObj = item:Get("DesertStormPlayerObj")
        for j,k in ipairs(allianceListObj.playerList) do
            Common_Util.SetActive(k.item,false)
        end
        table.sort(self.playerData[i],function(a, b) 
            return a.playerInfo.personCE > b.playerInfo.personCE
        end)
        for j,k in ipairs(self.playerData[i]) do
            local tempObj = allianceListObj.playerList[j]
            local item2 = {}
            if not tempObj then
                local go1 = GameObject.Instantiate(DesertStormPlayerObj,PlayerList)
                Common_Util.SetActive(go1,true)
                local trans = go1.transform
                item2 = Common_Util.GetComponent(trans,typeof(ScrollRectItem),"")
                tempObj = {}
                tempObj.item = item2
                tempObj.rect = Common_Util.GetComponent(trans,typeof(RectTransform),"")
            else
                item2 = tempObj.item
                Common_Util.SetActive(item2,true)
            end
            local applicationObj = item2:Get("applicationObj")
            Common_Util.SetActive(applicationObj,#k.timeHope > 0)
            local PlayerHead = item2:Get("PlayerHead")
            local HeadTran = face_item:CFaceItem():Init(PlayerHead, function(p)
                if not p then
                    return
                end
            end, 1)
            local faceStr = k.playerInfo.faceId
            if k.playerInfo.faceStr and not string.IsNullOrEmpty(k.playerInfo.faceStr) then
                faceStr = k.playerInfo.faceStr
            end
            HeadTran:SetFaceInfo(faceStr,function()
                local mgr_personalInfo = require "mgr_personalInfo"
                mgr_personalInfo.ShowRoleInfoView(k.playerInfo.roleId)
            end)
            HeadTran:SetFrameID(k.playerInfo.frameID, true)
            HeadTran:SetActorLvText(true,k.playerInfo.level,true)
            tempObj.headObj = HeadTran
            tempObj.playerIndex = k.playerInfo.roleId
            local PlayerName = item2:Get("PlayerName")
            PlayerName.text = k.playerInfo.strName
            local PowerText = item2:Get("PowerText")
            PowerText.text = util.NumberWithUnit2(k.playerInfo.personCE)
            local battleToggle = item2:Get("battleToggle")
            local replacementToggle = item2:Get("replacementToggle")
            battleToggle.onValueChanged:RemoveAllListeners()
            replacementToggle.onValueChanged:RemoveAllListeners()
            local NoBattle = item2:Get("NoBattle")
            local NoReplacement = item2:Get("NoReplacement")
            local selectTimeBg = item2:Get("selectTimeBg")
            local bgRect = item2:Get("Bg")
            local Layout = item2:Get("Layout")
            
            local battleMark = item2:Get("battleMark")
            local replacementMark = item2:Get("replacementMark")
            
            local hasSelectTime = #k.timeHope > 0
            Common_Util.SetActive(selectTimeBg,hasSelectTime)
            if hasSelectTime then
                Common_Util.SetSize(bgRect,0,fullSize,true)
                Layout.minHeight = 149
            else
                Common_Util.SetSize(bgRect,0,miniSize,true)
                Layout.minHeight = 125
            end
            battleToggle.interactable = isSignUpState == true
            replacementToggle.interactable = isSignUpState == true
            if isSignUpState then
                Common_Util.SetActive(NoBattle,false)
                Common_Util.SetActive(NoReplacement,false)
                Common_Util.SetActive(battleToggle,true)
                Common_Util.SetActive(replacementToggle,true)
                if k.isSignUp then
                    battleToggle.isOn = k.isBattle
                    replacementToggle.isOn = not k.isBattle
                else
                    battleToggle.isOn = false
                    replacementToggle.isOn = false
                end
            else
                if k.isSignUp then
                    if k.isBattle then
                        Common_Util.SetActive(NoReplacement,true)
                        Common_Util.SetActive(NoBattle,false)
                        battleToggle.isOn = true
                        Common_Util.SetActive(battleToggle,true)
                        Common_Util.SetActive(replacementToggle,false)
                    else
                        Common_Util.SetActive(NoReplacement,false)
                        Common_Util.SetActive(NoBattle,true)
                        replacementToggle.isOn = true
                        Common_Util.SetActive(battleToggle,false)
                        Common_Util.SetActive(replacementToggle,true)
                    end
                else
                    battleToggle.isOn = false
                    replacementToggle.isOn = false
                    Common_Util.SetActive(NoReplacement,true)
                    Common_Util.SetActive(NoBattle,true)
                    Common_Util.SetActive(battleToggle,false)
                    Common_Util.SetActive(replacementToggle,false)
                end

            end
            Common_Util.SetActive(battleMark,battleToggle.isOn)
            Common_Util.SetActive(replacementMark,replacementToggle.isOn)
            battleToggle.onValueChanged:AddListener(function(value)
                if value then
                    gw_storm_mgr.OnSetMember(k.playerInfo.roleId,true,true)
                else
                    local replaceValue = replacementToggle.isOn
                    if not replaceValue then
                        gw_storm_mgr.OnSetMember(k.playerInfo.roleId,false,false)
                    end
                end
            end)
            
            replacementToggle.onValueChanged:AddListener(function(value)
                if value then
                    gw_storm_mgr.OnSetMember(k.playerInfo.roleId,false,true)
                else
                    local battleValue = battleToggle.isOn
                    if not battleValue then
                        gw_storm_mgr.OnSetMember(k.playerInfo.roleId,false,false)
                    end
                end
            end)
            allianceListObj.playerList[j] = tempObj
        end
        allianceListObj.item = item
        self.createObj[i] = allianceListObj
        Toggle.onValueChanged:RemoveAllListeners()
        local cache = Toggle.isOn
        if not isUpdate then
            Common_Util.SetActive(PlayerList,i == 5)
            Toggle.isOn = i == 5
        else
            Common_Util.SetActive(PlayerList,selectTime ~= nil and showSelectPlayer == true or cache)
            Toggle.isOn = selectTime ~= nil and showSelectPlayer == true or cache
        end
        Toggle.onValueChanged:AddListener(function(value)
            Common_Util.SetActive(PlayerList,value)
        end)
        if Toggle.isOn and self.cacheIndex == i then
            self:OnShowHeaderObj(item,-1,true)
        end
    end
    for i = 0,2 do
        local value = selectTimePlayerCount[i + 1] or 0
        local objPath = string.format2("txt_PlayerCount_{%s1}",i)
        self[objPath].text = value
    end
    self.txt_AllPlayerCount_0.text = allPlayer
    self.txt_TeamAPlayerCount_0.text = allSelectPlayer[event_DesertStrom_define.EnDesertStrom_Team.enDesertStrom_Team_A]
    self:UpdateRoleTime()
end

function UIView:UpdateRoleTime()
    for i,v in pairs(self.playerData) do
        for j,k in ipairs(v) do
            local tempObj = self.createObj[i].playerList[j]
            if tempObj then
                local item2 = tempObj.item
                local dayTitle = item2:Get("dayTitle")
                dayTitle.text = self.isServerTime and lang.Get(1003491) or lang.Get(1003437)
                local dayTxt = item2:Get("dayTxt")
                local timeTxt = item2:Get("timeTxt")
                if self.selectTime then
                    dayTxt.text = self.isServerTime and self.timeList[self.selectTime].serverDay or self.timeList[self.selectTime].selfDay
                    timeTxt.text = self.isServerTime and self.timeList[self.selectTime].serverHour or self.timeList[self.selectTime].selfHour
                else
                    if k.timeHope[1] then
                        dayTxt.text = self.isServerTime and self.timeList[k.timeHope[1]].serverDay or self.timeList[k.timeHope[1]].selfDay
                        timeTxt.text = self.isServerTime and self.timeList[k.timeHope[1]].serverHour or self.timeList[k.timeHope[1]].selfHour
                    end
                end
            end
        end
    end
end

function UIView:OnSetTime(value)
    self.isServerTime = value
    if self.tog_changeTime_1.isOn ~= value then
        self.tog_changeTime_1.isOn = value
    end
    if self.tog_changeTime_2.isOn ~= value then
        self.tog_changeTime_2.isOn = value
    end
    local signUpInfo = self.data.activityData.OnGetSignUpInfo()
    local selectTime = signUpInfo.teamData[event_DesertStrom_define.EnDesertStrom_Team.enDesertStrom_Team_A].battleTimeIndex--battle_time_index
    if value then
        self.txt_TimeTips.text = lang.Get(1003491)
        self.txt_TimeTips_1.text = self.txt_TimeTips.text
        self.txt_TimeTips_2.text = self.txt_TimeTips.text
        self.txt_battleTimeTitle_0.text = self.txt_TimeTips.text
        self.txt_battleTimeTitle_1.text = self.txt_TimeTips.text
        self.txt_battleTimeTitle_2.text = self.txt_TimeTips.text
        self.txt_battleTime.text = string.format2("{%s1} {%s2}",self.timeList[selectTime].serverDay,self.timeList[selectTime].serverHour)
        self.txt_battleTime_0.text = string.format2("{%s1} {%s2}",self.timeList[1].serverDay,self.timeList[1].serverHour)
        self.txt_battleTime_1.text = string.format2("{%s1} {%s2}",self.timeList[2].serverDay,self.timeList[2].serverHour)
        self.txt_battleTime_2.text = string.format2("{%s1} {%s2}",self.timeList[3].serverDay,self.timeList[3].serverHour)
        self.txt_Time01.text = self.txt_battleTime_0.text
        self.txt_Time02.text = self.txt_battleTime_1.text
        self.txt_Time03.text = self.txt_battleTime_2.text
    else
        self.txt_TimeTips.text = lang.Get(1003437)
        self.txt_TimeTips_1.text = self.txt_TimeTips.text
        self.txt_TimeTips_2.text = self.txt_TimeTips.text
        self.txt_battleTimeTitle_0.text = self.txt_TimeTips.text
        self.txt_battleTimeTitle_1.text = self.txt_TimeTips.text
        self.txt_battleTimeTitle_2.text = self.txt_TimeTips.text
        self.txt_battleTime.text = string.format2("{%s1} {%s2}",self.timeList[selectTime].selfDay,self.timeList[selectTime].selfHour)
        self.txt_battleTime_0.text = string.format2("{%s1} {%s2}",self.timeList[1].selfDay,self.timeList[1].selfHour)
        self.txt_battleTime_1.text = string.format2("{%s1} {%s2}",self.timeList[2].selfDay,self.timeList[2].selfHour)
        self.txt_battleTime_2.text = string.format2("{%s1} {%s2}",self.timeList[3].selfDay,self.timeList[3].selfHour)
        self.txt_Time01.text = self.txt_battleTime_0.text
        self.txt_Time02.text = self.txt_battleTime_1.text
        self.txt_Time03.text = self.txt_battleTime_2.text
    end
    self:UpdateRoleTime()
end

function UIView:OnHideSelectTimePanel()
    Common_Util.SetActive(self.btn_darkMask,false)
end

function UIView:HideSelectTimeAndJumpToTarget()
    Common_Util.SetActive(self.btn_darkMask,false)
    local alliance_user_data = require "alliance_user_data"
    local selfRank = alliance_user_data.GetPlayerAuthority() --获取自己的阶级
    local allianceListObj = self.createObj[selfRank]
    if allianceListObj then
        local item = allianceListObj.item
        local Toggle = item:Get("Toggle")
        Toggle.isOn = true;
        local PlayerList = item:Get("PlayerList")
        Common_Util.SetActive(PlayerList,true)
        local moveOffset = 0
        local playerIndex = player_mgr.GetPlayerRoleID()
        local selfItem
        for i = 5,selfRank + 1,-1 do --查找自己之上的所有阶级
            local obj = self.createObj[i]
            if obj then
                local rect = obj.rect
                local height = rect.sizeDelta.y
                moveOffset = moveOffset + height
            end
        end
        for j,k in ipairs(self.playerData[selfRank]) do
            local tempObj = allianceListObj.playerList[j]
            if tempObj then
                if playerIndex ~= tempObj.playerIndex then
                    local height = tempObj.rect.sizeDelta.y
                    moveOffset = moveOffset + height
                elseif playerIndex == tempObj.playerIndex then --找到自己，则立即退出循环
                    selfItem = tempObj
                    break
                end
            end
        end
        moveOffset = moveOffset - self.sr_ScrollView.viewport.rect.height / 2 --确保居中
        if self.twincleTimer then
            util.RemoveDelayCall(self.twincleTimer)
            self.twincleTimer = nil
        end
        self.twincleTimer = util.DelayCallOnce(0.1,function() --延时0.1秒触发
            local maxOffset = self.sr_ScrollView.content.rect.height - self.sr_ScrollView.viewport.rect.height
            if moveOffset > maxOffset then
                moveOffset = maxOffset
            end
            if selfItem then
                local item2 = selfItem.item
                self.sr_ScrollView.content.anchoredPosition = Vector2(self.sr_ScrollView.content.anchoredPosition.x,moveOffset)
                local Anim = item2:Get("Anim")
                Anim:Play()
            end
        end)

    end
end

function UIView:OnShowHelpTips()
    Common_Util.SetActive(self.btn_mask,true)
    Common_Util.SetActive(self.ator_HelpTips,true)
    self.ator_HelpTips:Play("ShowDesertStormSelectPlayerBubble")
end

function UIView:OnHideHelpTips()
    self.ator_HelpTips:Play("HideDesertStormSelectPlayerBubble")
    if self.maskTimer then
        Common_Util.SetActive(self.ator_DropDownBg,false)
        Common_Util.SetActive(self.ator_HelpTips,false)
        util.RemoveDelayCall(self.maskTimer)
        self.maskTimer = nil
    end
    self.maskTimer = util.DelayCallOnce(0.1,function()
        if self.maskTimer then
            util.RemoveDelayCall(self.maskTimer)
            self.maskTimer = nil
        end
        Common_Util.SetActive(self.ator_HelpTips,false)
        Common_Util.SetActive(self.btn_mask,false)
    end)
end

function UIView:OnShowDropDown()
    Common_Util.SetActive(self.btn_mask,true)
    local activityState = self.data.activityData.GetActivityState()
    local isSignUpState = activityState == event_DesertStrom_define.EnDesertStrom_ActivityType.enDesertStrom_ActivityType_SignUp
    if isSignUpState then
        Common_Util.SetActive(self.ator_DropDownBg,true)
        self.ator_DropDownBg:Play("ShowDesertStormSelectPlayerBubble")
    else
        Common_Util.SetActive(self.ator_DropDownPlayerBg,true)
        self.ator_DropDownPlayerBg:Play("ShowDesertStormSelectPlayerBubble")
    end

end

function UIView:OnHideDropDown()
    self.ator_DropDownBg:Play("HideDesertStormSelectPlayerBubble")
    if self.maskTimer then
        Common_Util.SetActive(self.ator_DropDownBg,false)
        Common_Util.SetActive(self.ator_HelpTips,false)
        util.RemoveDelayCall(self.maskTimer)
        self.maskTimer = nil
    end
    self.maskTimer = util.DelayCallOnce(0.1,function()
        if self.maskTimer then
            util.RemoveDelayCall(self.maskTimer)
            self.maskTimer = nil
        end
        Common_Util.SetActive(self.ator_DropDownBg,false)
        Common_Util.SetActive(self.btn_mask,false)
    end)
end

function UIView:OnShowSelectTimePanel()
    Common_Util.SetActive(self.btn_darkMask,true)
end

---@public function 获取UI世界坐标
---@param rectTransform table UI
---@param index number UI四个角的索引
function UIView:GetUIWorldPos(rectTransform,index)
    local worldCorners = Array.CreateInstance(typeof(Vector3),4)
    rectTransform:GetWorldCorners(worldCorners)
    return worldCorners[index].y
end
--endregion

--region View Static 
local CUIView = class(ui_base, nil, UIView)

function Show(data)
    if window == nil then
        window = CUIView()
        window._NAME = _NAME

        if data and type(data) == "table" then
            local tempPath = data.uiPath or ui_path
            local tempParent = data.uiParent or nil
			window:LoadUIResource(tempPath, nil, tempParent, nil, nil, false)
        else
			window:LoadUIResource(ui_path, nil, nil, nil,nil, false)
        end
    end
    window:Show()
    return window
end

function Close(data)
    if window ~= nil then
        window:Close(data)
        window = nil
    end
end

function Hide()
    if window ~= nil then
        window:Hide()
    end
end
--endregion
