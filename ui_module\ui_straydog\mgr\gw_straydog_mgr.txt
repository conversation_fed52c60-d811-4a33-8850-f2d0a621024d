---
--- Generated by <PERSON><PERSON><PERSON>(https://github.com/EmmyLua)
--- Created by chenwen.
--- DateTime: 2025/8/25
---

local require            = require
local table              = table
local game_scheme        = require "game_scheme"
local event              = require "event"
local GWConst            = require "gw_const"
local GWHomeMgr          = GWHomeMgr
local reward_mgr         = require "reward_mgr"
local gw_ed              = require "gw_ed"
local log                = require "log"
local ui_window_mgr      = require "ui_window_mgr"
local event_StrayDog_define = require "event_StrayDog_define"
local red_const = require "red_const"
local red_system = require "red_system"
local gw_home_building_data = require "gw_home_building_data"
local pairs = pairs

module("gw_straydog_mgr")

local M = {}
M.HouseData = {}
M.RechargeIdList = {}
M.ActivityContentIdList = {}

function Init()
    event.Register(event.USER_DATA_RESET, ResetData)
    event.Register(event.REWARD_RESULT_NEW_CLOSE, RewardResultClose)
    event.Register(event_StrayDog_define.TMSG_STRAY_DOG_GETINFO_RSP, GetInfoRsp)
    event.Register(event_StrayDog_define.TMSG_STRAY_DOG_RECEIVEAWARD_RSP, GetAwardRsp)
    event.Register(event.FIRST_LOGIN_CREATE_DATA_FINISH, LoginFinish)
    event.Register(gw_ed.GW_HOME_EVENT_PASS_LEVEL, BuildEventComplete)
    gw_ed.mgr:Register(gw_ed.GW_HOME_BUILDING_UPGRADE, BuildingUpgrade)
    red_system.RegisterRedFunc(red_const.Enum.DogHouseReward, GetRewardRedCount)

    --常量表配置的是ActivityContent的ID列表，获取礼包ID列表
    local constConfig = game_scheme:InitBattleProp_0(8410)
    if constConfig and constConfig.szParam and constConfig.szParam.data then
        for i = 0, constConfig.szParam.count - 1 do
            local activityContentCfg = game_scheme:ActivityContent_0(constConfig.szParam.data[i])
            if activityContentCfg and activityContentCfg.rechargeID and activityContentCfg.rechargeID.count > 0 then
                table.insert(M.ActivityContentIdList, constConfig.szParam.data[i])
                table.insert(M.RechargeIdList, activityContentCfg.rechargeID.data[0])
            end
        end
    end

    --地格事件ID
    M.BuildEventId = 0
    local functionConfig = game_scheme:FunctionOpen_0(2000)
    if functionConfig and functionConfig.EventID and functionConfig.EventID.count >= 2 then
        M.BuildEventId = functionConfig.EventID.data[1]
    end
    
    ResetData()
end 

function ResetData()
    M.HouseData.Level = 0
    M.HouseData.IsMaxLevel = false
    M.HouseData.RewardIdList = {}
end

function LoginFinish()
    local net_StrayDog_module = require "net_StrayDog_module"
    net_StrayDog_module.MSG_STRAY_DOG_GETINFO_REQ()
end

function GetInfoRsp(eventName, msg)
    if msg.errorcode and msg.errorcode > 0 then
        return
    end

    UpdateHouseLevel()
    --狗屋下次寻宝时间
    M.HouseData.RefreshTime = msg.refreshTm
    --狗屋奖励列表
    if msg.awards then
        M.HouseData.RewardIdList = msg.awards
    end
    --测试数据
    --if M.HouseData.Level > 0 and #M.HouseData.RewardIdList == 0 then
    --    M.HouseData.RewardIdList = {701011, 701012}
    --end

    event.Unregister(event.REWARD_RESULT_NEW_CLOSE, RewardResultClose)
    local gw_recharge_mgr = require "gw_recharge_mgr"
    --没到最高等级，当前等级的礼包没买过需要监听获得奖励关闭事件触发引导
    if not M.HouseData.IsMaxLevel and gw_recharge_mgr.GetRechargeBuyCount(GetRechargeId()) == 0 then
        event.Register(event.REWARD_RESULT_NEW_CLOSE, RewardResultClose)
    end
    
    event.Unregister(event.GW_REFRESH_RECHARGE_GOODS, RechargeRefresh)
    --没买过礼包监听充值事件，买了第一个礼包时派发事件通知礼包激活
    if not HasBuyFirstPackage() then
        event.Register(event.GW_REFRESH_RECHARGE_GOODS, RechargeRefresh)
    end
    
    event.Trigger(event.STRAY_DOG_INFO_UPDATE)
    red_system.TriggerRed(red_const.Enum.DogHouseReward)
end

function GetAwardRsp(eventName, msg)
    if msg.errorcode and msg.errorcode > 0 then
        return
    end

    if not msg.awards then
        return
    end
    
    local reportMsg = {
        TreasureID = table.concat(msg.awards, "#")
    }
    event.EventReport("DogTreasure", reportMsg)

    local rewardData = reward_mgr.GetRewardGoodsList2(msg.awards)
    local showData = {
        [1] = {
            dataList = rewardData
        }
    }
    local ui_reward_result = require "ui_reward_result_new"
    ui_reward_result.SetInputParam(showData)			--设置数据
    ui_window_mgr:ShowModule("ui_reward_result_new")
end

function BuildingUpgrade(_, data)
    if not data then
        return
    end
    local buildingType = GWHomeMgr.buildingData.GetBuildingTypeByBuildingId(data.nBuildingID)
    if buildingType == GWConst.enBuildingType.enBuildingType_DogHouse then
        UpdateHouseLevel()
    end
end

function BuildEventComplete()
    --完成对应的地格事件打开礼包购买界面
    if GWHomeMgr.chapterData and GWHomeMgr.chapterData.CheckJustPassEvent(M.BuildEventId) then
        local net_module_open = require "net_module_open"
        local moduleOpenPro_pb = require "moduleOpenPro_pb"
        if net_module_open.CheckModuleOpen(moduleOpenPro_pb.emModuleID_StrayDog) then
            gw_ed.mgr:Unregister(gw_ed.GW_HOME_EVENT_UPDATE, BuildEventComplete)
            ui_window_mgr:ShowModule("ui_straydog_buy", nil, nil, { type = 1})
        end
    end
end

function RewardResultClose()
    local gw_recharge_mgr = require "gw_recharge_mgr"
    --如果获得奖励界面关闭后礼包已经购买过，则关闭界面并引导建造狗屋
    if gw_recharge_mgr.GetRechargeBuyCount(GetRechargeId()) > 0 then
        ui_window_mgr:CloseAll()
        local buildType = GWConst.enBuildingType.enBuildingType_DogHouse
        local buildingData = GWHomeMgr.buildingData.GetBuildingDataByBuildingType(buildType)
        --跳转建造或者升级狗屋
        local module_jumping = require "module_jumping"
        if buildingData then
            module_jumping.Jump("slg_aag", buildType .. "#2")
        else
            module_jumping.Jump("slg_aaf", buildType)
        end
        
        --最后一级的礼包买了注销监听
        if M.HouseData.Level >= #M.RechargeIdList - 1 then
            event.Unregister(event.REWARD_RESULT_NEW_CLOSE, RewardResultClose)
        end
    end
end

function UpdateHouseLevel()
    if GWHomeMgr.buildingData then
        local buildingData = GWHomeMgr.buildingData.GetBuildingDataByBuildingType(GWConst.enBuildingType.enBuildingType_DogHouse)
        if buildingData then
            M.HouseData.Level = buildingData.nLevel
            local nextLvCfg = game_scheme:Building_0(buildingData.nBuildingID, buildingData.nLevel + 1)
            M.HouseData.IsMaxLevel = not nextLvCfg
        end
    end
end

function RechargeRefresh()
    if HasBuyFirstPackage() then
        event.Unregister(event.GW_REFRESH_RECHARGE_GOODS, RechargeRefresh)
        event.Trigger(event.STRAY_DOG_PACKAGE_ACTIVE)
    end
end

function GetRechargeId()
    if not M.HouseData.Level and #M.RechargeIdList > 0 then
        return M.RechargeIdList[1]
    end
    if #M.RechargeIdList > M.HouseData.Level then
        return M.RechargeIdList[M.HouseData.Level + 1]
    end
    return 0
end

function GetActivityContentId()
    if not M.HouseData.Level and #M.ActivityContentIdList > 0 then
        return M.ActivityContentIdList[1]
    end
    if #M.ActivityContentIdList > M.HouseData.Level then
        return M.ActivityContentIdList[M.HouseData.Level + 1]
    end
    return 0
end

function HasBuyFirstPackage()
    if #M.RechargeIdList > 0 then
        local gw_recharge_mgr = require "gw_recharge_mgr"
        return gw_recharge_mgr.GetRechargeBuyCount(M.RechargeIdList[1]) > 0
    end
    return false
end

function GetRewardRedCount()
    local rewardIdList = M.HouseData.RewardIdList
    if rewardIdList then
        return #rewardIdList
    end
    return 0
end

function GetAwardItemCount()
    local rewardIdList = M.HouseData.RewardIdList
    if rewardIdList then
        local itemList = reward_mgr.GetRewardGoodsList2(M.HouseData.RewardIdList)
        if (itemList) then
            return #itemList
        end
    end
    return 0
end

function HasAward()
    if M.HouseData then
        local rewardIdList = M.HouseData.RewardIdList
        return rewardIdList and #rewardIdList > 0
    end
    return false
end

function GetHouseData()
    return M.HouseData
end 

function GetLotteryBoxData()
        if not M.LotteryBoxData then
        M.LotteryBoxData = {}
        for i = 1, #M.RechargeIdList do
            local buildId = gw_home_building_data.GetBuildingIdByBuildingType(GWConst.enBuildingType.enBuildingType_DogHouse)
            local buildingCfg = game_scheme:Building_0(buildId, i)
            if buildingCfg then
                local buildingFunctionCfg = game_scheme:BuildingFunction_0(buildingCfg.buildingFunction)
                if buildingFunctionCfg then
                    local rewardList = {}
                    local lotteryBoxCfg = game_scheme:LotteryBox_0(buildingFunctionCfg.param4)
                    if lotteryBoxCfg then
                        --奖池权重总和
                        local totalWeight = 0
                        for j = 0, lotteryBoxCfg.arrProbability.count - 1 do
                            totalWeight = totalWeight + lotteryBoxCfg.arrProbability.data[j]
                        end
                        for j = 1, 15 do
                            local rewardIds = lotteryBoxCfg["arrRandomRewardID" .. j]
                            if rewardIds and rewardIds.count > 0 then
                                --每个奖励组的权重
                                local rewardWeight = lotteryBoxCfg.arrProbability.data[j - 1] or 0
                                local goodsList = reward_mgr.GetRewardGoodsList2(rewardIds.data)
                                for k, v in pairs(goodsList) do
                                    --每个道具的概率
                                    v.probability = rewardWeight / totalWeight / #goodsList
                                    if v.probability > 0 then
                                        table.insert(rewardList, v)
                                    end
                                end
                            end
                        end
                    end
                    local boxData = {
                        level = i,
                        langId = buildingFunctionCfg.name,
                        reward = rewardList,
                    }

                    table.insert(M.LotteryBoxData, boxData)
                end
            end
        end
    end
    return M.LotteryBoxData
end