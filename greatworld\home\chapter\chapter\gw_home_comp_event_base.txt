---
--- Generated by <PERSON><PERSON><PERSON>(https://github.com/Emmy<PERSON>ua)
--- Created by hans<PERSON><PERSON>.
--- DateTime: 2024/9/2 14:55
--- Des: 事件基类

local require = require
local pairs = pairs
local newclass = newclass
local UIUtil = CS.Common_Util.UIUtil
local string = string
local math = math
local net_module_open = require "net_module_open"
local gw_main_mini_game_data = require "gw_main_mini_game_data"
local event = require "event"
local gw_home_building_data = require "gw_home_building_data"
local gw_home_effect_mgr = require "gw_home_effect_mgr"
local gw_ed = require "gw_ed"
local util = require "util"
local gw_hero_mgr = require "gw_hero_mgr"
local ui_pointing_target = require "ui_pointing_target"
local lang = require "lang"
local puzzlegame_mgr = require "puzzlegame_mgr"
local flow_text = require "flow_text"
local const = require "const"
local windowMgr = require "ui_window_mgr"
local net_city_module = require "net_city_module"
local ui_window_mgr = require "ui_window_mgr"
local unit_base_object = require "unit_base_object"
local game_scheme = require "game_scheme"
local GWG = GWG
local GWConst = require "gw_const"
local gw_home_grid_data = require "gw_home_grid_data"
local gw_home_common_util = require "gw_home_common_util"
-- - - - - - - - - - - - - - - - - - - - - 分割线 - - - - - - - - - - - - - - - - - - - -
---@class GWEventBase : unit_base 事件基类
---@field __base UnitBaseObject
module("gw_home_comp_event_base")
local GWEventObject = newclass("gw_home_comp_event_base", unit_base_object)
local configMap = {
    [270] = {
        [0] = 180, -- 270-0，返回2
        [180] = 90        -- 270-180，返回3
    },
    [0] = {
        [270] = 0, -- 0-270，返回1
        [90] = 90         -- 0-90，返回4
    },
    [180] = {
        [270] = 270, -- 0-270，返回1
        [90] = 180         -- 0-90，返回4
    },
    [90] = {
        [180] = 0, -- 0-270，返回1
        [0] = 270         -- 0-90，返回4
    },
}
local normalConfigMap = {
    [270] = 90,
    [0] = 0,
    [180] = 0,
    [90] = 90
}
--事件气泡显示类型
local EventBubbleTypeByResType = {
    [0] = GWConst.EHomeBubbleEntityType.None,
    [1] = GWConst.EHomeBubbleEntityType.EventGesture,
    [2] = GWConst.EHomeBubbleEntityType.Event,
    [3] = GWConst.EHomeBubbleEntityType.EventGesture,
    [4] = GWConst.EHomeBubbleEntityType.EventUAV,
}
--- 构造器
function GWEventObject:ctor()
    self:ClearData()
    unit_base_object.ctor(self)
end

--- @public 设置数据 （现必须基成设置数据）
---@see override
function GWEventObject:InitData(cityMapId, eventId)
    self.mapId = cityMapId
    self.eventId = eventId
    self:InitCfgData(cityMapId, eventId)
    self:InitLoadRes()
    self:InstantiateModelAsync(self.res, GWG.GWHomeNode.eventNode())
end

--- @public 初始化配置数据
function GWEventObject:InitCfgData(cityMapId, eventId)
    local mapCfg = game_scheme:BuildMaincityMap_0(cityMapId)
    local eventCfg = game_scheme:BuildEvent_0(eventId)

    if not mapCfg then
        GWG.GWAdmin.SwitchUtility.Error("GWEventObject mapCfg = nil", cityMapId)
        return false
    end

    if not eventCfg then
        GWG.GWAdmin.SwitchUtility.Error("GWEventObject eventCfg = nil", eventId)
        return false
    end

    self.mapCfg = mapCfg
    self.eventCfg = eventCfg
    return true
end

function GWEventObject:InitLoadRes()
    --通过前一格和后一格的角度确定要加载的模型
    if not self.eventCfg then
        return
    end
    local preMapId = 0
    local nextMapId = 0
    local nextEventCfg = game_scheme:BuildEvent_1(self.eventCfg.EventID)
    if self.eventCfg.EventID ~= 0 then
        preMapId = GWG.GWHomeMgr.chapterData.GetMainCityMapId(self.eventCfg.EventID)
    end
    if nextEventCfg then
        nextMapId = GWG.GWHomeMgr.chapterData.GetMainCityMapId(nextEventCfg.EventID)
    end
    local preCityMapCfg = game_scheme:BuildMaincityMap_0(preMapId)
    local nextCityMapCfg = game_scheme:BuildMaincityMap_0(nextMapId)

    if preCityMapCfg and nextCityMapCfg then
        self.res = "art/greatworld/home/<USER>/event/homeeventturn.prefab"
        self.angele = self:GetAngleResourceRes(preCityMapCfg.angel, nextCityMapCfg.angel)
    elseif nextCityMapCfg then
        self.res = "art/greatworld/home/<USER>/event/homeeventbase.prefab"
        self.angele = self:GetNormalResourceRes(nextCityMapCfg.angel)
    elseif preCityMapCfg then
        self.res = "art/greatworld/home/<USER>/event/homeeventbase.prefab"
        self.angele = self:GetNormalResourceRes(preCityMapCfg.angel)
    else
        GWG.GWAdmin.SwitchUtility.Error(" GWEventObject:InitLoadRes preMapId = " .. preMapId .. " nextMapId = " .. nextMapId)
    end
    if self.eventCfg.ArrowAngle and util.get_len(self.eventCfg.ArrowAngle.data) == 2 then
        self.res = "art/greatworld/home/<USER>/event/homeeventarrow.prefab"
        local angle1 = self.eventCfg.ArrowAngle.data[1]
        if angle1 == 90 or angle1 == 270 then
            angle1 = angle1 + 180
        end
        self.angele = angle1
    end
end
function GWEventObject:GetAngleResourceRes(angle1, angle2)
    if not angle1 or not angle2 then
        GWG.GWAdmin.SwitchUtility.Error("GWEventObject:GetResourceRes angle1 = " .. angle1 .. " angle2 = " .. angle2)
    end
    local difference = math.abs(angle1 - angle2)
    if difference == 90 or difference == 270 then
        if configMap[angle1] and configMap[angle1][angle2] then
            return configMap[angle1][angle2]
        end
    end
    self.res = "art/greatworld/home/<USER>/event/homeeventbase.prefab"
    return self:GetNormalResourceRes(angle1)
end
function GWEventObject:GetNormalResourceRes(angle)
    return normalConfigMap[angle]
end

--- @public 基类方法实例化模型
---@see override
function GWEventObject:InstantiateModelAsync(path, parent)
    unit_base_object.InstantiateModelAsync(self, path, parent)
end

--- 实例化成功 （现必须基成设置名字和组件Id）
---@see override
function GWEventObject:InstantiateSuccess(_obj)
    _obj.tag = GWG.GWConst.EHomeBuildEntityTagType.HomeBuildEvent
    self.defaultY = GWG.GWConst.HomeMapDefaultY

    unit_base_object.InstantiateSuccess(self, _obj)
    self.sortingGroup = UIUtil.GetComponent(_obj.transform, "SortingGroup")
    self.node = UIUtil.GetTrans(self.transform, "node")
    local box = self.node:GetChild(0)
    self.ground = UIUtil.GetComponent(self.node, "Transform", "ground")
    if box then
        box.name = "build_" .. self.compId
    end
    UIUtil.SetRotation(self.ground, 90, 0, self.angele)
    self.size = self.eventCfg.tile
    self:SetGridPos(self.mapCfg.x, self.mapCfg.y)
    local x, y, z = gw_home_grid_data.GetPosByGridXY(self.mapCfg.x, self.mapCfg.y)
    self:SetPosition(x, y, z)
    self:SetScale(2, true)
    self:IsCurrentEvent(nil, true)
    self.refreshUpgrade = function(eventName, openData)
        self:RefreshEventLockHud(true)
        if openData and openData.nLevel == 1 then
            local reconnaissanceId = gw_home_building_data.GetBuildingIdByBuildingType(GWConst.enBuildingType.enBuildingType_Reconnaissance)
            if reconnaissanceId == openData.nBuildingID then
                self:CreateXyxBubble()
            end
        end
    end
    self.OnInputEvent = function(_, action, p1, p2, p3, p4)
        if action == gw_ed.Input.EventBegin then
            --self:DisposeArrowHud()
        end
    end
    gw_ed.mgr:Register(gw_ed.GW_HOME_BUILDING_UPGRADE, self.refreshUpgrade)
    gw_ed.mgr:Register(gw_ed.GW_INPUT_EVENT, self.OnInputEvent)
    self.OnUpdateModuleOpen = function()
        local curEventId = GWG.GWHomeMgr.chapterData.GetCurrentEventPoint()
        if self.eventId and self.eventId >= curEventId then
            self:DestroyModel()
            self:SetEventModel()    
        end        
    end
    if self.eventCfg and self.eventCfg.moduleOpen and self.eventCfg.moduleOpen ~= 0 then
        event.Register(event.UPDATE_MODULE_OPEN,self.OnUpdateModuleOpen)
    end
end

---@public 设置格子
function GWEventObject:SetGridPos(x, y)
    self:SetLayer(x, y)
    self:ClearGridPos(x, y)
    --gw_home_grid_data.SetGridStateByBuilding(x, y, self.compId, self.size)
    self.curGridX = x
    self.curGridY = y
end

---@public 清楚格子坐标
function GWEventObject:ClearGridPos(x, y)
    if self.curGridX and (self.curGridX ~= x or self.curGridY ~= y) then
        --先清掉原来的数据
        --gw_home_grid_data.SetGridStateByBuilding(self.curGridX, self.curGridY, 0, self.size)
    end
end

---@public 点击
function GWEventObject:OnClickGrid(names, gridX, gridY)
    local force_guide_system = require "force_guide_system"
    local stepId = force_guide_system.GetCurStep()
    if stepId and stepId == 892 then
        local force_guide_event = require "force_guide_event"
        force_guide_system.TriComEvent(force_guide_event.cEventNormalEnterEvent2)
    end
    if self.isLoaded and not GWG.GWHomeMgr.chapterData.GetMoveActionIng() then
        if not self.eventCfg then
            GWG.GWAdmin.SwitchUtility.Error("GWEventObject eventCfg = nil", self.eventId)
            return
        end
        if not self.eventCfg.PreEvent then
            GWG.GWAdmin.SwitchUtility.Error("GWEventObject eventCfg.PreEvent = nil", self.eventId)
            return
        end
        local curEventId = GWG.GWHomeMgr.chapterData.GetCurrentEventPoint()
        if self.eventCfg.EventID == curEventId then

            self:StartEvent()
        elseif self.eventCfg.EventID > curEventId then
            self:OpenWindowEvent()
        end
        local curCompleteEventCfg = game_scheme:BuildEvent_0(curEventId)
        if not curCompleteEventCfg then
            return
        end
        local playerEventId = GWG.GWHomeMgr.chapterData.GetPlayerEventId()
        if self.eventCfg.EventID == curCompleteEventCfg.PreEvent and self.eventCfg.EventID ~= playerEventId then
            GWG.GWHomeMgr.chapterData.RefreshUpdateCheckEvent()
        end
    end
end
---@public 主线关卡点击事件
local function OnBtn_FightClicked(level, eventId)
    local equipment_mgr = require "equipment_mgr"
    if equipment_mgr.CheckEquipIsFull() == true then
        equipment_mgr.ShowEquipFullTip()
        return
    end
    local hangLevel = level
    local battle_switch_manager = require "battle_switch_manager"
    local common_new_pb = require "common_new_pb"
    local isBattling = battle_switch_manager.IsBattling(common_new_pb.GameGoal)
    local mapCfg = game_scheme:HookLevel_0(hangLevel)
    --[[    if not isBattling and mapCfg and (mapCfg.SkipBattle == 1 or mapCfg.SkipBattle == 2) then
            --遇到Boss关卡，不能跳过的，需要先打开ui_hook_boss界面
            --local net_illusiontower_module = require "net_illusiontower_module"
            --local illusiontower_pb = require "illusiontower_pb"
            --net_illusiontower_module.SendBattlePlayback(illusiontower_pb.enBattlePlayback_Idle,hangLevel)
            local story_mgr = require "story_mgr"
            local isCanExcute = story_mgr.PlayStory(3, hangLevel)
            if isCanExcute then
                story_mgr.AddCompleteCallback(function()
                    local ui_hook_boss = require "ui_hook_boss"
                    ui_hook_boss.setHookLevel(hangLevel, true)
                    windowMgr:ShowModule("ui_hook_boss")
                end)
            else
                local ui_hook_boss = require "ui_hook_boss"
                ui_hook_boss.setHookLevel(hangLevel, true)
                windowMgr:ShowModule("ui_hook_boss")
            end
    
            return
        end]]
    if const.OPEN_NEW_HOOK_SCENE then
        local new_hook_scene = require("new_hook_scene")
        new_hook_scene.OnFightClicked(--[[hangLevel]])
    else
        local laymain_top_scene = require("laymain_top_scene")
        laymain_top_scene.OnFightClicked(--[[hangLevel]])
    end
    --发送校验 Topic 数值
    net_city_module.MSG_CITY_CHECK_AREA_EVENT_REQ(eventId)
end
---@public 小游戏关卡点击事件
local function OnBtn_miniGameClicked(level)
    --local minilevelCfg = puzzlegame_mgr.GetLevelIdTinyGameLevelInfo(level)
    --if not minilevelCfg then
    --    GWG.GWAdmin.SwitchUtility.Error("OnBtn_miniGameClicked minilevelCfg = nil", level)
    --    return
    --end
    ----处理点击关卡章节页面的引导特效
    --if minilevelCfg.iPreLevel == 0 or puzzlegame_mgr.getIsFinishByLevelId(minilevelCfg.iPreLevel, minilevelCfg.LevelType) then
    --    if ui_window_mgr:IsModuleShown("ui_pointing_target") then
    --        ui_pointing_target.CloseWithParam()
    --    end
    --    puzzlegame_mgr.OpenMiniGame(minilevelCfg.ID)
    --else
    --    flow_text.Add(lang.Get(2718))
    --end
end
---@public 奖励点击事件
local function OnBtn_RewardClicked(eventId)
    if not eventId then
        GWG.GWAdmin.SwitchUtility.Error("OnBtn_RewardClicked eventId = nil")
        return
    end
    net_city_module.MSG_CITY_GET_EVENT_REWARD_REQ(eventId)
end
--- @public 开始执行事件
function GWEventObject:OpenWindowEvent()
    if not self:CheckEventConfig() then
        return
    end
    --开始主线任务 ,开始奖励 主线和奖励 打开详情 其他不操作
    --if self.eventCfg.type == GWConst.BuildEventType.MainQuest or self.eventCfg.type == GWConst.BuildEventType.Reward then
    if self.eventCfg.type == GWConst.BuildEventType.Reward then
        local screenPos = GWG.GWMgr.comp:GetCameraComponent():WorldToScreenPoint(self.transform.position)
        local data = { eventId = self.eventId, position = screenPos }
        ui_window_mgr:ShowModule("ui_bsreward_block", nil, nil, data)
        return
    end
    if self.modelComp then
        local screenPos = GWG.GWMgr.comp:GetCameraComponent():WorldToScreenPoint(self.transform.position)
        local data = { eventId = self.eventId, position = screenPos, isCreateXYXBubble = self.isCreateXYXBubble }
        ui_window_mgr:ShowModule("ui_bs_block", nil, nil, data)
        return
    end
end

--- @public 开始执行事件
function GWEventObject:StartEvent()
    if not self:CheckEventConfig() then
        return
    end
    local isUnlock, needId, needLevel = GWG.GWAdmin.GWHomeChapterUtil.GetBuildEventUnlock(self.eventId)
    if not isUnlock then
        GWG.GWAdmin.HomeCameraUtil.DoCameraToBuildMove(needId, needLevel, 100, true, nil, nil, 2)
        return
    end
    if self.eventCfg.type == GWConst.BuildEventType.MainQuest then
        ui_window_mgr:UnloadModule("ui_bs_operate")
        OnBtn_FightClicked(self.eventCfg.TypePara.data[0], self.eventId)
    elseif self.eventCfg.type == GWConst.BuildEventType.Reward then
        OnBtn_RewardClicked(self.eventId)
    elseif self.eventCfg.type == GWConst.BuildEventType.MiniGame then
        GWG.GWAdmin.SwitchUtility.Error("GWEventObject eventCfg.type = nil", self.eventCfg.type)
        --OnBtn_miniGameClicked(self.eventCfg.TypePara.data[0])
    else
        GWG.GWAdmin.SwitchUtility.Error("GWEventObject eventCfg.type = nil", self.eventCfg.type)
    end
    if self.eventCfg then
        local areaId = self.eventCfg.belong
        local event = require "event"
        event.EventReport("EventStart", { EventID = self.eventId, AreaID = areaId })
    end
end

--- @public 模型出现
function GWEventObject:ShowModel(isFirst)
    local playEffect = false
    if not self.modelComp then
        self:SetEventModel()
        playEffect = true
    end
    self.modelComp:ShowModel(playEffect)
    if not isFirst then
        return
    end
    local unforced_guide_const = require "unforced_guide_const"
    if self.eventId == unforced_guide_const.FirstForceGuideEventId then
        self.modelComp:AddLoadEvent(function()
            util.DelayCallOnce(0, function()
                local force_guide_system = require "force_guide_system"
                local force_guide_event = require "force_guide_event"
                local guideId = force_guide_system.GetCurGuide()
                if not guideId then
                    force_guide_system.TriEnterEvent(force_guide_event.tEventNormalEvent2Click)
                end
            end)
        end)
    end
end

--- @public 模型死亡
function GWEventObject:DeadModel(callBack)
    self.deadModeAction = true --正在执行死亡动画
    if not self.modelComp then
        self:SetEventModel()
    end
    if self.modelComp then
        self.modelComp:DeadModel(callBack, function()
            self:DestroyModel()
        end)
    end
    self:StopEffect(GWConst.HomeEffectType.EventCombat)
    self:DisposeFightHud()
end

--- @public 模型销毁
function GWEventObject:DestroyModel()
    if self.modelComp then
        GWG.GWAdmin.PushBSComponent(self.modelComp)
        self.modelComp = nil
    end
end
function GWEventObject:GetModelComp()
    return self.modelComp
end
function GWEventObject:SetModelVisible(show)
    if self.modelComp then
        self.modelComp:SetVisible(show)
    end
    if not show then
        self:DisposeBubble()
    end
end

--- @public 设置模型
function GWEventObject:SetEventModel()
    if self.modelComp then
        return
    end
    if self.eventCfg and self.eventCfg.TypePara then
        if self.eventCfg.type == GWConst.BuildEventType.Reward then
            local id = GWG.GWIdMgr:AllocComponentId()
            self.modelComp = GWG.GWAdmin.PopBSComponent(GWG.GWCompName.gw_home_comp_event_model, id)
            if self.modelComp then
                self.modelComp:InitData(self.eventId, self.mapId, GWG.GWHomeNode.otherNode(), self:GetEventResPath())
            else
                GWG.GWAdmin.SwitchUtility.Error("SetEventModel self.modelComp =  nil")
            end
        elseif self.eventCfg.type == GWConst.BuildEventType.MainQuest then
            local monsterRes = ""
            if string.empty(self:GetEventResPath()) then
                GWG.GWAdmin.SwitchUtility.Error("使用了模型表配置 请修改", "eventCfg.Id = ", self.eventId)
                local hookId = self.eventCfg.TypePara.data[0]
                local hookCfg = game_scheme:HookLevel_0(hookId)
                if not hookCfg then
                    GWG.GWAdmin.SwitchUtility.Error("SetEventModel hookCfg = nil", hookId)
                    return
                end
                local monsterTeamCfg = game_scheme:monsterTeam_0(hookCfg.ranksID.data[0])
                if not monsterTeamCfg then
                    GWG.GWAdmin.SwitchUtility.Error("SetEventModel monsterTeamCfg = nil", hookCfg.ranksID.data[0])
                    return
                end
                local monsterId = monsterTeamCfg.MonsterId.data[0]
                if monsterTeamCfg.BossNumber ~= 0 then
                    monsterId = monsterTeamCfg.MonsterId.data[monsterTeamCfg.BossNumber - 1]
                end
                local monsterModelId = gw_hero_mgr.ChangeHeroModel(monsterId, 1)
                local cfg_model = game_scheme:Modul_0(monsterModelId)
                if not cfg_model then
                    return
                end
                monsterRes = cfg_model.modelsimplePath
            else
                monsterRes = self:GetEventResPath()
            end
            if not string.empty(monsterRes) then
                local id = GWG.GWIdMgr:AllocComponentId()
                self.modelComp = GWG.GWAdmin.PopBSComponent(GWG.GWCompName.gw_home_comp_event_model, id)
                if self.modelComp then
                    self.modelComp:InitData(self.eventId, self.mapId, GWG.GWHomeNode.otherNode(), monsterRes)
                else
                    GWG.GWAdmin.SwitchUtility.Error("SetEventModel self.modelComp =  nil")
                end
            else
                GWG.GWAdmin.SwitchUtility.Error("SetEventModel monsterRes =  nil 策划配置错误", self.eventId)
            end
        end

    end
end

function GWEventObject:GetEventResPath()
    if self.eventCfg then
        if self.eventCfg.moduleOpen and self.eventCfg.moduleOpen ~= 0 then
            local isOpen = net_module_open.CheckModuleOpen(self.eventCfg.moduleOpen)
            if isOpen then
                return self.eventCfg.ResPath2
            end
        end
        return self.eventCfg.ResPath
    end
    return ""
end

function GWEventObject:GetEventResScale()
    if self.eventCfg then
        if self.eventCfg.moduleOpen and self.eventCfg.moduleOpen ~= 0 then
            local isOpen = net_module_open.CheckModuleOpen(self.eventCfg.moduleOpen)
            if isOpen then
                return self.eventCfg.ResScale2
            end
        end
        return self.eventCfg.ResScale
    end
    return 1
end

function GWEventObject:PlayEffectPlayerGrid(isShowEffect)
    local curEventId = GWG.GWHomeMgr.chapterData.GetCurrentEventPoint()
    local currentCfg = game_scheme:BuildEvent_0(curEventId)
    if isShowEffect then
        if currentCfg and currentCfg.PreEvent == self.eventId then
            local mapId = GWG.GWHomeMgr.chapterData.GetMainCityMapId(curEventId)
            if mapId == 0 then
                return
            end
            local nextMapCfg = game_scheme:BuildMaincityMap_0(mapId)
            if not nextMapCfg then
                GWG.GWAdmin.SwitchUtility.Error("GWEventObject nextMapCfg = nil", mapId)
                return
            end
            local angle = nextMapCfg.angel
            local posx, posy, posz = gw_home_grid_data.GetPosByGridXY(self.mapCfg.x, self.mapCfg.y)
            local pos = { x = posx, y = posy, z = posz }
            local effectRot = { x = 0, y = (180 + angle), z = 0 }
            local effectScale = { x = 2, y = 2, z = 2 }
            self:PlayEffect(GWConst.HomeEffectType.EventPlayerGrid, pos, effectRot, effectScale, GWG.GWHomeNode.effectNode())
        end
    end
end

---@public 是否是当前关卡 显示高亮
function GWEventObject:IsCurrentEvent(isHideEffect, isFirst)
    self:DisposeEffect()
    --创建气泡
    self:CreateBubbleHud(self.mapId)
    local curEventId = GWG.GWHomeMgr.chapterData.GetCurrentEventPoint()
    if self.eventId < curEventId then
        if not self.deadModeAction then
            self:DestroyModel()
        end
        self:PlayEffectPlayerGrid((not isHideEffect) and isFirst)
        return
    end
    if self.eventId == curEventId then
        --显示高亮
        self:ShowIsCurrentEffect()
        --显示模型
        self:ShowModel(isFirst)
        --不是奖励类型
        if self.eventCfg.type == GWConst.BuildEventType.MainQuest or
                self.eventCfg.type == GWConst.BuildEventType.MiniGame then
            --创建战斗Hud
            self:CreateFightHud()
        else
            self:CreateBubble()
        end
        --isFirst 后期优化后需要处理
        local state = gw_home_common_util.GetDefaultEventCameraOpenState()
        if isFirst and state then
            self:CreateArrowHud()
        end
    end
    local curDistance = self.eventId - curEventId + 1
    if self.eventCfg.display == 0 or (self.eventCfg.display > 0 and self.eventCfg.display >= curDistance) then
        if (self.eventCfg and self.eventCfg.type == GWConst.BuildEventType.Reward) then
            --创建气泡
            local maxLevel = gw_home_building_data.GetBuildingDataMaxLevelByType(GWConst.enBuildingType.enBuildingType_Reconnaissance)
            if maxLevel >= 1 then
                self:CreateBubble()
            end
        end
        --显示奖励模型
        self:SetEventModel()
    end
    self:CreateXyxBubble()
    self:RefreshEventLockHud()
end

function GWEventObject:CreateXyxBubble()
    local curEventId = GWG.GWHomeMgr.chapterData.GetCurrentEventPoint()
    if self.eventCfg.type == GWConst.BuildEventType.MainQuest and self.eventId ~= curEventId then
        local maxLevel = gw_home_building_data.GetBuildingDataMaxLevelByType(GWConst.enBuildingType.enBuildingType_Reconnaissance)
        --判断侦察机是否建造
        if maxLevel < 1 then
            return
        end
        local hookId = self.eventCfg.TypePara.data[0]
        self.isXyx = gw_main_mini_game_data.IsMiniGameUnlock(hookId, self.eventId)
        if self.isXyx then
            --显示奖励模型
            self:SetEventModel()
            --创建气泡
            self:DisposeBubble()
            self:CreateBubble(GWConst.EHomeBubbleEntityType.XYXEvent)
            self.isCreateXYXBubble = true
            local data = nil
            if self.eventCfg.bubblePos and self.eventCfg.bubblePos > 0 then
                data = { offsetData = { x = 0, y = self.eventCfg.bubblePos } }
            else
                local scale = self:GetEventResScale()
                data = { offsetData = { x = 0, y = (scale + 0.2) * 50 + 10 } }
            end
            if self.bubbleId then
                GWG.GWHomeMgr.bubbleMgr.BubbleUpdateEntity(self.bubbleId, data)
            end
        end
    end
end

function GWEventObject:RefreshEventGrid(isHideEffect)
    self:PlayEffectPlayerGrid(not isHideEffect)
end

function GWEventObject:RefreshData(isHideEffect)
    --如果还没加载 直接返回 实例化成功之后会再次调用
    if not self.isLoaded then
        return
    end
    self:IsCurrentEvent(isHideEffect)
end
function GWEventObject:ShowIsCurrentEffect()
    if not self.mapCfg then
        return
    end
    local posX, posY, posZ = gw_home_grid_data.GetPosByGridXY(self.mapCfg.x, self.mapCfg.y)
    local pos = { x = posX, y = posY, z = posZ }
    if self.eventCfg and self.eventCfg.type == GWConst.BuildEventType.Reward then
        self:PlayEffect(GWConst.HomeEffectType.EventRewardGrid)
        if self.eventCfg.ResType ~= 1 then
            self:PlayEffect(GWConst.HomeEffectType.EventRewardGridLight, pos, nil, nil, GWG.GWHomeNode.effectNode())
        end
    else
        self:PlayEffect(GWConst.HomeEffectType.EventCombatGrid)
        self:PlayEffect(GWConst.HomeEffectType.EventCombatGridLight, pos, nil, nil, GWG.GWHomeNode.effectNode())
    end
end
function GWEventObject:PlayBoxOpenEffect()
    self:PlayEffect(GWConst.HomeEffectType.EventBoxOpen)
end
function GWEventObject:StopBoxOpenEffect()
    self:StopEffect(GWConst.HomeEffectType.EventBoxOpen)
end
---@public 创建战斗Hud
function GWEventObject:CreateFightHud()
    --[[local compName = GWG.GWCompName.gw_home_comp_hud_fight
    if not self:GetComponent(compName) then
        local id, comp = GWG.GWAdmin.GWHomeHudUtil.InitMoveHudComponent(compName, self.transform)
        comp:SetOffsetScale(self.eventCfg.ResScale + 0.2)
        comp:SetOnClick(function()
            self:OnClickGrid()
        end)
        self:AddComponent(compName, comp)
    end]]
    self:DisposeFightHud()
    local clickFunc = function()
        self:OnClickGrid()
    end
    local isXyx = gw_main_mini_game_data.IsMiniGameUnlock(self.eventCfg.TypePara.data[0], self.eventId)
    if isXyx then
        self.fightBubble = GWG.GWHomeMgr.bubbleMgr.BindBubbleEntity(nil, GWConst.EHomeBubbleEntityType.XYXEvent, self.transform, clickFunc, nil);
    else
        self.fightBubble = GWG.GWHomeMgr.bubbleMgr.BindBubbleEntity(nil, GWConst.EHomeBubbleEntityType.EventBattle, self.transform, clickFunc, nil);
    end
    local data = nil
    if self.eventCfg.bubblePos and self.eventCfg.bubblePos > 0 then
        data = { offsetData = { x = 0, y = self.eventCfg.bubblePos } }
    else
        local scale = self:GetEventResScale()
        data = { offsetData = { x = 0, y = (scale + 0.2) * 50 + 10 } }
    end
    if self.fightBubble then
        GWG.GWHomeMgr.bubbleMgr.BubbleUpdateEntity(self.fightBubble, data)
    end
end
function GWEventObject:GetFightHud()
    local compName = GWG.GWCompName.gw_home_comp_hud_fight
    return self:GetComponent(compName)
end
function GWEventObject:DisposeFightHud()
    --[[local compName = GWG.GWCompName.gw_home_comp_hud_fight
    self:RemoveComponent(compName)]]
    if self.fightBubble then
        GWG.GWHomeMgr.bubbleMgr.DisposeBubbleEntity(self.fightBubble)
        self.fightBubble = nil
    end
end

---@public 创建箭头Hud
function GWEventObject:CreateArrowHud()
    self:AddLoadEvent(function()
        --[[local compName = GWG.GWCompName.gw_home_comp_hud_arrow
        if not self:GetComponent(compName) then
            local id, comp = GWG.GWAdmin.GWHomeHudUtil.InitMoveHudComponent(compName, self.transform)
            comp:SetOffsetScale(self.eventCfg.ResScale)
            self:AddComponent(compName, comp)
        end]]
        if self.arrowGuideId then
            local weak_guide_follow_mgr = require "weak_guide_follow_mgr"
            weak_guide_follow_mgr.DisposeFollowEntityById(self.arrowGuideId)
            self.arrowGuideId = nil
        end
        local weak_guide_follow_mgr = require "weak_guide_follow_mgr"
        local gw_home_chapter_data = require "gw_home_chapter_data"
        gw_home_chapter_data.SetLastArrowHud(self.eventId)
        self.arrowGuideId = weak_guide_follow_mgr.BindFollowObj({ self.transform.gameObject }, { [5] = 0, [6] = 0 })
        event.Trigger(event.TASK_UNFORCED_GUIDE)
    end)
    if self.delayCallTask then
        util.RemoveDelayCall(self.delayCallTask)
        self.delayCallTask = nil
    end
    self.delayCallTask = util.DelayCallOnce(20, function()
        if self.arrowGuideId then
            local weak_guide_follow_mgr = require "weak_guide_follow_mgr"
            weak_guide_follow_mgr.DisposeFollowEntityById(self.arrowGuideId)
            self.arrowGuideId = nil
        end
        event.Trigger(event.TASK_REFRESH_UNFORCED_GUIDE)
    end)
end
function GWEventObject:DisposeArrowHud()
    if self.arrowGuideId then
        local weak_guide_follow_mgr = require "weak_guide_follow_mgr"
        weak_guide_follow_mgr.DisposeFollowEntityById(self.arrowGuideId)
        self.arrowGuideId = nil
    end
    --[[ local compName = GWG.GWCompName.gw_home_comp_hud_arrow
     self:RemoveComponent(compName)]]
end

function GWEventObject:PlayEffect(type, pos, rot, scale, parent)
    if not self.effectGroup then
        self.effectGroup = {}
    end
    if not self.effectGroup[type] then
        local compId = gw_home_effect_mgr.CreateEffects(type, parent or self.transform, pos, nil, rot, scale)
        self.effectGroup[type] = compId
    end
end
function GWEventObject:StopEffect(type)
    if self.effectGroup and self.effectGroup[type] then
        gw_home_effect_mgr.RemoveEffect(self.effectGroup[type])
        self.effectGroup[type] = nil
    end
end

---@public 创建气泡
function GWEventObject:CreateBubble(type)
    if self.bubbleId then
        return
    end
    if not self.eventCfg then
        return
    end
    if not type then
        type = EventBubbleTypeByResType[self.eventCfg.ResType]
    end
    if type == GWConst.EHomeBubbleEntityType.None then
        return
    end
    local curEventId = GWG.GWHomeMgr.chapterData.GetCurrentEventPoint()
    if type == GWConst.EHomeBubbleEntityType.EventGesture and self.eventId ~= curEventId then
        --手势气泡的时候必须是当前事件出现
        return
    end
    local clickFunc = function()
        self:OnClickGrid()
    end
    self.bubbleId = GWG.GWHomeMgr.bubbleMgr.BindBubbleEntity(nil, type, self.transform, clickFunc, nil);
end

---@public 销毁气泡
function GWEventObject:DisposeBubble()
    if self.bubbleId then
        GWG.GWHomeMgr.bubbleMgr.DisposeBubbleEntity(self.bubbleId)
        self.bubbleId = nil
    end
    self.isCreateXYXBubble = false
end

---@public 创建锁定Hud
function GWEventObject:RefreshEventLockHud()
    local curEventId = GWG.GWHomeMgr.chapterData.GetCurrentEventPoint()
    if curEventId ~= self.eventId then
        return
    end
    local isUnlock, needId, needLevel = GWG.GWAdmin.GWHomeChapterUtil.GetBuildEventUnlock(self.eventId)
    if not isUnlock then
        self:DisposeBubble()
        local buildCfg = game_scheme:Building_0(needId, needLevel)
        if not buildCfg then
            return
        end
        local buildTypeCfg = game_scheme:BuildingType_0(buildCfg.TypeID)
        if not buildTypeCfg then
            return
        end
        local compName = GWG.GWCompName.gw_home_comp_hud_event_lock
        if not self:GetComponent(compName) then
            local id, comp = GWG.GWAdmin.GWHomeHudUtil.InitMoveHudComponent(compName, self.transform)
            comp:SetOffsetScale(1)
            comp:SetNodifyText(buildTypeCfg, needLevel)
            comp:SetOnClick(function()
                self:OnClickGrid()
            end)
            self:AddComponent(compName, comp)
        end
        self:PlayEffect(GWConst.HomeEffectType.EventCombatGrid)
    else
        if self.eventCfg.type == GWConst.BuildEventType.MainQuest or
                self.eventCfg.type == GWConst.BuildEventType.MiniGame then
        else
            self:CreateBubble()
        end
        self:DisposeEventLockHud()
    end
end
---@public 播放通关特效
function GWEventObject:PlayEventPassLight()
    local posX, posY, posZ = gw_home_grid_data.GetPosByGridXY(self.mapCfg.x, self.mapCfg.y)
    local pos = { x = posX, y = posY, z = posZ }
    self:PlayEffect(GWConst.HomeEffectType.EventPassLight, pos, nil, nil, GWG.GWHomeNode.effectNode())
    if self.eventPassTimer then
        util.RemoveDelayCall(self.eventPassTimer)
        self.eventPassTimer = nil
    end
    self.eventPassTimer = util.DelayCallOnce(3, function()
        self:StopEffect(GWConst.HomeEffectType.EventPassLight)
    end)
end

---@public 销毁锁定Hud
function GWEventObject:DisposeEventLockHud()
    local compName = GWG.GWCompName.gw_home_comp_hud_event_lock
    self:RemoveComponent(compName)
end

function GWEventObject:DisposeEffect()
    self:DisposeEventLockHud()
    self:DisposeFightHud()
    self:DisposeBubble()
    self:DisposeArrowHud()

    if self.effectGroup then
        for i, effectId in pairs(self.effectGroup) do
            gw_home_effect_mgr.RemoveEffect(effectId)
        end
        self.effectGroup = {}
    end
end
---清楚数据
---@see override
function GWEventObject:ClearData()
    self:RemoveComponent(GWG.GWCompName.gw_home_comp_hud_xyx)
    if self.delayCallTask then
        util.RemoveDelayCall(self.delayCallTask)
        self.delayCallTask = nil
    end
    self:ClearGridPos()
    self:DestroyModel()
    self:DisposeArrowHud()
    self:DisposeEffect()
    self.curGridX = nil
    self.curGridY = nil
    self.mapId = nil
    self.eventId = nil
    self.size = nil
    self.sortingGroup = nil
    self.node = nil
    self.ground = nil
    self.deadModeAction = nil
    self.handEffect = nil
    self.playerEffect = nil
    self.currentEffect = nil
    self.rewardEffect = nil
    self.fightEffect = nil
end
function GWEventObject:SetLayer(gridX, gridY)
    if gridX == nil or gridY == nil then
        gridX = self.serData.x
        gridY = self.serData.y
    end
    --设置对应的层级
    if not util.IsObjNull(self.sortingGroup) then
        self.sortingGroup.sortingOrder = GWG.GWAdmin.HomeCommonUtil.GetCurSortingOrder(gridX, gridY)
    end
end
function GWEventObject:CheckEventConfig()
    if not self.eventCfg or not self.eventCfg.type then
        GWG.GWAdmin.SwitchUtility.Error("GWEventObject eventCfg = nil", self.eventId)
        return false
    end
    return true
end

--- 新手引导
function GWEventObject:NovicePreform()
    if not self.modelComp then
        self:SetEventModel()
    end
    if self.modelComp then
        self.modelComp:SetActive(false)
        self.modelComp:NovicePreform()
    end
end
---@public 播放小游戏解锁HUD
function GWEventObject:PlayMiniGameUnLock()
    if not self.isXyx then
        return
    end
    --[[local isShow = gw_main_mini_game_data.IsMiniGameUnlockLevelId(self.eventCfg.TypePara.data[0], self.eventId, true)
    if not isShow then
        return
    end]]
    local compName = GWG.GWCompName.gw_home_comp_hud_xyx
    if not self:GetComponent(compName) then
        local id, comp = GWG.GWAdmin.GWHomeHudUtil.InitMoveHudComponent(compName, self.transform)
        comp:SetOffsetScale(1)
        comp:SetData(0, 150, 1.5)
        self:AddComponent(compName, comp)
    end
    self:AddCustomTimer(2, function()
        self:RemoveComponent(GWG.GWCompName.gw_home_comp_hud_xyx)
    end
    )
end

--function GWEventObject:RegisterListener()
--    self.EventRefresh = function(eventName, eventId)
--        self:CreateBubbleHud(self.mapId)
--    end
--    gw_ed.mgr:Register(gw_ed.GW_HOME_EVENT_UPDATE, self.EventRefresh)
--end
--- 弃置
---@see override
function GWEventObject:Dispose()
    if self.eventPassTimer then
        util.RemoveDelayCall(self.eventPassTimer)
        self.eventPassTimer = nil
    end
    if self.refreshUpgrade then
        gw_ed.mgr:Unregister(gw_ed.GW_HOME_BUILDING_UPGRADE, self.refreshUpgrade)
        self.refreshUpgrade = nil
    end
    if self.OnInputEvent then
        gw_ed.mgr:Register(gw_ed.GW_INPUT_EVENT, self.OnInputEvent)
        self.OnInputEvent = nil
    end
    event.Unregister(event.UPDATE_MODULE_OPEN,self.OnUpdateModuleOpen)
    self:ClearData()
    unit_base_object.Dispose(self)
end

--- 重置为了循环利用
---@see override
function GWEventObject:Recycle()
    self:ClearData()
    unit_base_object.Recycle(self)
end

-- - - - - - - - - - - - - - - - - - - - - 分割线 - - - - - - - - - - - - - - - - - - - -

return GWEventObject