local print = print
local require = require
local pairs = pairs     
local ipairs = ipairs
local string = string
local table = table
local newClass = newclass
local type = type

local controller_base = require "controller_base"
local ui_window_mgr = require "ui_window_mgr"
local gw_madness_lord_mgr = require "gw_madness_lord_mgr"
local lang = require "lang"
local log = require "log"
local event_violentZombieLeader_define = require("event_violentZombieLeader_define")

--region Controller Life
module("ui_madness_lord_activity_main_panel_controller")
local controller = nil
local UIController = newClass("ui_madness_lord_activity_main_panel_controller", controller_base)

function UIController:Init(view_name, controller_name, data)
    self.CData = {}
    self.__base.Init(self, view_name, controller_name)  
    local activityData = gw_madness_lord_mgr.GetActivityData()
    local festival_activity_mgr = require "festival_activity_mgr"
    local festival_activity_cfg = require "festival_activity_cfg"
    local actId = festival_activity_mgr.GetActivityIdByCodeType(festival_activity_cfg.ActivityCodeType.MadnessLord)
    local level = activityData[1] and activityData[1].bossLevel or 1 --取首位数据的boss等级，因为每个都一样
    self.cfg = gw_madness_lord_mgr.GetCfgDataByActIdAndLevel(actId,level)
    self.selectBoss = nil
    if self.cfg then
        local sendData = 
        {
            monsterList = {}
        }
        self.selectBoss = self.cfg.monster[1].sid
        for i,v in ipairs(self.cfg.monster) do
            local temp =
            {
                monsterActData = activityData[v.id],
                monsterCfg = v,
            }
            table.insert(sendData.monsterList,temp)
        end
        sendData.cfg = cfg
        self:TriggerUIEvent( "ShowPanel",sendData)
    else
        log.Error("不存在对应的cfg数据！level:"..level..",actId:"..actId)
    end

end

function UIController:OnShow()
    self.__base.OnShow(self)
end

function UIController:Close(data)   
    if self.CData then
        for i, v in pairs(self.CData) do
            if type(v) == "table" and v.Dispose then
                v:Dispose()
            end
        end
    end

    self.CData = nil
    if not data or not data.isRecycleUI then
        self.__base.Close(self)
        controller = nil
    end
end

function UIController:AutoSubscribeEvents()
    self.UpdateRank = function(_,bossType)
        self:TriggerUIEvent( "UpdateRank",bossType)
    end
    self:RegisterEvent(event_violentZombieLeader_define.VIOLENT_ZOMBIE_BOSS_RANK_UPDATE,self.UpdateRank)
end

function UIController:AutoUnsubscribeEvents() 
end
--endregion

--region Controller Logic
function  UIController:OnBtnGoToClickedProxy()
    ui_window_mgr:ShowModule("ui_madness_lord_team_panel", nil, nil, { selectBoss = self.selectBoss })
end
function  UIController:OnBtnHelpClickedProxy()
    local ui_help=require"ui_help"
    ui_help.ShowWithDate(10092)
end
function  UIController:OnTogMonster_1ValueChange(state)
    self:TriggerUIEvent( "OnPageChange",1)
    self.selectBoss = self.cfg.monster[1].sid
end
function  UIController:OnTogMonster_2ValueChange(state)
    self:TriggerUIEvent( "OnPageChange",2)
    self.selectBoss = self.cfg.monster[2].sid
end
function  UIController:OnTogMonster_3ValueChange(state)
    self:TriggerUIEvent( "OnPageChange",3)
    self.selectBoss = self.cfg.monster[3].sid
end

--endregion

--region Controller Static 
function Show()
    if controller == nil then
        controller = UIController.new()
    end
    return controller
end
--endregion
