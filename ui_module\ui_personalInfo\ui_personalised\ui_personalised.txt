--- Created by: 袁楠
--- DateTime: 2024/8/28
--- desc: 个性化界面View

--region Require
local require = require
local pairs = pairs
local string = string
local type = type
local Gradient = CS.War.UI.Gradient
local TextMeshProUGUI       = CS.TMPro.TextMeshProUGUI
local HorizontalLayoutGroup = CS.UnityEngine.UI.HorizontalLayoutGroup

local os = os

local event = require "event"
local unforced_guide_mgr = require "unforced_guide_mgr"
local enum_define = require "enum_define"
local class = require "class"
local ui_base = require "ui_base"
local lang = require "lang"
local const_personalInfo = require "const_personalInfo"
local helper_personalInfo = require "helper_personalInfo"
local face_item = require "face_item_new"
local title_icon = require "title_icon"
local red_const = require "red_const"
local table_list = require "ui_table_list"
local module_item = require "ui_module_item"
local time_util = require "time_util"
local attribute_group = require "ui_attribute_group"
local sprite_asset = require "card_sprite_asset"
local model_item = require "model_item"
local util = require "util"
local Quaternion = CS.UnityEngine.Quaternion
local weapon_data = require "weapon_data"
--endregion

--region View Life
module("ui_personalised")
local ui_path = "ui/prefabs/gw/gw_personalinfo/uipersonalised.prefab"
local window = nil
local UIView = {}

UIView.widget_table = {
    Btn_close = { path = "bottom/btn_closeBtn", type = "Button", event_name = "OnCloseBtnClick", backEvent = true },
    Btn_overview = { path = "center/Auto_overviewBtn", type = "Button", event_name = "OnBtnOverviewEvent" },
    
    Tog_head = { path = "bottom/Scroll View/Viewport/togGroup/togHead", type = "Toggle", value_changed_event = "OnTogHeadValueChanged" },
    Tog_schloss = { path = "bottom/Scroll View/Viewport/togGroup/togSchloss", type = "Toggle", value_changed_event = "OnTogSchlossValueChanged" },
    Tog_namePlate = { path = "bottom/Scroll View/Viewport/togGroup/togNamePlate", type = "Toggle", value_changed_event = "OnTogNamePlateValueChanged" },
    Tog_frame = { path = "bottom/Scroll View/Viewport/togGroup/togFrame", type = "Toggle", value_changed_event = "OnTogFrameValueChanged" },
    Tog_title = { path = "bottom/Scroll View/Viewport/togGroup/togTitle", type = "Toggle", value_changed_event = "OnTogTitleValueChanged" },
    Tog_mythicalAni = { path = "bottom/Scroll View/Viewport/togGroup/togMythicalAni", type = "Toggle", value_changed_event = "OnTogMythicalAniValueChanged" },
    Tog_effect = { path = "bottom/Scroll View/Viewport/togGroup/togEffect", type = "Toggle", value_changed_event = "OnTogEffectValueChanged" },

    Text_name = { path = "center/propInfo/Auto_nameText", type = "Text" },
    Grad_name = { path = "center/propInfo/Auto_nameText", type = Gradient },
    Text_expireTime = { path = "center/propInfo/Auto_expireTime/Auto_expireTimeText", type = "Text" },
    Text_showChannel = { path = "center/titleInfo/title/Auto_showChannel", type = "Text" },

    RTan_headList = { path = "center/list/iconList", type = "RectTransform" },
    RTan_schlossList = { path = "center/list/schlossList", type = "RectTransform" },
    RTan_namePlateList = { path = "center/list/namePlateList", type = "RectTransform" },
    RTan_frameList = { path = "center/list/frameList", type = "RectTransform" },
    RTan_titleList = { path = "center/list/titleList", type = "RectTransform" },
    RTan_animalList = { path = "center/list/animalList", type = "RectTransform" },
    RTan_schlossEffectList = { path = "center/list/schlossEffectList", type = "RectTransform" },
    
    RTan_propInfo = { path = "center/propInfo", type = "RectTransform" },
    RTan_attributeGroup = { path = "center/propInfo/Auto_attributeGroup", type = "RectTransform" },
    RTan_nullInfo = { path = "center/nullInfo", type = "RectTransform" },
    RTan_faceInfo = { path = "center/faceInfo", type = "RectTransform" },
    RTan_schlossInfo = { path = "center/schlossInfoMask/schlossInfo", type = "RectTransform" },
    RTan_namePlateInfo = { path = "center/schlossInfoMask/schlossInfo/namePlateInfo", type = "RectTransform" },
    RTan_titleInfo = { path = "center/titleInfo", type = "RectTransform" },
    RTan_expireTime = { path = "center/propInfo/Auto_expireTime", type = "RectTransform" },
    RTan_animalInfo = { path = "center/animalInfoMask/animalInfo", type = "RawImage" },
    --RTan_schlossEffectInfo = { path = "center/schlossEffectInfoMask/schlossInfo", type = "RectTransform" },

    RTan_namePlateImg = { path = "center/schlossInfoMask/schlossInfo/namePlateInfo/nameImg", type = "Image" },
    RTan_namePlateImgHorizontalLayout = { path = "center/schlossInfoMask/schlossInfo/namePlateInfo/nameImg", type = HorizontalLayoutGroup },
    RTan_namePlateNameTxt = { path = "center/schlossInfoMask/schlossInfo/namePlateInfo/nameImg/txtName", type = "Text" },
    RTan_namePlateLvTxt = { path = "center/schlossInfoMask/schlossInfo/namePlateInfo/levelImg/txtLv", type = "Text" },
}



function UIView:SetVCTypeUI()
    return self.__base:SetVCTypeUI(enum_define.enum_ui_vc_type.vc)
end

--[[窗口初始化]]
function UIView:Init()
    self:SetVCTypeUI()
    self.__base:Init(self)

    self:BuildToggleInfo()
    self:BindAllRedDot()

    self.VData = {}
    self.LabelList = {}

    self.goodsSpriteAssets = sprite_asset.CreateSpriteAsset()
    self.namePlateSpriteAssets = sprite_asset.CreateCardSpriteAsset("personalisedNamePlate")

    -- 初始化头像
    local faceItem = face_item.CFaceItem():Init(self.RTan_faceInfo, nil, 1)
    self.VData.faceItem = faceItem

    -- 初始化城堡模型
    local moduleItem = module_item:new():Init(self.RTan_schlossInfo.gameObject, nil)
    self.VData.moduleItem = moduleItem

--[[    local moduleEffectItem = module_item:new():Init(self.RTan_schlossEffectInfo.gameObject, nil)
    self.VData.moduleEffectItem = moduleEffectItem]]
    
    -- 初始化城堡属性 
    local attributeGroup = attribute_group:new():Init(self.RTan_attributeGroup.gameObject)
    self.VData.attributeGroup = attributeGroup
    self:ResetAllState(true)
end

function UIView:BuildToggleInfo()
    self.ui_toggle_Info = {
        [self.Tog_head] = {
            enumType = const_personalInfo.PersonalisedTag.face,
        },
        [self.Tog_schloss] = {
            enumType = const_personalInfo.PersonalisedTag.schloss,
        },
        [self.Tog_namePlate] = {
            enumType = const_personalInfo.PersonalisedTag.namePlate,
        },
        [self.Tog_frame] = {
            enumType = const_personalInfo.PersonalisedTag.frame,
        },
        [self.Tog_title] = {
            enumType = const_personalInfo.PersonalisedTag.title,
        },
        [self.Tog_mythicalAni] = {
            enumType = const_personalInfo.PersonalisedTag.MythicalAnimals,
        },
        [self.Tog_effect] = {
            enumType = const_personalInfo.PersonalisedTag.effect,
        }
    }
end

function UIView:BindAllRedDot()
    for tog, info in pairs(self.ui_toggle_Info) do
        self:BindUIRed(tog.transform, red_const.Enum.PersonalisedPerTag, { info.enumType }, nil )
    end
end

function UIView:SetAnimalPageActive(isActive)
    if isActive then
        self.Tog_mythicalAni.gameObject:SetActive(true)
    else
        self.Tog_mythicalAni.gameObject:SetActive(false)
    end
end
function UIView:SetEffectPageActive(isActive)
    if isActive then
        self.Tog_effect.gameObject:SetActive(true)
    else
        self.Tog_effect.gameObject:SetActive(false)
    end
end

--神兽装扮模型生成
function UIView:SetAnimalModel(path, adornId)
    if self.VData.moduleItemAnimal then
        self.VData.moduleItemAnimal:Dispose()
        self.VData.moduleItemAnimal = nil
    end
    self.VData.moduleItemAnimal = model_item.CModelItem():InitSingle(path, function(_rt)
        if self:IsValid() then
            self.RTan_animalInfo.texture = _rt
            self.RTan_animalInfo.gameObject:SetActive(false)
            util.DelayCall(0.2, function()
                if self and self:IsValid() and self.RTan_animalInfo and not util.IsObjNull(self.RTan_animalInfo) then
                    self.RTan_animalInfo.gameObject:SetActive(true)
                end
            end)
            self.RTan_animalInfo:SetNativeSize()
            local cameraData = weapon_data.weaponCameraData[adornId]
            if cameraData then
                self.VData.moduleItemAnimal.actorNode.gameObject.transform.parent.localRotation = Quaternion.Euler(0, cameraData.rotate, 0)
                self.VData.moduleItemAnimal:MoveCamera(cameraData.pos)
            end
            self.VData.moduleItemAnimal:LookAtModel()
            self.RTan_animalInfo.transform.anchoredPosition = { x = 0, y = 0 }
        end
    end, false, { x = 885, y = 1211 })
end

--[[资源加载完成，被显示的时候调用]]
function UIView:OnShow()
    self:OnPlayAShowAni()
    self.__base:OnShow()
end

--[[界面隐藏时调用]]
function UIView:OnHide()
    self.__base:OnHide()
end

function UIView:Close()
    if self.VData then
        for _, v in pairs(self.VData) do
            v:Dispose()
        end
    end
    self.VData = nil

    local stepID = unforced_guide_mgr.GetCurStep()
    if unforced_guide_mgr.GetCurGuide() == 25  and (stepID == 30)then
        unforced_guide_mgr.CloseGuide()
    end
    
    self.__base:Close()
end
--endregion

--region 功能函数区
function UIView:BindLabelList(key, array, onClickEvent)
    if self.VData[key] then
        self.VData[key]:SetTableDataList(array)
    else
        local parentTrans, spriteAsset = self:BindHeadLabelList(key)
        self.VData[key] = table_list:new():Init(parentTrans, array, onClickEvent, spriteAsset, key)
    end
end

function UIView:BindHeadLabelList(key)
    if key == const_personalInfo.PersonalisedTag.face then
        return self.RTan_headList, sprite_asset.CreateHeroAsset()
    elseif key == const_personalInfo.PersonalisedTag.schloss then
        return self.RTan_schlossList, sprite_asset.CreateHeroAsset()
    elseif key == const_personalInfo.PersonalisedTag.namePlate then
        return self.RTan_namePlateList, sprite_asset.CreateHeroAsset()
    elseif key == const_personalInfo.PersonalisedTag.frame then
        return self.RTan_frameList, nil
    elseif key == const_personalInfo.PersonalisedTag.title then
        return self.RTan_titleList, sprite_asset.CreateTitleAsset()
    elseif key == const_personalInfo.PersonalisedTag.MythicalAnimals then
        return self.RTan_animalList, sprite_asset.CreateHeroAsset()
    elseif key == const_personalInfo.PersonalisedTag.effect then
        return self.RTan_schlossEffectList, sprite_asset.CreateHeroAsset()
    end
end

function UIView:UpdatePropertyList(data, labelLayer, state)
    if data then
        self.RTan_nullInfo.gameObject:SetActive(false)
        self.RTan_propInfo.gameObject:SetActive(true)

        --helper_personalInfo.SetQualityTMPColor(self.Text_name,data.quality, true)
        helper_personalInfo.SetQualityTextGradientColor(self.Text_name, self.Grad_name, data.quality, true)
        self.Text_name.text = lang.Get(data.nameId)

        self.VData.attributeGroup:SetAttributeIds(data.useAttributes, data.haveAttributes, state)
        self.VData[labelLayer]:SetTableIndex(data.id)
        return true
    else
        self:ResetAllState()
        self.RTan_nullInfo.gameObject:SetActive(true)
        self.RTan_propInfo.gameObject:SetActive(false)

        self.VData[labelLayer]:SetTableIndex(-1)
        return false
    end
end

local MAX_REMAINING_TIME = 86313600 -- 999d

function UIView:UpdateExpireTime(remainingTime)
    if remainingTime and remainingTime > 0  and remainingTime < os.server_time() + MAX_REMAINING_TIME then
        self.RTan_expireTime.gameObject:SetActive(true)
        local days, hours, minutes, seconds = time_util.GetCountdownTimeNumber(remainingTime)
        local timeStr = nil
        if days > 0 then
            timeStr = string.format("%dd %02d:%02d:%02d", days, hours, minutes, seconds)
        else
            timeStr = string.format("%02d:%02d:%02d", hours, minutes, seconds)
        end
        self.Text_expireTime.text = string.format("%s%s", lang.Get(const_personalInfo.lang_ValidTime), timeStr)
    else
        self.RTan_expireTime.gameObject:SetActive(false)
    end
end

function UIView:UpdateToggleState(key)
    self:OpenCurIndexPanel(key)
    if key == const_personalInfo.PersonalisedTag.face then
        self.Tog_head.isOn = true
    elseif key == const_personalInfo.PersonalisedTag.schloss then
        self.Tog_schloss.isOn = true
    elseif key == const_personalInfo.PersonalisedTag.namePlate then
        self.Tog_namePlate.isOn = true
    elseif key == const_personalInfo.PersonalisedTag.frame then
        self.Tog_frame.isOn = true
    elseif key == const_personalInfo.PersonalisedTag.title then
        self.Tog_title.isOn = true
    elseif key == const_personalInfo.PersonalisedTag.MythicalAnimals then
        self.Tog_mythicalAni.isOn = true
    elseif key == const_personalInfo.PersonalisedTag.effect then
        self.Tog_effect.isOn = true
    end
end

function UIView:OpenCurIndexPanel(key)
    self.RTan_headList.gameObject:SetActive(key == const_personalInfo.PersonalisedTag.face)
    self.RTan_schlossList.gameObject:SetActive(key == const_personalInfo.PersonalisedTag.schloss)
    self.RTan_namePlateList.gameObject:SetActive(key == const_personalInfo.PersonalisedTag.namePlate)
    self.RTan_frameList.gameObject:SetActive(key == const_personalInfo.PersonalisedTag.frame)
    self.RTan_titleList.gameObject:SetActive(key == const_personalInfo.PersonalisedTag.title)
    self.RTan_animalList.gameObject:SetActive(key == const_personalInfo.PersonalisedTag.MythicalAnimals)
    self.RTan_schlossEffectList.gameObject:SetActive(key == const_personalInfo.PersonalisedTag.effect)
end

function UIView:UpdateScrollListByCustomAvatar(key, data, customAvatarData)
    --self:UpdatePropertyList(data, key, state)
    --刷新table_list 的显示
    self.VData[key]:RefreshCustomAvatar(customAvatarData)

end

function UIView:UpdateScrollListByRemoveCustomAvatar(key, data, customAvatarData)
    self.VData[key]:RefreshCustomAvatar(customAvatarData)
    if data and data.clickSystemAvatar == true then
        self.VData[key]:ClickSystemAvatar()
    end
end

function UIView:ClickScrollListByCustomAvatar(key, customAvatarData)
    --self:UpdatePropertyList(data, key, state)
    --刷新table_list 的显示
    self.VData[key]:RefreshCustomAvatar(customAvatarData)
    self.VData[key]:ClickCustomAvatar()

end

function UIView:UpdateFaceByCustomAvatar(key, data, customAvatarData, frameID, state)
    self:UpdateToggleState(key)
    if self:UpdatePropertyList(data, key, state) then
        self.RTan_faceInfo.gameObject:SetActive(true)
        if customAvatarData then
            self.VData.faceItem:SetCustomFaceInfo(customAvatarData, function()
                local player_mgr = require "player_mgr"
                local info = { avatarUrl = customAvatarData.localUrl, ownerId = player_mgr.GetPlayerRoleID() }
                local mgr_personalInfo = require "mgr_personalInfo"
                mgr_personalInfo.ShowAvatarBig(info)
            end)
        end
        if frameID then
            self.VData.faceItem:SetFrameID(frameID, true)
        end
    end
end

function UIView:SwitchFaceLabel(key, data, faceID, frameID, state)
    self:UpdateToggleState(key)
    if self:UpdatePropertyList(data, key, state) then
        self.RTan_faceInfo.gameObject:SetActive(true)
        if faceID then
            local temp = {}
            if type(faceID) == "string" then
                temp.avatarUrl = faceID
                local player_mgr = require "player_mgr"
                temp.ownerId = player_mgr.GetPlayerRoleID()
            else
                temp.avatarID = faceID
                local player_mgr = require "player_mgr"
                temp.ownerId = player_mgr.GetPlayerRoleID()
            end
            self.VData.faceItem:SetFaceInfo(faceID, function()
                local mgr_personalInfo = require "mgr_personalInfo"
                mgr_personalInfo.ShowAvatarBig(temp)
            end)
        end
        if frameID then
            self.VData.faceItem:SetFrameID(frameID, true)
        end
    end
end

function UIView:SwitchSchlossLabel(key, data, path,effectParams,effectBottomParams)
    self:UpdateToggleState(key)
    if self:UpdatePropertyList(data, key, true) then
        self.RTan_schlossInfo.gameObject:SetActive(true)
        self.VData.moduleItem:SetChangeModel(path)
        self.VData.moduleItem:SetChangeModelEffect(effectParams)        
        self.VData.moduleItem:SetChangeModelBottomEffect(effectBottomParams)
        self.VData.moduleItem:SetCameraDistance(6)
        self.VData.moduleItem:SetLookAtConstraintWeight(1)
    end
end

function UIView:SwitchNamePlateLabel(key, data, path,effectParams)
    self:UpdateToggleState(key)
    if self:UpdatePropertyList(data, key, true) then
        self.RTan_namePlateInfo.gameObject:SetActive(true)

        self.RTan_schlossInfo.gameObject:SetActive(true)
        self.VData.moduleItem:SetChangeModel(path)
        self.VData.moduleItem:SetChangeModelEffect(effectParams)        
        self.VData.moduleItem:SetCameraDistance(6)
        self.VData.moduleItem:SetLookAtConstraintWeight(1)
    end
end

function UIView:SwitchTitleLabel(key, data)
    self:UpdateToggleState(key)
    if self:UpdatePropertyList(data, key, true) then
        self.RTan_titleInfo.gameObject:SetActive(true)
        if self.VData.titleItem then
            self.VData.titleItem:Dispose()
            self.VData.titleItem = nil
        end

        self.VData.titleItem = title_icon.CUIContent():Init(self.RTan_titleInfo, data, nil, 1.5)
        self.Text_showChannel.text = lang.Get(data.cfg.ChannelDes)
    end
end

function UIView:SwitchAnimalLabel(key, data, path)
    self:UpdateToggleState(key)
    if self:UpdatePropertyList(data, key, true) then
        --self.RTan_animalInfo.gameObject:SetActive(true)
        self:SetAnimalModel(path, data.adornID)
    end
end

function UIView:SwitchEffectLabel(key, data, path)
    self:UpdateToggleState(key)
    if self:UpdatePropertyList(data, key, true) then
        if string.IsNullOrEmpty(path) then
            --self.RTan_schlossEffectInfo.gameObject:SetActive(false)
            return
        end
        --self.RTan_schlossEffectInfo.gameObject:SetActive(true)
        self.RTan_schlossInfo.gameObject:SetActive(true)
        self.VData.moduleEffectItem:SetChangeModel(path)
        self.VData.moduleEffectItem:SetCameraDistance(6)
        self.VData.moduleEffectItem:SetLookAtConstraintWeight(1)
    end
end

function UIView:SetPlateInfo(name, plateIconName, level, paddingSetting)
    self.goodsSpriteAssets:GetSprite(plateIconName, function(sprite)
        if self and self.RTan_namePlateImg and not self.RTan_namePlateImg:IsNull() and plateIconName then
            self.RTan_namePlateImg.sprite = sprite
        end
    end)
    -- local padding = self.RTan_namePlateImgHorizontalLayout.padding
    -- padding.left = paddingSetting.l
    -- padding.right = paddingSetting.r
    -- self.RTan_namePlateImgHorizontalLayout.padding = padding
    self.RTan_namePlateNameTxt.text = name
    self.RTan_namePlateLvTxt.text = string.format("%d", level)
end

function UIView:ResetAllState(forced)
    if forced then
        self.RTan_headList.gameObject:SetActive(false)
        self.RTan_schlossList.gameObject:SetActive(false)
        self.RTan_frameList.gameObject:SetActive(false)
        self.RTan_titleList.gameObject:SetActive(false)
        self.RTan_animalList.gameObject:SetActive(false)
        self.RTan_schlossEffectList.gameObject:SetActive(false)
    end
    self.RTan_propInfo.gameObject:SetActive(false)
    self.RTan_nullInfo.gameObject:SetActive(false)
    self.RTan_faceInfo.gameObject:SetActive(false)
    self.RTan_schlossInfo.gameObject:SetActive(false)
    self.RTan_titleInfo.gameObject:SetActive(false)
    self.RTan_animalInfo.gameObject:SetActive(false)
    --self.RTan_schlossEffectInfo.gameObject:SetActive(false)
end

--endregion

--region View Static
local CUIView = class(ui_base, nil, UIView)

function Show(data)
    if window == nil then
        window = CUIView()
        window._NAME = _NAME
        window.isBlurBg = true
        window.delayOpenMain = 0
        window.delayCloseMain = 0
        window:LoadUIResource(ui_path, nil, nil, nil)
    end
    window:Show()
    return window
end

function Close(data)
    if window ~= nil then
        window:Close()
        window = nil
    end
end

function Hide()
    if window ~= nil then
        window:Hide()
    end
end
--endregion