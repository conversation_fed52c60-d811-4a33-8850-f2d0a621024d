--region FileHead
--- ui_alliance_gift.txt
-- author:  马睿
-- ver:     1.0
-- desc:    联盟礼物界面
-------------------------------------------------
--endregion 

--region Require
local require = require
local type = type
local string = string
local pairs = pairs
local ipairs = ipairs

local log = require "log"
local red_const = require "red_const"
local alliance_ui_util = require "alliance_ui_util"
local GameObject = CS.UnityEngine.GameObject
local ScrollRectTable = CS.UI.UGUIExtend.ScrollRectTable
local alliance_pb = require "alliance_pb"
local os = require "os"
local enum_define = require "enum_define"
local class = require "class"
local ui_base = require "ui_base"
local e_handler_mgr = require "e_handler_mgr"
local game_scheme = require "game_scheme"
local color_palette = require "color_palette"
local iui_item_detail = require "iui_item_detail"
local item_data = require "item_data"
local Common_Util = CS.Common_Util.UIUtil
local lang = require "lang"
local time_util = require "time_util"
local util = require "util"
--endregion 

--region ModuleDeclare
module("ui_alliance_gift")
local ui_path = "ui/prefabs/gw/alliancesystem/uialliancegift.prefab"
local window = nil
local UIView = {}
--endregion 

--region WidgetTable
UIView.widget_table = {
    Btn_close = { path = "panelBg/Auto_close", type = "Button", event_name = "OnBtn_closeClickedProxy" },
    Btn_btnGoBattle = { path = "Auto_BootyPanel/GotoTip/Auto_btnGoBattle", type = "Button", event_name = "OnBtn_btnGoBattleClickedProxy" },
    Btn_ReceiveAllBtn = { path = "Auto_BootyPanel/Auto_ReceiveAllBtn", type = "Button", event_name = "OnBtn_ReceiveAllBtnClickedProxy" },
    Btn_GiftReceiveAllBtn = { path = "Auto_AllyGiftPanel/Auto_ReceiveAllBtn", type = "Button", event_name = "OnBtn_btnReceiveClickedProxy" },
    Auto_BoxScrollList = { path = "Auto_BootyPanel/Auto_ScrollList/Viewport/Auto_Content", type = ScrollRectTable },
    privilegePanel = { path = "privilegePanel", type = "RectTransform" },
    srt_privilegeContent = { path = "privilegePanel/panel/rtf_rankList/Viewport/srt_privilegeContent", type = ScrollRectTable },
    privilege_closeBtn = { path = "privilegePanel/privilege_closeBtn", type = "Button", event_name = "OnBtn_PrivilegeCloseBtnClickedProxy" },
    Slider_ExpSlider = { path = "giftInfoArea/Auto_ExpSlider", type = "Slider" },
    Text_Auto_giftLevelText = { path = "giftInfoArea/Auto_giftLevelText", type = "Text" },
    Image_Auto_giftIcon = { path = "giftInfoArea/Auto_giftIcon", type = "Image" },
    Btn_Auto_giftIcon = { path = "giftInfoArea/Auto_giftIcon", type = "Button", event_name = "OnBtn_GiftIconClickedProxy" },
    Image_Auto_ExpIcon = { path = "giftInfoArea/Auto_ExpIcon", type = "Image" },
    Text_Auto_exp_Text = { path = "giftInfoArea/Auto_ExpSlider/Auto_exp_Text", type = "Text" },

    Rtsf_BootyPanel = { path = "Auto_BootyPanel", type = "RectTransform", event_name = "" },
    Rtsf_AllyGiftPanel = { path = "Auto_AllyGiftPanel", type = "RectTransform", event_name = "" },
    Rtsf_Auto_AnonymousToggle = { path = "Auto_AllyGiftPanel/Auto_AnonymousToggle", type = "RectTransform", event_name = "" },
    Toggle_Auto_AnonymousToggle = { path = "Auto_AllyGiftPanel/Auto_AnonymousToggle", type = "Toggle", value_changed_event = "OnToggle_AnonymousValueChanged" },
    Auto_GiftScrollList = { path = "Auto_AllyGiftPanel/Auto_ScrollList/Viewport/Auto_Content", type = ScrollRectTable },
    Toggle_Auto_Box_Toggle = { path = "ToggleGroup/Auto_Box_Toggle", type = "Toggle", value_changed_event = "OnToggle_BoxValueChanged" },
    Toggle_Auto_Gift_Toggle = { path = "ToggleGroup/Auto_Gift_Toggle", type = "Toggle", value_changed_event = "OnToggle_GiftValueChanged" },

    Btn_btnGo = { path = "Auto_AllyGiftPanel/GotoTip/Auto_btnGo", type = "Button", event_name = "OnBtn_btnGoClickedProxy" },
    
    Text_box = { path = "ToggleGroup/Auto_Box_Toggle/boxText", type = "Text" },
    Text_gift = { path = "ToggleGroup/Auto_Gift_Toggle/giftText", type = "Text" },
    
    Trans_flowRewardGo = { path = "flowRewardGo", type = "RectTransform" },
    Trans_flowRewardStart = { path = "flowRewardStart", type = "RectTransform" },
}
--endregion 

--function 设置View-Controller模式的UI
-- return type  ---- 未定义/VC/纯V   
-- 注意，View-Controller模式的ui必须要重写这个接口
function UIView:SetVCTypeUI()
    return self.__base:SetVCTypeUI(enum_define.enum_ui_vc_type.vc)
end

--endregion 


--region WindowInit
--[[窗口初始化]]

--region WindowInit
--[[窗口初始化]]
function UIView:Init()
    self:SetVCTypeUI()
    self.__base:Init(self)

    self:BindUIRed(self.Toggle_Auto_Box_Toggle.transform, red_const.Enum.AllianceBoxPage, nil, { pos = { x = 155, y = 45 }, redPath = red_const.Type.Num })
    self:BindUIRed(self.Toggle_Auto_Gift_Toggle.transform, red_const.Enum.AllianceGiftPage, nil, { pos = { x = 155, y = 45 }, redPath = red_const.Type.Num })
    
    self:InitScrollRectTable()
    self:InitGiftScrollRectTable()
    --region User
    self:SwitchPanel(1)
    --endregion 
end --///<<< function

--endregion 


--region WindowOnShow
--[[资源加载完成，被显示的时候调用]]
function UIView:OnShow()
    self:ShowPrivilegePanel(false)
end --///<<< function

--endregion 



--region WindowOnHide
--[[界面隐藏时调用]]
function UIView:OnHide()
    --region User
    --endregion 
end --///<<< function

--endregion 


--region WindowClose
function UIView:Close()
    self.__base:Close()
    window = nil
    if self.Auto_BoxScrollList then
        self.Auto_BoxScrollList:ItemsDispose()
    end
    if self.Auto_GiftScrollList then
        self.Auto_GiftScrollList:ItemsDispose()
    end
    if self.srt_privilegeContent then
        self.srt_privilegeContent:ItemsDispose()
    end
    --region User
    --endregion 
end --///<<< function

--endregion 

--region WindowInherited
local CUIView = class(ui_base, nil, UIView)
--endregion 

---********************功能函数区**********---

function UIView:ShowGiftReceiveAll(isShow)
    self:SetActive(self.Btn_GiftReceiveAllBtn, isShow)
end

--设置匿名状态
function UIView:SetGiftAnonymousSate(isValue)
    self.Toggle_Auto_AnonymousToggle.isOn = isValue
end
function UIView:UpdateListData(data)
    if data.allianceGiftLV then
        self.Text_Auto_giftLevelText.text = string.format("%s %s", lang.Get(82), data.allianceGiftLV) -- data.allianceGiftLV
        self.Text_Auto_exp_Text.text = data.allianceGiftExp .. "/" .. data.allianceGiftMaxExp
        self.Slider_ExpSlider.value = data.allianceGiftExp / data.allianceGiftMaxExp
    end
    --[[Slider_ExpSlider
    Text_Auto_giftLevelText
    Image_Auto_giftIcon
    Image_Auto_ExpIcon 
    Text_Auto_exp_Text ]]
end
--设置列表数据
function UIView:InitScrollRectTable()
    self.Auto_BoxScrollList.onItemRender = function(scroll_rect_item, index, dataItem) 
        self:OnBoxItemRender(scroll_rect_item, index, dataItem)
    end 
    self.Auto_BoxScrollList.onItemDispose = function(scroll_rect_item, index)
        if scroll_rect_item and scroll_rect_item.data then

            if scroll_rect_item.data.timer then
                util.RemoveDelayCall(scroll_rect_item.data.timer)
                scroll_rect_item.data.timer = nil
            end
            
            if scroll_rect_item.data.icon ~= nil then
                scroll_rect_item.data.icon:Dispose()
                scroll_rect_item.data.icon = nil
            end

        end
    end
    
    self.srt_privilegeContent.onItemRender = function(scroll_rect_item, index, dataItem) 
        self:OnPrivilegeItemRender(scroll_rect_item, index, dataItem)
    end 
    self.srt_privilegeContent.onItemDispose = function(scroll_rect_item, index)
        if scroll_rect_item and scroll_rect_item.data then

        end
    end
end

function UIView:UpdateAlliancePrivilegeList(data,allianceGiftLV)
    self.allianceGiftLV = allianceGiftLV
    self:ShowPrivilegePanel(true)
    if not data then
        return
    end
    self.srt_privilegeContent:SetData(data, #data)
    self.srt_privilegeContent:Refresh(0, -1)
end

function UIView:ShowPrivilegePanel(isShow)
    self:SetActive(self.privilegePanel, isShow)
end

function UIView:UpdateAllianceBoxListData(data, begin)
    if not data then
        return
    end
    self.Auto_BoxScrollList:SetData(data, #data)
    self.Auto_BoxScrollList:Refresh(0, -1)
    if begin ~= 0 then
        self.Auto_BoxScrollList.renderPerFrames = begin
    end
end
function UIView:InitGiftScrollRectTable()
    self.Auto_GiftScrollList.onItemRender = function(...) 
        self:OnGiftItemRender(...)
    end
    self.Auto_GiftScrollList.onItemDispose = function(scroll_rect_item, index)
        if scroll_rect_item and scroll_rect_item.data then
            if scroll_rect_item.data.timer then
                util.RemoveDelayCall(scroll_rect_item.data.timer)
                scroll_rect_item.data.timer = nil
            end
            
            if scroll_rect_item.data.icon ~= nil then
                scroll_rect_item.data.icon:Dispose()
                scroll_rect_item.data.icon = nil
            end
        end
    end
end

function UIView:UpdateAllianceGiftListData(data, begin)
    if not data then
        return
    end
    self.Auto_GiftScrollList:SetData(data, #data)
    self.Auto_GiftScrollList:Refresh(0, -1)
    if begin ~= 0 then
        self.Auto_GiftScrollList.renderPerFrames = begin
    end
end

function UIView:OnPrivilegeItemRender(scroll_rect_item, index, dataItem)
    scroll_rect_item.data = scroll_rect_item.data or { index, dataItem }
    scroll_rect_item.data[1] = index
    scroll_rect_item.data[2] = dataItem
    --BtnReceiveOnClick
    local bg = scroll_rect_item:Get("bg")
    local privilegeIconBg = scroll_rect_item:Get("privilegeIconBg")
    local title = scroll_rect_item:Get("title")
    local desc = scroll_rect_item:Get("desc")
    local lock_unlockLevel = scroll_rect_item:Get("lock_unlockLevel")
    local gou_unlockLevel = scroll_rect_item:Get("gou_unlockLevel")
    local lock = scroll_rect_item:Get("lock")
    local gou = scroll_rect_item:Get("gou")
    local privilegeIcon = scroll_rect_item:Get("privilegeIcon")
    title.text = lang.Get(dataItem.title)
    desc.text = lang.Get(dataItem.description)
    lock_unlockLevel.text = string.format2(lang.Get(1005340),dataItem.UnlockLevel)
    gou_unlockLevel.text = string.format2(lang.Get(1005340),dataItem.UnlockLevel)
    self:CreateSubSprite("CreateGWAllianceAsset", privilegeIcon, dataItem.PrivilegeIcon,function () 
        privilegeIcon:SetNativeSize()
    end)


    local isUnlock = self.allianceGiftLV >= dataItem.UnlockLevel
    bg.color = isUnlock and color_palette.HexToColor("#D0DCE0") or color_palette.HexToColor("#D9D9D9")
    privilegeIconBg.color = isUnlock and color_palette.HexToColor("#A8BCC3") or color_palette.HexToColor("#B7B7B7")
    title.color = isUnlock and color_palette.HexToColor("#264C61") or color_palette.HexToColor("#4A4A4A")
    desc.color = isUnlock and color_palette.HexToColor("#4D6676") or color_palette.HexToColor("#606060")
    self:SetActive(gou, isUnlock)
    self:SetActive(lock, not isUnlock)
    self:SetActive(gou_unlockLevel, isUnlock)
    self:SetActive(lock_unlockLevel, not isUnlock)
    --绑定上点击等事件，主要是为了抛给controller层进行分离   
    scroll_rect_item.InvokeFunc = function(funcname)
        --注意  这里的事件是存在多种的clickItemEvent 
        if dataItem[funcname] then
            dataItem[funcname](index, dataItem)
        end
    end
end

function UIView:OnBoxItemRender(scroll_rect_item, index, dataItem)
    scroll_rect_item.data = scroll_rect_item.data or { index, dataItem }
    scroll_rect_item.data[1] = index
    scroll_rect_item.data[2] = dataItem
    --BtnReceiveOnClick
    local giftIcon = scroll_rect_item:Get("giftIcon")
    local giftOwnerText = scroll_rect_item:Get("giftOwnerText")
    local giftTimeText = scroll_rect_item:Get("giftTimeText")
    local giftSourceText = scroll_rect_item:Get("giftSourceText")
    local rewardIcon = scroll_rect_item:Get("rewardIcon")
    local rewardNumText = scroll_rect_item:Get("rewardNumText")
    local Auto_btnReceive = scroll_rect_item:Get("Auto_btnReceive")
    local Auto_btnReceived = scroll_rect_item:Get("Auto_btnReceived")

    --local listBg = scroll_rect_item:Get("listBg")
    local listBgBlack = scroll_rect_item:Get("listBgBlack")
    local itemParent = scroll_rect_item:Get("itemParent")


    if scroll_rect_item.data.timer then
        util.RemoveDelayCall(scroll_rect_item.data.timer)
        scroll_rect_item.data.timer = nil
    end
    
    
    -- rewardIcon
    local itemCfg = game_scheme:Item_0(dataItem.spoilsID)
    if itemCfg then
        giftOwnerText.text = string.format("%s",lang.Get(itemCfg.nameKey))
        self:CreateSubSprite("CreateSpriteAsset", giftIcon, itemCfg.icon)
    end
    
    if dataItem.rewardId~=nil then
        --礼物已领取,不应该在显示时间
        self:SetActive(giftTimeText, false)
    else
        self:SetActive(giftTimeText, true)
        scroll_rect_item.data.timer = util.IntervalCall(1, function()
            local tempTime = dataItem.expireTime - os.server_time()
            if not util.IsObjNull(giftTimeText) then
                giftTimeText.text = time_util.GetCountDown(tempTime)
            end
            if tempTime <= 0 then
                --礼物倒计时结束
                e_handler_mgr.TriggerHandler(window.controller_name, "BoxItemCountDownEnd", index, dataItem)
                return true
            end
        end)
    end
    
    local str = string.format2(lang.Get(dataItem.TextConfiguration), dataItem.roleName)
    giftSourceText.text = str
    rewardNumText.text = dataItem.experience
    if dataItem.rewardId then
        local rewardInfo = game_scheme:Reward_0(dataItem.rewardId)
        local data = {}
        if rewardInfo then
            data.produceId = rewardInfo.arrParam[0]
            data.num = rewardInfo.arrParam[1]
        end
        local itemCfg = game_scheme:Item_0(data.produceId)
        if itemCfg then
            local itemIcon2 = scroll_rect_item.data["icon"] or alliance_ui_util.CreateGoodsItemIcon(itemParent, 0.75, data.produceId, data.num, nil)
            scroll_rect_item.data["icon"] = itemIcon2
            itemIcon2:SetGoods(nil, data.produceId, data.num, function()
                iui_item_detail.Show(data.produceId, nil, item_data.Item_Show_Type_Enum.Reward_Interface, data.num, nil, nil)
            end)
            --scroll_rect_item.data["icon"] = itemIcon2
        end
        Common_Util.SetActive(itemParent, true)
        Common_Util.SetActive(giftIcon, false)
    else
        local rewardId = dataItem.levelRewardItemID;
        local boxCfg = game_scheme:BoxChoose_0(rewardId)
        if boxCfg then
            -- giftIcon boxCfg.icon
            Common_Util.SetActive(giftIcon, true)
            Common_Util.SetActive(itemParent, false)
        end
    end

    
    --设置显示状态
    Common_Util.SetActive(Auto_btnReceive, dataItem.rewardId == nil)
    Common_Util.SetActive(Auto_btnReceived, dataItem.rewardId ~= nil)
    if dataItem.rewardId then
        --变灰
        --Common_Util.SetColor(listBg, "#D1D1D1")
        Common_Util.SetActive(listBgBlack, true)
    else
        --变黄
        --Common_Util.SetColor(listBg, "#FBE3B3")
        Common_Util.SetActive(listBgBlack, false)
    end

    Auto_btnReceive.onClick:RemoveAllListeners()
    Auto_btnReceive.onClick:AddListener(function()
        self:FlowReward(Auto_btnReceive.transform,self.Image_Auto_ExpIcon.transform,5)
    end)
    
    e_handler_mgr.TriggerHandler(window.controller_name, "OnScorllBoxRectItemRender", index, dataItem)
    --绑定上点击等事件，主要是为了抛给controller层进行分离   
    scroll_rect_item.InvokeFunc = function(funcname)
        --注意  这里的事件是存在多种的clickItemEvent 
        if dataItem[funcname] then
            dataItem[funcname](index, dataItem)
        end
    end
end

function UIView:OnGiftItemRender(scroll_rect_item, index, dataItem)
    scroll_rect_item.data = scroll_rect_item.data or { index, dataItem }
    scroll_rect_item.data[1] = index
    scroll_rect_item.data[2] = dataItem
    --BtnReceiveOnClick
    local giftIcon = scroll_rect_item:Get("giftIcon")
    local giftOwnerText = scroll_rect_item:Get("giftOwnerText")
    local giftTimeText = scroll_rect_item:Get("giftTimeText")
    local giftSourceText = scroll_rect_item:Get("giftSourceText")
    local rewardIcon = scroll_rect_item:Get("rewardIcon")
    local rewardNumText = scroll_rect_item:Get("rewardNumText")
    local Auto_btnReceive = scroll_rect_item:Get("Auto_btnReceive")
    local Auto_btnReceived = scroll_rect_item:Get("Auto_btnReceived")

    --local listBg = scroll_rect_item:Get("listBg")
    local listBgBlack = scroll_rect_item:Get("listBgBlack")
    local itemParent = scroll_rect_item:Get("itemParent")

    if scroll_rect_item.data.timer then
        util.RemoveDelayCall(scroll_rect_item.data.timer)
        scroll_rect_item.data.timer = nil
    end
    
    --倒计时
    if dataItem.rewardId~=nil then
        self:SetActive(giftTimeText,false)
    else
        self:SetActive(giftTimeText,true)
        scroll_rect_item.data.timer = util.IntervalCall(1, function()
            local tempTime = dataItem.expireTime - os.server_time()
            if not util.IsObjNull(giftTimeText) then
                giftTimeText.text = time_util.GetCountDown(tempTime)
            end
            if tempTime <= 0 then
                --礼物倒计时结束
                e_handler_mgr.TriggerHandler(window.controller_name, "GiftCountDownEnd", index, dataItem)
                return true
            end
        end)
    end

    -- rewardIcon
    local itemCfg = game_scheme:Item_0(dataItem.spoilsID)
    if itemCfg then
        giftOwnerText.text = lang.Get(itemCfg.nameKey)

        --giftTimeText.text = dataItem.time
        local name = string.empty()
        if dataItem.anonymous == alliance_pb.emAllianceRSwitchOpt_Open then
            name = lang.Get(600421)
        else
            name = dataItem.roleName
        end

        local str = ""
        local rechargeCfg = game_scheme:RechargeGoods_0(dataItem.rechargeID)
        if rechargeCfg then
            local rechargeName = lang.Get(rechargeCfg.strGoodsNameID.data[0] or 0 )
             str = string.format2(lang.Get(600205), name, rechargeName)
        end
       
        giftSourceText.text = str

        self:CreateSubSprite("CreateSpriteAsset", giftIcon, itemCfg.icon)
    end

    rewardNumText.text = dataItem.experience
    if dataItem.rewardId then
        local rewardInfo = game_scheme:Reward_0(dataItem.rewardId)
        local data = {}
        if rewardInfo then
            data.produceId = rewardInfo.arrParam[0]
            data.num = rewardInfo.arrParam[1]
        end
        local itemCfg = game_scheme:Item_0(data.produceId)
        if itemCfg then
            local itemIcon2 = scroll_rect_item.data["icon"] or alliance_ui_util.CreateGoodsItemIcon(itemParent, 0.75, data.produceId, data.num, nil)
            scroll_rect_item.data["icon"] = itemIcon2
            itemIcon2:SetGoods(nil, data.produceId, data.num, function()
                iui_item_detail.Show(data.produceId, nil, item_data.Item_Show_Type_Enum.Reward_Interface, data.num, nil, nil)
            end)
            --scroll_rect_item.data["icon"] = itemIcon2
        end
        Common_Util.SetActive(itemParent, true)
        Common_Util.SetActive(giftIcon, false)
    else
        local rewardId = dataItem.levelRewardItemID;
        local boxCfg = game_scheme:BoxChoose_0(rewardId)
        if boxCfg then
            -- giftIcon boxCfg.icon
            Common_Util.SetActive(giftIcon, true)
            Common_Util.SetActive(itemParent, false)
        end
    end

    --设置显示状态
    Common_Util.SetActive(Auto_btnReceive, dataItem.rewardId == nil)
    Common_Util.SetActive(Auto_btnReceived, dataItem.rewardId ~= nil)
    if dataItem.rewardId then
        --变灰
        --Common_Util.SetColor(listBg, "#D1D1D1")
        Common_Util.SetActive(listBgBlack, true)
    else
        --变黄
        --Common_Util.SetColor(listBg, "#FBE3B3")
        Common_Util.SetActive(listBgBlack, false)
    end

    Auto_btnReceive.onClick:RemoveAllListeners()
    Auto_btnReceive.onClick:AddListener(function()
        local gw_common_item_animation_mgr = require "gw_common_item_animation_mgr"
        local id = dataItem.levelRewardItemID
        local main_slg_mgr = require "main_slg_mgr"
        local main_slg_const = require "main_slg_const"
        local tf = main_slg_mgr.GetMainButtonTransformByType(main_slg_const.MainButtonGroupType.RightBottom,main_slg_const.MainButtonType.bag)
        if tf then
            gw_common_item_animation_mgr.DoItemAnimationTransformToTransform(id, 1, Auto_btnReceive.transform,nil,nil,{hideOffset = {x = 360,y = 0}})
        end
    end)
    
    e_handler_mgr.TriggerHandler(window.controller_name, "OnScorllGiftRectItemRender", index, dataItem)
    --绑定上点击等事件，主要是为了抛给controller层进行分离   
    scroll_rect_item.InvokeFunc = function(funcname)
        --注意  这里的事件是存在多种的clickItemEvent 
        if dataItem[funcname] then
            dataItem[funcname](index, dataItem)
        end
    end
end

function UIView:RemoveBoxItem(index)
    self.Auto_BoxScrollList:RemoveChild(index)
end
--切换分页
function UIView:SwitchPanel(index)

    self.Toggle_Auto_Box_Toggle.isOn = index == 1
    self.Toggle_Auto_Gift_Toggle.isOn = index == 2
    self:SetActive(self.Rtsf_BootyPanel, index == 1)
    self:SetActive(self.Rtsf_AllyGiftPanel, index == 2)

    if index == 1 then
        self:SetToggleTextColor(self.Text_box, true)
        self:SetToggleTextColor(self.Text_gift, false)
    elseif index == 2 then
        self:SetToggleTextColor(self.Text_box, false)
        self:SetToggleTextColor(self.Text_gift, true)
    end

end

function UIView:SetToggleTextColor(text, bool)
    local TrueColor = "#FFFFFF"
    local FalseColor = "#C0D4E2"

    if bool then
        self:SetColor(text, TrueColor)
    else
        self:SetColor(text, FalseColor)
    end

end

---@public 一键领取的飞奖励
function UIView:FlowAllReceiveReward(boxNum)
    self:FlowReward(self.Trans_flowRewardStart.transform,self.Image_Auto_ExpIcon.transform,boxNum,{
        duration = 2.5,
        spawnCount = 3,
        interval = 0.1,
        randRadius = {100,100},
    })
end

---@public 飞奖励
function UIView:FlowReward(startTransform, endTransform,num,animPara)

    if not animPara then
        animPara = {duration = 1}
    end
    
    local gw_common_item_animation_mgr = require "gw_common_item_animation_mgr"
    
    --local goItem = GameObject.Instantiate(self.Trans_flowRewardGo.gameObject)
    gw_common_item_animation_mgr.DoGameObjectAnimationTransformToTransform(self.Trans_flowRewardGo,num, startTransform,endTransform,function()
        GameObject.DestroyImmediate(goItem)
    end,animPara)
end

---********************end功能函数区**********---

--region static ModuleFunction 
-- 特别注意，当前并不是由controller层来驱动ui的生命流程的 
-- 当前因为需要view层 也就是ui_base来驱动ui的init  加载完成，show等流程，所以流程仍然保留，而controller层的流程逻辑受view流程影响，
-- view对应的Init/Show 加载完后，当前会通过事件同步调用controller层的Init/Show流程，controller层的流程逻辑才会执行。
--当前仍然保留了静态的Show，Close接口流程
function Show(data)
    if data and data["uipath"] then
        ui_path = data["uipath"];
    end
    if window == nil then
        window = CUIView()
        window._NAME = _NAME
        window:LoadUIResource(ui_path, nil, nil, nil,nil,true)
    end
    --这里调用window:Show()  会造成多次调用 但hide后却又需要 uiwindowmgr有问题 TODO
    window:Show()
    return window
end

function Close(data)
    if window ~= nil then
        window:Close()
        window = nil
    end
end
--endregion
