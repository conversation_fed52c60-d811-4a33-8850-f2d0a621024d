local print = print
local require = require
local pairs = pairs     
local ipairs = ipairs
local string = string
local table = table
local tostring = tostring
local tonumber = tonumber
local type = type
local typeof = typeof
local CS = CS
local math = math

local Vector2 = CS.UnityEngine.Vector2
local SpriteSwitcher = CS.War.UI.SpriteSwitcher

local enum_define = require "enum_define"
local ui_base = require "ui_base"
local lang = require "lang"
local util = require "util"
local class = require "class"
local binding = require "ui_halloween_activity_slot_machine_panel_binding"

local reward_mgr = require "reward_mgr"

local gw_asset_mgr = require "gw_asset_mgr"

local slot_machine_ani = require "slot_machine_ani_helper"

local halloween_activity_slot_machine_const = require "halloween_activity_slot_machine_const"
local slot_game_progress_panel = require "slot_game_progress_panel"

--region View Life
module("ui_halloween_activity_slot_machine_panel")
local ui_path = binding.UIPath
local window = nil
local UIView = {}

UIView.widget_table = binding.WidgetTable

local UI_CONST = {
    img_item_goal_progress_total_length = 110
}

function UIView:SetVCTypeUI()
    return self.__base.SetVCTypeUI(self, enum_define.enum_ui_vc_type.vc)
end

function UIView:Init()
    self:SetVCTypeUI()
    self.__base.Init(self)

    self.VData = {
        ---@type SlotMachine
        slot_machine_ani = slot_machine_ani:new()
    }

    self.item_progress_panel.gameObject:SetActive(false)
end

function UIView:OnShow()
    self.__base.OnShow(self)
end

function UIView:OnHide()
    self.__base.OnHide(self)
end

function UIView:Close(data)   
    if self.VData then
        for i, v in pairs(self.VData) do
            if type(v) == "table" and v.Dispose then
                v:Dispose()
            end
        end
    end
    self.VData = nil    
    self.__base.Close(self)
    window = nil
end
--endregion

--region View Logic

-- 刷新活动倒计时
function UIView:RenderActivityCountDownTime(timeStr)
    --self["txt_ActivityCountDownTime"].text = timeStr
    self["txt_ActivityCountDownTime"].text = lang.Get(656002) .. timeStr
end

function UIView:RenderCoinItem(id, count)
    self["txt_item_count"].text = string.format("%d", count)
    gw_asset_mgr:LoadGoodsIcon(id, function(sprite)
        if not self:IsValid() then
            return
        end
        self["img_item_icon"].sprite = sprite
    end)
    
end



function UIView:RenderProgressItem(reward_id, progress_value, progress_max, progress_rate, onClick)
    local goodsItem = reward_mgr.GetRewardItemList(reward_id, self.rtf_goal_item, function (item_id, count, reward_type)
        local is_show = self.item_progress_panel.gameObject.activeSelf
        onClick(is_show)
    end, 0.75)
    local origin_size = self.img_item_goal_progress:GetComponent("RectTransform").sizeDelta
    local length = UI_CONST.img_item_goal_progress_total_length * progress_rate
    length = math.min(length, UI_CONST.img_item_goal_progress_total_length)
    self.img_item_goal_progress:GetComponent("RectTransform").sizeDelta = {x= length, y=origin_size.y} 
    self.txt_progress.text = string.format("%d/%d", progress_value, progress_max)
end

function UIView:ShowProgressPanel(bshow)
    self.item_progress_panel.gameObject:SetActive(bshow)
end

function UIView:RenderProgressPanel(data, clickClose, clickItem)
    local parent_order = self.curOrder
    slot_game_progress_panel.Render(self.item_progress_panel:GetComponent("RectTransform"), parent_order, data, clickClose, clickItem)
end

function UIView:RenderDrawTimesBtnInfo(count)
    local lang_key_tb = halloween_activity_slot_machine_const.lang_key
    self.txt_btn_times.text = lang.Get(count > 1 and lang_key_tb.kai or lang_key_tb.guan)
    if count == 1 then
        self.btn_times:GetComponent(typeof(SpriteSwitcher)):Switch(1)
        self.txt_btn_times_info.text = lang.Get(lang_key_tb.times_info_1)
    else
        self.btn_times:GetComponent(typeof(SpriteSwitcher)):Switch(0)
        self.txt_btn_times_info.text = lang.Get(lang_key_tb.times_info_5)
    end

end

function UIView:RenderDrawBtnInfo(item_id, count, enough)
    self.btn_draw:GetComponent(typeof(SpriteSwitcher)):Switch(count > 1 and 0 or 1)
    self["txt_btn_draw_cost"].text = string.format("x%d", count)
    self["txt_btn_draw_cost"].color = enough and CS.UnityEngine.Color.white or CS.UnityEngine.Color.red
    gw_asset_mgr:LoadGoodsIcon(item_id, function(sprite)
        if not self:IsValid() then
            return
        end
        self["img_btn_draw_icon"].sprite = sprite
    end)
end

function UIView:RenderRemainTimesInfo(count)
    self["txt_remain_times"].text = lang.Get(halloween_activity_slot_machine_const.lang_key.today_remain_times) .. ": " .. count
end

function UIView:RenderSlotMachine(idIconList, patternIdList, startPattern)
    startPattern = startPattern or {}
    self.VData.slot_machine_ani:Init(idIconList, patternIdList, {
        self["item_slot_col1"],self["item_slot_col2"],self["item_slot_col3"],
    }, self["btn_draw"])
    self.VData.slot_machine_ani:SetStartState(startPattern)
end

function UIView:RenderSkipState(boolSkip)
    self.tog_speed:SetIsOnWithoutNotify(boolSkip)
end
function UIView:RunSlotMachine(iconIdList, special_bg_bool_group, endcb)
    self.VData.slot_machine_ani:OnSpin(iconIdList, special_bg_bool_group, endcb)
end

--endregion




--region View Static 
local CUIView = class(ui_base, nil, UIView)

function Show(data)
    if window == nil then
        window = CUIView()
        window._NAME = _NAME

        if data and type(data) == "table" then
            local tempPath = data.uiPath or ui_path
            local tempParent = data.uiParent or nil
			window:LoadUIResource(tempPath, nil, tempParent, nil, nil, false)
        else
			window:LoadUIResource(ui_path, nil, nil, nil,nil, false)
        end
    end
    window:Show()
    return window
end

function Close(data)
    if window ~= nil then
        window:Close(data)
        window = nil
    end
end

function Hide()
    if window ~= nil then
        window:Hide()
    end
end
--endregion
