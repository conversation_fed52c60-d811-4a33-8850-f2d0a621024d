local game_scheme = require "game_scheme"
local halloween_const = require "halloween_const"


local halloween_jump_center_helper = {}

function halloween_jump_center_helper.GetListData()
    local cfgCount = game_scheme:ActivityPropJump_nums()
    local thisActivityCfgList = {}
    for i = 0, cfgCount - 1 do
        local cfg = game_scheme:ActivityPropJump(i)
        if cfg.AtcID == halloween_const.act_id.jump_center then
            table.insert(thisActivityCfgList, cfg)
        end
    end

    return thisActivityCfgList
end

return halloween_jump_center_helper