-- arena_common_data.txt ------------------------------------------
-- author:  hym
-- date:    2025/08/28
-- ver:     1.0
-- desc:    竞技场通用数据
--------------------------------------------------------------
local require 	= require
local string 	= string
local table 	= table
local pairs 	= pairs
local ipairs 	= ipairs
local player_prefs = require "player_prefs"
local game_scheme = require "game_scheme"
local event_arena_common_define = require "event_arena_common_define"
local event = require "event"
local data_mgr = require "data_mgr"
local arena_common_const = require "arena_common_const"

module("arena_common_data")
local logger = require("logger").new("arena_common_data",1)
IsLogLevel = logger.IsLogLevel
Warning = logger.Warning        --log
Warning0 = logger.Warning0      --error

---所有数据存储
local _d = data_mgr:CreateData("arena_common_data")
---非服务器数据存储
local mc = _d.mde.const
---服务器数据存储
local mn = _d.mde.net


function Clear()
    _d.mde:clear()
    mc = _d.mde.const
    mn = _d.mde.net
end

---根据竞技场类型获取当前竞技场ID
function GetCurArenaID(arenaType)
    if not mn.arenaIDList or not mn.arenaIDList[arenaType] then
        return
    end
    return mn.arenaIDList[arenaType]
end

---设置当前竞技场ID
function SetCurArenaID(arenaType,arenaID)
    if not mn.arenaIDList then
        mn.arenaIDList = {}
    end
    mn.arenaIDList[arenaType] = arenaID
end

---根据竞技场id获取玩家当前排名
function GetPlayerCurRank(arenaID)
    if not mn.curRank or not mn.curRank[arenaID] then
        return
    end
    return mn.curRank[arenaID]
end

--根据竞技场id获取玩家的旧排名
function GetPlayerOldRank(arenaID)
    if not mn.oldRank then
        mn.oldRank = {}
    end
    local data = GetArenaData(arenaID)
    if not data then
        return
    end
    mn.oldRank[arenaID] = data.nLastRank
    return mn.oldRank[arenaID]
end

--设置玩家上次进入竞技场时的排名
function SetPlayerOldRank(arenaID,rank)
    if not mn.oldRank then
        mn.oldRank = {}
    end
    mn.oldRank[arenaID] = rank
end

--设置竞技场数据
function SetArenaData(msg)
    if not msg.nArenaID then
        return
    end
    if not mn.arenaData then
        mn.arenaData = {}
    end
    mn.arenaData[msg.nArenaID] = msg
end

--获取竞技场数据
function GetArenaData(arenaID)
    if not mn.arenaData or not mn.arenaData[arenaID] then
        return
    end
    return mn.arenaData[arenaID]
end


--获取竞技场开启时间
function GetArenaOpenTime(arenaID)
    if not mn.arenaData or not mn.arenaData[arenaID] or not mn.arenaData[arenaID].nActivityStartTime then
        return 
    end 
    return mn.arenaData[arenaID].nActivityStartTime
end

--获取竞技场结束时间
function GetArenaEndTime(arenaID)
    if not mn.arenaData or not mn.arenaData[arenaID] or not mn.arenaData[arenaID].nActivityEndTime then
        return
    end
    return mn.arenaData[arenaID].nActivityEndTime
end

--获取排行榜前三数据
function GetTopRankData(arenaID)
    if not mn.arenaData or not mn.arenaData[arenaID] or not mn.arenaData[arenaID].lsTopNRoleArenaRankInfo then
        return 
    end 
    return mn.arenaData[arenaID].lsTopNRoleArenaRankInfo
end

--获取竞技场匹配区服
function GetArenaMatchServer(arenaID)
    if not mn.arenaData or not mn.arenaData[arenaID] or not mn.arenaData[arenaID].lsWorldIDs then
        return
    end
    return mn.arenaData[arenaID].lsWorldIDs
end


--获取玩家是否能参加竞技场
function IsCanJoinArena(arenaID)
    if  not mn.arenaData or not mn.arenaData[arenaID] or not mn.arenaData[arenaID].tSelfRankInfo then
        return false
    end
    return true
end

--获取竞技场配置
function GetArenaConfig(arenaID)
    if not mc.arenaConfig then
        mc.arenaConfig = {}
    end
    if not mc.arenaConfig[arenaID] then
        mc.arenaConfig[arenaID] = game_scheme:ArenaCommonInfo_0(arenaID)
    end
    if not mc.arenaConfig[arenaID] then
        Warning0("without arena config arenaID:"..arenaID)
        return {}
    end
    return mc.arenaConfig[arenaID]
end

--获取已进入的竞技场开启时间
function GetEnterArenaTime(arenaType)
    local str = string.format("%s_%s",arenaType,arena_common_const.LAST_ARENA_OPEN_TIME)
    return player_prefs.GetCacheData(str,0)
end

--设置已进入的竞技场开启时间
function GetEnterArenaTime(arenaType)
    local str = string.format("%s_%s",arenaType,arena_common_const.LAST_ARENA_OPEN_TIME)
    player_prefs.SetCacheData(str,arenaType)
end

--获取是否跳过战斗
function GetSkipBattle(arenaType)
    local str = string.format("%s_%d", arena_common_const.IS_SKIP_BATTLE, arenaType)
    return player_prefs.GetCacheData(str, true)
end

--设置是否跳过战斗
function SetSkipBattle(arenaType, isSkip)
    local str = string.format("%s_%d", arena_common_const.IS_SKIP_BATTLE, arenaType)
    player_prefs.SetCacheData(str, isSkip)
end

--设置竞技场切换数据
function SetArenaSwitchData(nOldArenaID, nNewArenaID)
    if not mn.arenaSwitchData then
        mn.arenaSwitchData = {}
    end
    if not nOldArenaID or not nNewArenaID then
        return
    end
    mn.arenaSwitchData[nOldArenaID] = nNewArenaID
end

--获取竞技场切换数据
function GetArenaSwitchData(arenaID)
    if not mn.arenaSwitchData or not mn.arenaSwitchData[arenaID] then
        return
    end
    return mn.arenaSwitchData[arenaID]
end


--region 竞技场战斗数据

--获取攻击阵容
function GetAttackLineUpTeamList(arenaType, arenaID, teamNum)
    local attackTeamList = mc.attackTeamList and mc.attackTeamList[arenaID]
    if(not attackTeamList or #attackTeamList < 1)then
        InitAttackLineUp(arenaType, teamNum)
    end
    return mc.attackTeamList and mc.attackTeamList[arenaID]
end

---@see 解析缓存的英雄数据
---@return table<number, table>
---@param heroData table<number, table>
---@param indexbyone boolean 是否从1开始
function ParseAttackLineUpByCache(arenaID, heroData,indexbyone)
    local battle_data = require "battle_data"
    local gw_home_drone_data = require "gw_home_drone_data"
    local gw_home_soldier_data = require "gw_home_soldier_data"
    local gw_hero_data = require "gw_hero_data"
    local gw_power_mgr = require "gw_power_mgr"
    mc.attackTeamList = mc.attackTeamList or {}
    mc.attackTeamList[arenaID] = {}
    local weaponPower = gw_home_drone_data.GetDroneLocalPower()
    local maxSoldierLevel = gw_home_soldier_data.GetCampusSoldierLevel()
    local maxLevelSoldierCfg = game_scheme:Soldier_1(maxSoldierLevel)
    for i = 1, #heroData do
        local teamItem = heroData[i]
        local teamItem1 = {}
        teamItem1.pals = {}
        teamItem1.index = i
        teamItem1.heroCount = 0
        ---每个队伍的战力 = 上阵英雄战力 + 士兵战力 + 召唤兽战力
        teamItem1.power = 0
        if teamItem and #teamItem > 0  then
            teamItem1.power = weaponPower
        end
        for k, hero in pairs(teamItem) do
            local hero1 = {}
            if indexbyone then
                hero1.row, hero1.col = battle_data.GetRowAndColByIndex(k)
            else
                hero1.row, hero1.col = battle_data.GetRowAndColByIndex(k+1)
            end
            teamItem1.heroCount = teamItem1.heroCount + 1
            hero1.heroSid = hero.heroSid
            hero1.heroID = hero.heroID
            hero1.heroPower = gw_power_mgr.GetHeroPowerByCfgId(hero1.heroID)
            local entity = gw_hero_data.GetHeroEntityByCfgId(hero1.heroID)
            local carrySoldierNum = entity and entity:GetHeroSoldierNum() or 0
            if maxLevelSoldierCfg then
                teamItem1.power = teamItem1.power + gw_power_mgr.GetSoliderForceInQueue(carrySoldierNum,maxLevelSoldierCfg.soldierID)
            end
            teamItem1.power = teamItem1.power + hero1.heroPower
            hero1.heroStar = hero.numProp.starLv
            hero1.lv = hero.numProp.lv
            hero1.palId = hero.heroSid
            table.insert(teamItem1.pals, hero1)
        end
        table.insert(mc.attackTeamList[arenaID], teamItem1)
    end
    return mc.attackTeamList[arenaID]
end

---@see 获取攻击阵容的信息(缓存或者城建)
function InitAttackLineUp(arenaType, teamNum)
    local lineMark = arena_common_const.ArenaAttackLineup[arenaType]
    if not lineMark then
        Warning0("can not get arena line mark arenaType:", arenaType)
        return
    end
    local arenaId = GetCurArenaID(arenaType)
    if not arenaId then
        Warning0("can not get arena id arenaType:", arenaType)
        return
    end
    local teamData = {}
    local battle_manager = require "battle_manager"
    local isExitCache = false
    local str = string.format("%s_%d", lineMark, arenaId)
    for i = 1, teamNum do
        local hData = battle_manager.GetHeroFormation(str..i)
        if(hData and #hData > 0 )then
            isExitCache = true
        end
        table.insert(teamData, hData)
    end
    ---有缓存使用缓存的进攻阵容数据，没有使用城建编队的数据
    if(not isExitCache)then
        GetAttackLineUpBySandPower(arenaId, teamNum)
    else
        ParseAttackLineUpByCache(arenaId, teamData)
    end
end

---@see 获取进攻阵容的信息根据城建队伍的战力排行
---@return table<number, table> 返回进攻阵容的数据
function GetAttackLineUpBySandPower(arenaID, teamNum)

    local sand_team_data = require "sand_team_data"
    local lines = sand_team_data.GetAllTeamEntityList(false)--gw_home_common_data.GetAllSquadData(); --缓存用
    mc.attackTeamList = mc.attackTeamList or {}
    mc.attackTeamList[arenaID] = {}
    if lines then
        local teamList = GetSimpleTeamData(lines)
        if #teamList > teamNum then
            table.sort(teamList, function(v1, v2)
                return v1.power > v2.power
            end)
        end
        for i = 1, teamNum do
            if(teamList[i])then
                teamList[i].index = i
                table.insert(mc.attackTeamList[arenaID], teamList[i])
            else
                local team = {}
                team.index = i
                ---队伍无英雄 队伍战力 = 召唤兽战力
                team.power = 0
                team.pals = {};
                table.insert(mc.attackTeamList[arenaID], team)
            end
        end
        table.sort(mc.attackTeamList[arenaID], function(v1, v2)
            return v1.power > v2.power
        end)
    else
        for i = 1, teamNum do
            local team = {}
            team.index = i
            team.power = 0
            team.pals = {};
            table.insert(mc.attackTeamList[arenaID], team)
        end
    end
    return mc.attackTeamList[arenaID]
end

--- @see data数据就是服务器下发通用解析TSimpleTeam
function  GetSimpleTeamData(data)
    local teamList = {}
    if data then
        for i,v in pairs(data) do
            local hData2 = v.team;
            local temp = {};
            temp.index = i;
            ---每个队伍的战力 = 上阵英雄战力 + 士兵战力 + 召唤兽战力
            local gw_home_soldier_data = require "gw_home_soldier_data"
            local gw_hero_data = require "gw_hero_data"
            local gw_home_drone_data = require "gw_home_drone_data"
            local gw_power_mgr = require "gw_power_mgr"
            local maxSoldierLevel = gw_home_soldier_data.GetCampusSoldierLevel()
            local maxLevelSoldierCfg = game_scheme:Soldier_1(maxSoldierLevel)
            temp.power = 0
            if hData2 ~= nil and hData2.pals ~= nil then
                if #hData2.pals > 0 then
                    temp.power = gw_home_drone_data.GetDroneLocalPower()
                end
                temp.order = hData2.order;
                temp.state = hData2.state;
                temp.isUnlocked = hData2.isUnlocked;
                temp.pals = {};
                for j,k in ipairs(hData2.pals) do
                    local heroData = {}
                    heroData.heroID = k.heroID;
                    heroData.heroSid = k.heroSid;
                    heroData.skinPropID = k.skinID;

                    heroData.heroLevel = k.heroLevel;
                    heroData.heroPower = gw_power_mgr.GetHeroPowerByCfgId(k.heroID);
                    local entity = gw_hero_data.GetHeroEntityByCfgId(k.heroID)
                    local carrySoldierNum = entity and entity:GetHeroSoldierNum() or 0
                    if maxLevelSoldierCfg then
                        temp.power = temp.power + gw_power_mgr.GetSoliderForceInQueue(carrySoldierNum,maxLevelSoldierCfg.soldierID)
                    end
                    heroData.heroStar = k.heroStar;
                    heroData.remainHP = k.remainHP;
                    heroData.row = k.row;
                    heroData.col = k.col;
                    temp.power = temp.power + heroData.heroPower
                    heroData.soldierCapacity = k.soldierCapacity;
                    heroData.soldierNum = k.soldierNum;
                    heroData.soldierValid = k.soldierValid;
                    table.insert(temp.pals, heroData)
                end
            end
            table.insert(teamList, temp)
        end
    end

    return teamList
end

---@see 进攻阵容队伍交换
---@param oldIndex number 需要替换的下标
---@param newIndex number 新英雄数据的下标
function SwitchAttackLineUpByIndex(arenaID, oldIndex, newIndex)
    local attackTeamList = mc.attackTeamList[arenaID]
    if not attackTeamList or #attackTeamList < 1 then
        Warning0("当前玩家的进攻阵容数据没有,请检查", attackTeamList)
        return
    end

    -- 检查索引是否在有效范围内
    if oldIndex < 1 or oldIndex > #attackTeamList or newIndex < 1 or newIndex > #attackTeamList then
        Warning0("索引超出范围: oldIndex =", oldIndex, ", newIndex =", newIndex)
        return
    end
    local tempTeamItem = attackTeamList[oldIndex]
    attackTeamList[oldIndex] = attackTeamList[newIndex]
    attackTeamList[newIndex] = tempTeamItem
    mc.attackTeamList[arenaID] = attackTeamList
end

---@public 交换防守阵容队伍
function SwitchDefenseLineUp(arenaID, oldIndex, newIndex)
    local defenseTeamList = mc.lineUp_defense[arenaID]
    if not defenseTeamList or #defenseTeamList < 1 then
        Warning0("当前玩家的进攻阵容数据没有,请检查", defenseTeamList)
        return
    end
    local tempTeamItem = defenseTeamList[oldIndex]
    defenseTeamList[oldIndex] = defenseTeamList[newIndex]
    defenseTeamList[newIndex] = tempTeamItem
    mc.lineUp_defense[arenaID] = defenseTeamList
end

---@public 设置竞技场的防守阵容数据
---@param defendLineupList (msg.defendLineupList)
function SetDefenseLineUp(arenaID, defendLineupList)
    mn.lineUp_defense = mn.lineUp_defense or {}
    mn.lineUp_defense[arenaID] = defendLineupList
end

---@public 获取竞技场的防守阵容数据
---@return msg.defendLineupList
function GetDefenseLineUp(arenaID)
    if not mn.lineUp_defense then
        return {}
    end
    return mn.lineUp_defense[arenaID]
end

---@public 临时存储战斗数据
function SetBattleData(arenaID, battleData)
    --战斗数据结构
    --[[local data = {
        attackInfo,
        defendInfo,
        enemyInfo,
    }]]
    mn.battleData = mn.battleData or {}
    mn.battleData[arenaID] = battleData
end

---@public 获取战斗数据
function GetBattleData(arenaID)
    return mn.battleData and mn.battleData[arenaID]
end

---@public 清除战斗数据
function ClearBattleData(arenaID)
    mn.battleData = mn.battleData or {}
    mn.battleData[arenaID] = nil
end

--endregion




