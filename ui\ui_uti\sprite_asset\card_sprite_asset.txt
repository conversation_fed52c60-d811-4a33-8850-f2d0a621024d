local require = require
local sprite_asset = require "sprite_asset"
local log = log
module("card_sprite_asset")
--其实建议读配置  或者规范图集一定放置在固定路径（目前不可行）
cardSpriteAssetPath = {
    equipBigCardAssetPath = "ui/equip/equipplus.asset",
    GoodsAssetPath = "ui/card/card.asset",
    --  HeroAssetPath = "ui/heroes/heroes.asset",
    ActivityAssetPath = "ui/activity/activity.asset",
    BuffAssetPath = "ui/battleinui/buff.asset",
    UnionTechnologyAssetPath = "ui/uniontechnology/projsicon.asset",
    LeagueAssetPath = "ui/guild/guild.asset",
    LeagueCompCellIconAssetPath = "ui/sociatyfight/mapicon.asset",
    TimeAreaTree = "ui/timearea/tree.asset",
    EquipAssetPath = "ui/equip/equip.asset",
    unlockGuidePath = "ui/unlockguide/unlockguide.asset",
    festivalPath = "ui/festival/festival.asset",
    christmasFestivalPath = "ui/festival/festival.asset",
    normalActivityPath = "ui/festival/festival.asset",
    specialBagPath = "ui/tequanlibao/specialbag.asset",
    welfarePath = "ui/xianshihuodong_s/welfera.asset",
    valuegiftPath = "ui/libao/valuegift.asset",
    peakIconPath = "ui/ditu/peakchapter.asset",
    systemIconPath = "ui/ziyuanbuzu/ziyuanbuzu.asset",
    starChestPath = "ui/starchest/starchest.asset",
    lnstanceTypePath = "ui/lnstancetype/card_lnstancetype.asset",
    taskChestPath = "ui/task/taskchest.asset",
    homelandPath = "ui/jiayuan/homeland.asset",
    factionWantedPath = "ui/zhenliegaiban/zhenlie.asset",
    rookiePath = "ui/rookie/rookie.asset",
    nationalFlagPath = "ui/nationalflag/nationalflag.asset",
    czlbAssetPath = "ui/chaozhilibao/chaozhilibao.asset",
    summonedPath = "ui/summoned/summoned.asset",
    arenaAssetPath = "ui/jingjichang_new/jingjichang_new.asset",
    jingjichangAssetPath = "ui/jingjichang/jingjichang.asset",
    titleAssetPath = "ui/titleicon/roletitle.asset",
    newheroactivity = "ui/yingxionghuodong/newheroactivity.asset",
    medal = "ui/lianmenghuizhang_s/medal.asset",
    instanceBossImg = "ui/zhuangbeifuben/bossimg.asset",
    warShipTechnology = "ui/warshiptechnology/warshiptechnology.asset",
    spaceExploration = "ui/spaceexploration/spaceexploration.asset",
    recommendpack = "ui/recommendpack/recommendpack.asset",
    activitybanner = "ui/operateactivity/activitybanner.asset",
    fleetexpedition = "ui/fleetexpedition/fleetexpedition.asset",
    casualcommonhead = "ui/casualcommon/casualcommonhead.asset",
    casuagameicon = "ui/casualcommon/casuagameicon.asset",
    giftAssetPath = "ui/libao/libao.asset",
    vipLvPath = "ui/vipzuanshi/viplv.asset", --vip数字等级代码
    actorBackground = "art/actorbackground/backgournd.asset", --角色背景
    decorateAssetPath = "ui/decorate/decorate.asset", --饰品
    jewelryworkshopAssetPath = "ui/jewelryworkshopdraws/jewelryworkshop.asset",
    renderImg = "ui/renderimg/renderimg.asset",
    skinShareImage = "ui/yingxiongpifu_s/skinshareimage.asset",
    rebirthspaceAssetPath = "ui/rebirthsplace/rebirthspace.asset",
    starTempleAsset = "ui/startemple/startempletog.asset",
    ---@field 适用于特殊的去留白的很多行icon ui_message_box_multiple_ex.txt
    cardMessageBoxIconPath = "ui/card_message_box_icon/card_message_box_icon.asset",
    unionslgrankImg = "ui/unionslgrank/unionslgrankimg.asset", 
    minigameIconAsset = "ui/dating_s/minigame/minigame.asset",
    pickTheRouteAsset = "ui/picktheroute/asset/pickthetoute.asset",
    drawSugramanAsset = "ui/festival_draw_sugarman/sugarman/drawsugraman.asset",
    halloweenDressAsset = "ui/halloweenday2023/halloween_dress.asset",
    shouchongAssetPath = "ui/shouchong_s/shouchong.asset",
    returnplayerAssetPath = "ui/newreturnplayer/bigtexture/returnplayer.asset",
    turncardAsset = "ui/festival_turn_over_card/turncard.asset",
    homebtnAsset = "ui/gwhomebtn/gwhomebtn.asset",
    gwsandmapAsset = "ui/gwsandmap/gwsandmap.asset",
    technologyIconAsset = "ui/gw_technologysystemicon/gw_technologysystemicon.asset",
    gwsandminmap = "ui/gwsandminmap/gwsandminmap.asset",
    gwbubble = "ui/gwbubble/ui.gwbubble.asset",
    gwhomescenebuilding = "ui/gwhomescenebuilding/gwhomescenebuilding.asset",
    gwhomebuildingicon = "ui/gwhomebuildingicon/gwhomebuildingicon.asset",
    gwuicommon = "ui/gwuicommon/gwuicommon.asset",
    gwmainslg = "ui/gw_main_slg/gw_main_slg.asset",
    gwsandmarkAsset = "ui/gwsandmark/gwsandmarktip.asset",
    buildingbtnAsset = "ui/gw_buildingbtn/gwbuildingbtn.asset",
    gwsandShareAsset = "ui/gwsandshare/gwsandshare.asset",
    survivorHeadAsset = "ui/gwbuildingsystemsurvivorhead/survivorhead.asset",
    soldierHeadAsset = "ui/gwbuildingsystemsoldierhead/soldierhead.asset",
    radarTaskIconAsset = "ui/gwradartaskicon/gwradartaskicon.asset",
    gwallianceachievement = "ui/gwallianceachievement/gwallianceachievement.asset",
    --gwsandsearchAsset = "ui/gwsandsearch/gwsandsearchtip.asset",
    gwsandmonstericon = "ui/gwsandmonstericon/gwsandmonstericon.asset",
    gwshop = "ui/gwshop/gw_shop.asset",
    campTrial = "ui/gw_camptrial/camp_trial.asset",
    gwsandattackAsset = "ui/gwsandattack/gwsandattacktip.asset",
    gwActivityCommon = "ui/gwactivitycommon/gwactivitycommon.asset",
    --droneEquipmentIconAsset = "ui/gwmagicweapon/droneequipmenticon/droneequipmenticon.asset"
    gwmenu = "ui/gwmenu/gwmenu.asset",
    gwtask = "ui/gwtask/gwtask.asset",
    gwExplainImage = "ui/gwexplainimage/gwexplainimage.asset",
    gwRadarTaskDetail = "ui/gwradartaskicon/gwradartaskdetail.asset",
    gwRechargeIcon = "ui/gwrechargeicon/gwrechargeicon.asset",
    gwAllianceDuelIcon = "ui/gwallianceduelicon/gwallianceduelicon.asset",
    gwPioneerActivityIcon = "ui/gwpioneeractivity/gwpioneeractivityicon.asset",
    gwWeekendCardIcon = "ui/gwweekendcard/gwweekendcard.asset",
    gwdailygift = "ui/gwdailygift/gwdailygift.asset",
    gwDiamondGift="ui/gwdiamondgift/diamondgift.asset",
    gwLimitGift="ui/gwlimitgift/gwlimitgift.asset",
    gwloginpop = "ui/gwloginpop/gwloginpop.asset",
    gwtotalrecharge = "ui/gwtotalrecharge/gwtotalrecharge.asset",
    gwshiningmarket = "ui/gwshiningmarket/gwshiningmarket.asset",
    alliancetrainicon = "ui/alliancetrainicon/alliancetrainicon.asset",
    gwluckywheel = "ui/gwluckywheel/gwluckywheel.asset",
    gwcarddraw = "ui/gwcarddrawactivity/gwcarddrawactivity.asset",
	stormminimap = "ui/gwstormminimap/stormminimap.asset",
    stormbuilding = "ui/gwstormuibuilding/desertbuilding.asset",
    stormbattlemap = "ui/gwstormbattlemap/gwstormbattlemap.asset",
    gwWarZoneDuel = "ui/gwwarzoneduel/gwwarzoneduel.asset",
    congressIcon = "ui/gwalliance/gwcomgress/congressicon.asset",
    gwOptionalWeekCard = "ui/gwoptionalweekcard/gwoptionalweekcard.asset",
    gwXyx = "ui/gw_xyx/gw_xyx.asset",
    leveliconasset = "ui/gw_commonminigame/leveliconasset.asset",
    gwbestleader = "ui/gwbestleader/gwbestleader.asset",
    monstersapproachingicon = "ui/monstersapproachingicon/monstersapproachingicon.asset", -- 怪物攻城相关
    gwClickLike = "ui/gwclickliking/gwclickliking.asset",
    gwSuperMonthGift = "ui/gwsupermonthgift/gwsupermonthgift.asset",-- 月月超值
    gwTodoSchedule = "ui/gwtodoschedule/gwtodoschedule.asset",-- 待办日程
    gwSurpriseBag = "ui/gw_surprisebag/surprisebag.asset",-- 惊喜盒
    gwLandRevivalIcon = "ui/gwlandrevival/gwlandrevivalicon.asset",--领地复兴
    gwAllianceRecordIcon = "ui/gwalliance/recordicon/alliancerecordicon.asset",--联盟记录
    gwAlliance = "ui/gwalliance/gwalliance.asset",
    
}

---@public 创建一个图集
---@param cardSpriteName string 图集名字
---通用，建议后续没必要每个图集都有一个构造函数
function CreateCardSpriteAsset(cardSpriteName)
    local path = cardSpriteAssetPath[cardSpriteName]
    if not path then
        log.Error(cardSpriteName, "cardSpriteAssetPath is nil")
        return
    end
    return sprite_asset.CSpriteAsset(path)
end

function CreateCardSpriteAssetWithPath(path)
    return sprite_asset.CSpriteAsset(path)
end

--function CreateDroneEquipmentAsset()
--    return CreateCardSpriteAsset("droneEquipmentIconAsset")
--end

function CreateCongressPositionIconAsset()
    return CreateCardSpriteAsset("congressIcon")
end

function CreateSoldierHeadAsset()
    return CreateCardSpriteAsset("soldierHeadAsset")
end

function CreateBuildingAsset()
    return CreateCardSpriteAsset("gwhomebuildingicon")
end

function CreateSurvivorHeadAsset()
    return CreateCardSpriteAsset("survivorHeadAsset")
end

function CreateBuildingBtnAsset()
    return CreateCardSpriteAsset("buildingbtnAsset")
end

function CreateTechnologyIconAsset()
    return CreateCardSpriteAsset("technologyIconAsset")
end

function CreateHomeButtonAsset()
    return CreateCardSpriteAsset("homebtnAsset")
end

function CreateGWSandmapAsset()
    return CreateCardSpriteAsset("gwsandmapAsset")
end

function GetRebirthSpaceAsset()
    return CreateCardSpriteAsset("rebirthspaceAssetPath")
end

function CreateShouchongAsset()
    return CreateCardSpriteAsset("shouchongAssetPath")
end

function CreateDecorateAsset()
    return CreateCardSpriteAsset("decorateAssetPath")
end

function CreateVipLvAsset()
    return CreateCardSpriteAsset("vipLvPath")
end

function CreateGiftAsset()
    return CreateCardSpriteAsset("giftAssetPath")
end

function CreateSpriteAsset()
    return CreateCardSpriteAsset("GoodsAssetPath")
end

function CreateHeroAsset()
    return CreateCardSpriteAsset("GoodsAssetPath")
end

function CreateActivityAsset()
    return CreateCardSpriteAsset("ActivityAssetPath")
end

function CreateBuffAsset()
    return CreateCardSpriteAsset("BuffAssetPath")
end

function CreateUnionTechnologyAsset()
    return CreateCardSpriteAsset("UnionTechnologyAssetPath")
end

function CreateLeagueAsset()
    return CreateCardSpriteAsset("LeagueAssetPath")
end

function CreateLeagueCompMapIconAsset()
    return CreateCardSpriteAsset("LeagueCompCellIconAssetPath")
end

function CreateTimeAreaTreeAsset()
    return CreateCardSpriteAsset("TimeAreaTree")
end

function CreateEquipAsset()
    return CreateCardSpriteAsset("EquipAssetPath")
end

function CreateUnlockGuideAsset()
    return CreateCardSpriteAsset("unlockGuidePath")
end

function CreateFestivalAsset()
    return CreateCardSpriteAsset("festivalPath")
end

function CreateEquipBigCardAsset()
    return CreateCardSpriteAsset("equipBigCardAssetPath")
end

function CreateChristmasFestivalAsset()
    return CreateCardSpriteAsset("christmasFestivalPath")
end

function CreateNormalActivityAsset()
    return CreateCardSpriteAsset("normalActivityPath")
end

function CreateSpecialBagAsset()
    return CreateCardSpriteAsset("specialBagPath")
end

function CreateWelfareAsset()
    return CreateCardSpriteAsset("welfarePath")
end

function CreateValueGiftAsset()
    return CreateCardSpriteAsset("valuegiftPath")
end

function CreatePeakIconAsset()
    return CreateCardSpriteAsset("peakIconPath")
end

function CreateSystemIconAsset()
    return CreateCardSpriteAsset("systemIconPath")
end

function CreateStarChestAsset()
    return CreateCardSpriteAsset("starChestPath")
end

function CreateLnstanceTypeAsset()
    return CreateCardSpriteAsset("lnstanceTypePath")
end

function CreateTaskChestAsset()
    return CreateCardSpriteAsset("taskChestPath")
end

function CreateHomelandAsset()
    return CreateCardSpriteAsset("homelandPath")
end

function CreateFactionWantedAsset()
    return CreateCardSpriteAsset("factionWantedPath")
end

function CreateRookieAsset()
    return CreateCardSpriteAsset("rookiePath")
end

function CreateChaoZhiLiBaoAsset()
    return CreateCardSpriteAsset("czlbAssetPath")
end

function CreateSummonedAsset()
    return CreateCardSpriteAsset("summonedPath")
end

function CreateArenaAsset()
    return CreateCardSpriteAsset("arenaAssetPath")
end

function CreatejingjichangAsset()
    return CreateCardSpriteAsset("jingjichangAssetPath")
end

function CreateTitleAsset()
    return CreateCardSpriteAsset("titleAssetPath")
end

function CreateNewHeroActivityAsset()
    return CreateCardSpriteAsset("newheroactivity")
end

function CreateSociatyMedalAsset()
    return CreateCardSpriteAsset("medal")
end

function CreateWarShipTechnologyAsset()
    return CreateCardSpriteAsset("warShipTechnology")
end

function CreateSpaceExplorationAsset()
    return CreateCardSpriteAsset("spaceExploration")
end

function CreateInstanceBossImg()
    return CreateCardSpriteAsset("instanceBossImg")
end

function CreateInstanceRecommend()
    return CreateCardSpriteAsset("recommendpack")
end

function CreateInstanceActivitybanner()
    return CreateCardSpriteAsset("activitybanner")
end

function CreateFleetExpeditionAsset()
    return CreateCardSpriteAsset("fleetexpedition")
end

function CreateCasualCommonHeadAsset()
    return CreateCardSpriteAsset("casualcommonhead")
end

function CreateCasualGameIconAsset()
    return CreateCardSpriteAsset("casuagameicon")
end

function CreateActorGroundAsset()
    return CreateCardSpriteAsset("actorBackground")
end

function CreateJewelryWorkShopAsset()
    return CreateCardSpriteAsset("jewelryworkshopAssetPath")
end

function CreateRenderImgAsset()
    return CreateCardSpriteAsset("renderImg")
end

function CreateSkinShareImageAsset()
    return CreateCardSpriteAsset("skinShareImage")
end

function CreateNationalFlagAsset()
    return CreateCardSpriteAsset("nationalFlagPath")
end

function CreateStarTempleAsset()
    return CreateCardSpriteAsset("starTempleAsset")
end
---@see 适用于特殊的去留白的很多行icon ui_message_box_multiple_ex.txt
function CreateMessageBoxExPath()
    return CreateCardSpriteAsset("cardMessageBoxIconPath")
end

function CreateUnionSlgRankImg()
    return CreateCardSpriteAsset("unionslgrankImg")
end


function CreateMinigameIconAsset()
    return CreateCardSpriteAsset("minigameIconAsset")
end

function CreatePickTheRouteAsset()
    return CreateCardSpriteAsset("pickTheRouteAsset")
end

function CreateDrawSugramanAsset()
    return CreateCardSpriteAsset("drawSugramanAsset")
end

function CreateHalloweenDressAsset()
    return CreateCardSpriteAsset("halloweenDressAsset")
end

function CreateReturnplayerAsset()
    return CreateCardSpriteAsset("returnplayerAssetPath")
end

function CreateTurnCardAsset()
    return CreateCardSpriteAsset("turncardAsset")
end

function CreateGWSandMinMap()
    return CreateCardSpriteAsset("gwsandminmap")
end

function CreateGWUICommon()
    return CreateCardSpriteAsset("gwuicommon")
end

function CreateGWMainAsset()
    return CreateCardSpriteAsset("gwmainslg")
end

function CreateGWSandMarkAsset()
    return CreateCardSpriteAsset("gwsandmarkAsset")
end

function CreateGWSandShareAsset()
    return CreateCardSpriteAsset("gwsandShareAsset")
end

function CreateGWRadarTaskIconAsset()
    return CreateCardSpriteAsset("radarTaskIconAsset")
end

function CreateGWRadarTaskDetailAsset()
    return CreateCardSpriteAsset("gwRadarTaskDetail")
end

function CreateGWAlliance()
    return CreateCardSpriteAsset("gwallianceachievement")
end

function CreateCampTrialAsset()
    return CreateCardSpriteAsset("campTrial")
end
function CreateGWSandMonsterIcon()
    return CreateCardSpriteAsset("gwsandmonstericon")
end

function CreateGWSandAttackAsset()
    return CreateCardSpriteAsset("gwsandattackAsset")
end

function CreateGWActivityCommonAsset()
    return CreateCardSpriteAsset("gwActivityCommon")
end
function CreateGWMenu()
    return CreateCardSpriteAsset("gwmenu")
end
function CreateGWTask()
    return CreateCardSpriteAsset("gwtask")
end

function CreateGWExplainImage()
    return CreateCardSpriteAsset("gwExplainImage")
end

function CreateGWRechargeIcon()
    return CreateCardSpriteAsset("gwRechargeIcon")
end

function CreateGWAllianceDuelIcon()
    return CreateCardSpriteAsset("gwAllianceDuelIcon")
end

function CreateGWPioneerActivityIcon()
    return CreateCardSpriteAsset("gwPioneerActivityIcon")
end 

function CreateGWWeekendCardIcon()
    return CreateCardSpriteAsset("gwWeekendCardIcon")
end

function CreateGWDailyIcon()
    return CreateCardSpriteAsset("gwdailygift")
end

function CreateGWShopIcon()
    return CreateCardSpriteAsset("gwshop")
end

function CreateGWDiamondIcon() 
    return CreateCardSpriteAsset("gwDiamondGift")
end

function CreateGWLimitIcon() 
    return CreateCardSpriteAsset("gwLimitGift")
end
function CreateGWLoginPop() 
    return CreateCardSpriteAsset("gwloginpop")
end
function CreateGWTotalRecharge() 
    return CreateCardSpriteAsset("gwtotalrecharge")
end
function CreateGWShiningMarket() 
    return CreateCardSpriteAsset("gwshiningmarket")
end
function CreateAllianceTrainIconAsset()
    return CreateCardSpriteAsset("alliancetrainicon")
end
function CreateGWLuckyWheelAsset()
    return CreateCardSpriteAsset("gwluckywheel")
end

function CreateGWCardDrawAsset() 
    return CreateCardSpriteAsset("gwcarddraw")
end

function CreateStormMiniMapAsset() 
    return CreateCardSpriteAsset("stormminimap")
end

function CreateStormBuildingAsset() 
    return CreateCardSpriteAsset("stormbuilding")
end

function CreateStormBattleMapAsset() 
    return CreateCardSpriteAsset("stormbattlemap")
end

function CreateGwWarZoneDuelAsset()
    return CreateCardSpriteAsset("gwWarZoneDuel")
end

function CreateGwOptionalWeekCardAsset()
    return CreateCardSpriteAsset("gwOptionalWeekCard")
end

function CreateXyxCardAsset()
    return CreateCardSpriteAsset("gwXyx")
end

function CreateLevelIconAsset()
    return CreateCardSpriteAsset("leveliconasset")
end

function CreateGWBestLeader()
    return CreateCardSpriteAsset("gwbestleader")
end
function CreateMonsterSapproachingiconAsset()
    return CreateCardSpriteAsset("monstersapproachingicon")
end

function CreateGWClickLikeAsset()
    return CreateCardSpriteAsset("gwClickLike")
end

function CreateGWSuperMonthGiftAsset()
    return CreateCardSpriteAsset("gwSuperMonthGift")
end

function CreateGwTodoScheduleAsset()
    return CreateCardSpriteAsset("gwTodoSchedule")
end 

function CreateGwSurpriseBagAsset()
    return CreateCardSpriteAsset("gwSurpriseBag")
end

function CreateGWLandRevivalIconAsset()
    return CreateCardSpriteAsset("gwLandRevivalIcon")
end 

function CreateGWAllianceRecordIconAsset()
    return CreateCardSpriteAsset("gwAllianceRecordIcon")
end 

function CreateGWAllianceAsset()
    return CreateCardSpriteAsset("gwAlliance")
end 