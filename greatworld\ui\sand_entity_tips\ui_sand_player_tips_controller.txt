---
--- Created by xby.
--- DateTime: 2024/6/27
--- Desc 玩家点击弹窗 ctrl
---

local require = require
local string = string

local event_personalInfo = require "event_personalInfo"
local flow_text = require "flow_text"
local controller_base = require "controller_base"
local windowMgr = require "ui_window_mgr"

local sand_ui_event_define = require "sand_ui_event_define"
local sand_ui_data = require "sand_ui_data"
local sand_mark_data = require "sand_mark_data"
local sandbox_ui_mgr = require "sandbox_ui_mgr"
local player_mgr = require "player_mgr"
local mgr_personalInfo = require "mgr_personalInfo"
local gw_sand_preview_mgr = require "gw_sand_preview_mgr"
local click_liking_mgr = require "click_liking_mgr"
local newclass = newclass

module("ui_sand_player_tips_controller")

local controller = nil
local UISandPlayerController = newclass("ui_sand_player_tips_controller", controller_base)

--[[窗口初始化]]
function UISandPlayerController:Init(view_name, controller_name, data)
    self.__base.Init(self, view_name, controller_name)
    self.entity = data
    local curMarkData = sand_mark_data.FindMarkExistByPos(self.entity.pos)
    local isFavorite
    if curMarkData then
        isFavorite = curMarkData.nType ~= 4
    end
    self:TriggerUIEvent("SetFavorite", isFavorite)
    --更新位置
    self:TriggerUIEvent("SetFollowPosition", self.entity)
    local isShowLike = self.entity:GetRoleId() ~= player_mgr.GetPlayerRoleID()
    self:TriggerUIEvent("SetLikeBtnShow", isShowLike)
    self:SandDetailUpdate(data)
    self:SetScoutData(data)
end

function UISandPlayerController:OnShow()
    self.__base.OnShow(self)
end
function UISandPlayerController:Close()
    self.__base.Close(self)
    if self.popID then
        gw_sand_preview_mgr.RemovePreviewObject(self.popID)
    end
    controller = nil
end

function UISandPlayerController:AutoSubscribeEvents()
    -- local PraiseSuccess = function()
    --     local lang = require "lang"
    --     flow_text.Add(lang.Get(650096)) --点赞成功
    -- end
    -- self:RegisterEvent(event_personalInfo.ROLE_PRAISE_UPDATE_MSG, PraiseSuccess)

    self:RegisterEvent(sand_ui_event_define.REFRESH_ENTITY_TIP, function()
        self:TriggerUIEvent("SetFollowPosition", self.entity)
    end)
end

function UISandPlayerController:AutoUnsubscribeEvents()

end

function GetShortAllianceName()
    local alliance_data = require "alliance_data"
    local userdata = alliance_data.GetUserAllianceData()
    return userdata and userdata.shortName and string.format("[%s]", userdata.shortName) or ""
end

function UISandPlayerController:SetScoutData(entity)
    local data = nil
    local gw_common_util = require "gw_common_util"
    if gw_common_util.CheckInSand_Sand() then
        if entity:GetDetectTimeStamp() > 0 then
            data = {}
            data.unionName = GetShortAllianceName()
            data.timeStamp = entity:GetDetectTimeStamp()
            data.rewards = entity:GetDetectRewards()
        end
    end
    self:TriggerUIEvent("SetScoutData", data)
end

function UISandPlayerController:SandDetailUpdate(entity)
    if entity == nil then
        return
    end

    local playerData = {
        playerName = entity:GetUnionShortAndRoleNameAndServerID(),
        allianceName = entity:GetUnionShortAndUnionNameAndServerID(),
        lv = entity:GetBaseLv(),
        killNum = entity:GetBaseKillNum(),
        power = entity:GetBasePower(),
        allianceId = entity:GetBaseUnionID(),
        faceID = string.IsNullOrEmpty(entity:GetFaceStr()) and entity:GetFaceId() or entity:GetFaceStr(),
        frameID = entity:GetFrameId(),
        playerLevel = entity:GetPlayerLevel()
    }
    self:TriggerUIEvent("OnRefreshData", playerData)
end

function UISandPlayerController:OnBtn_btnInfoClickedProxy()
    --打开角色详情界面
    mgr_personalInfo.ShowRoleInfoView(self.entity:GetRoleId())
end

function UISandPlayerController:OnBtn_btnComgressClickedProxy()
    local data = {
        init = true,
        roleId = self.entity:GetRoleId(),
        lv = self.entity:GetBaseLv(),
    }
    windowMgr:ShowModule("ui_mini_congress_panel", nil, nil, data)
end

function UISandPlayerController:OnBtn_btnShareClickedProxy()
    --分享
    local ShareUtil = require "share_util"
    sandbox_ui_mgr.OnGW_CLICK_EMPTY()
    ShareUtil.OpenShareWithEntity(self.entity)
end

function UISandPlayerController:OnBtn_btnFavoriteClickedProxy()
    --收藏
    sandbox_ui_mgr.OnGW_CLICK_EMPTY()
    local data = {
        entity = self.entity,
        type = 2        --1:联盟 2:玩家
    }
    windowMgr:ShowModule("ui_sand_mark_tip", nil, nil, data)

end

function UISandPlayerController:OnBtn_btnTagClickedProxy()
    sandbox_ui_mgr.OnGW_CLICK_EMPTY()
    local data = {
        entity = self.entity,
        type = 1        --1:联盟 2:玩家
    }
    windowMgr:ShowModule("ui_sand_mark_tip", nil, nil, data)
end

function UISandPlayerController:OnBtn_LikeBtnClickedProxy()
    --点赞 需求修改 
    local roleId = self.entity:GetRoleId()
    local role_pb = require "role_pb"
    -- local log = require "log"
    -- log.Warning("OnBtn_LikeBtnClickedProxy",roleId)
    local click_liking_data = require "click_liking_data"
    click_liking_data.SetPos(self.entity.pos)
    mgr_personalInfo.PraiseRole(roleId, role_pb.enLikeSysType_SxMainCity)
    sandbox_ui_mgr.OnGW_CLICK_EMPTY()
end

--region ModuleFunction 
function Show()
    if controller == nil then
        controller = UISandPlayerController.new()
    end
    return controller
end
--endregion