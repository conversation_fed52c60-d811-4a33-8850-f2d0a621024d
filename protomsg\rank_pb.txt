-- Generated By protoc-gen-lua Do not Edit
local protobuf = require "protobuf"
local V=protobuf.EnumValueDescriptor
local F=protobuf.FieldDescriptor
local D=protobuf.Descriptor
local E=protobuf.EnumDescriptor
local M=protobuf.Message
local prop_pb=require("prop_pb")
local error_code_pb=require("error_code_pb")
local common_new_pb=require("common_new_pb")
module('rank_pb')


V1M=V(4,"enRankZero",0,0)
V2M=V(4,"enStage",1,1)
V3M=V(4,"enCamp",2,2)
V4M=V(4,"enActivityRank",3,3)
V5M=V(4,"enArenaRank",4,4)
V6M=V(4,"enLeagueHelp",5,5)
V7M=V(4,"enLeagueTFlower",6,6)
V8M=V(4,"enRankMax",7,7)
E1M=E(3,"EnRankingType",".CSMsg.EnRankingType")
V9M=V(4,"enStageZero",0,0)
V10M=V(4,"enWeiMian",1,1)
V11M=V(4,"enXingJi",2,2)
V12M=V(4,"enYiLuo",3,3)
V13M=V(4,"enRankFactionType1",4,4)
V14M=V(4,"enRankFactionType2",5,5)
V15M=V(4,"enRankFactionType3",6,6)
V16M=V(4,"enRankFactionType4",7,7)
V17M=V(4,"enChinaRed",8,8)
E2M=E(3,"EnStageType",".CSMsg.EnStageType")
V18M=V(4,"ActivityRank_Min",0,0)
V19M=V(4,"ActivityRank_ThanksgivingTurkeyScore",1,1)
V20M=V(4,"ActivityRank_QingRenFestival",2,4)
V21M=V(4,"ActivityRank_Five",3,5)
V22M=V(4,"ActivityRank_Six",4,6)
V23M=V(4,"ActivityRank_Seven",5,7)
V24M=V(4,"ActivityRank_BossTrial",6,8)
V25M=V(4,"ActivityRank_NewBossTrial",7,9)
V26M=V(4,"ActivityRank_NewBossTrial2",8,10)
V27M=V(4,"ActivityRank_MidAutumnBossTrial",9,11)
V28M=V(4,"ActivityRank_BossTrial2",10,13)
V29M=V(4,"ActivityRank_Max",11,1000)
E3M=E(3,"ActivityRankType",".CSMsg.ActivityRankType")
V30M=V(4,"LeagueHelpRank_WeekRank",0,0)
E4M=E(3,"LeagueHelpRankType",".CSMsg.LeagueHelpRankType")
V31M=V(4,"LeagueTreasure_Flower",0,0)
E5M=E(3,"LeagueTreasureType",".CSMsg.LeagueTreasureType")
V32M=V(4,"RankingReward_unkown",0,0)
V33M=V(4,"RankingReward_WorldBoss",1,1)
V34M=V(4,"RankingReward_ArmsRace",2,2)
V35M=V(4,"RankingReward_Commander",3,3)
V36M=V(4,"RankingReward_ZombieComing",4,4)
V37M=V(4,"RankingReward_ZoneBattleDuel",5,8)
V38M=V(4,"RankingReward_PuzzleGameMiniLevel",6,11)
V39M=V(4,"RankingReward_StormArena",7,12)
V40M=V(4,"RankingReward_ViolentZombieLeader",8,13)
E6M=E(3,"RankingRewardType",".CSMsg.RankingRewardType")
F1D=F(2,"nPeriod",".CSMsg.RankReward.nPeriod",1,0,2,false,0,13,3)
F2D=F(2,"nEndTime",".CSMsg.RankReward.nEndTime",2,1,2,false,0,13,3)
F3D=F(2,"nRankReward",".CSMsg.RankReward.nRankReward",3,2,2,false,0,13,3)
F4D=F(2,"nWorldID",".CSMsg.RankReward.nWorldID",4,3,2,false,0,13,3)
M1G=D(1,"RankReward",".CSMsg.RankReward",false,{},{},nil,{})
F5D=F(2,"nRankType",".CSMsg.RankPeriodRecord.nRankType",1,0,2,false,nil,14,8)
F6D=F(2,"nSubType",".CSMsg.RankPeriodRecord.nSubType",2,1,2,false,0,13,3)
F7D=F(2,"nPeriodBeginTime",".CSMsg.RankPeriodRecord.nPeriodBeginTime",3,2,2,false,0,13,3)
F8D=F(2,"nRankArr",".CSMsg.RankPeriodRecord.nRankArr",4,3,3,false,{},11,10)
M2G=D(1,"RankPeriodRecord",".CSMsg.RankPeriodRecord",false,{},{},nil,{})
F9D=F(2,"enType",".CSMsg.TMSG_RANK_DETAIL_REQ.enType",1,0,2,false,nil,14,8)
F10D=F(2,"enSatge",".CSMsg.TMSG_RANK_DETAIL_REQ.enSatge",2,1,1,false,nil,14,8)
F11D=F(2,"enCamp",".CSMsg.TMSG_RANK_DETAIL_REQ.enCamp",3,2,1,false,nil,14,8)
F12D=F(2,"enActivity",".CSMsg.TMSG_RANK_DETAIL_REQ.enActivity",4,3,1,false,nil,14,8)
F13D=F(2,"enLeagueHelp",".CSMsg.TMSG_RANK_DETAIL_REQ.enLeagueHelp",5,4,1,false,nil,14,8)
M4G=D(1,"TMSG_RANK_DETAIL_REQ",".CSMsg.TMSG_RANK_DETAIL_REQ",false,{},{},nil,{})
F14D=F(2,"enRankType",".CSMsg.TMSG_RANK_DETAIL_RSP.enRankType",1,0,2,false,nil,14,8)
F15D=F(2,"enStageType",".CSMsg.TMSG_RANK_DETAIL_RSP.enStageType",2,1,1,false,nil,14,8)
F16D=F(2,"enCampType",".CSMsg.TMSG_RANK_DETAIL_RSP.enCampType",3,2,1,false,nil,14,8)
F17D=F(2,"enActivity",".CSMsg.TMSG_RANK_DETAIL_RSP.enActivity",4,3,1,false,nil,14,8)
F18D=F(2,"enLeagueHelp",".CSMsg.TMSG_RANK_DETAIL_RSP.enLeagueHelp",5,4,1,false,nil,14,8)
F19D=F(2,"enErrCode",".CSMsg.TMSG_RANK_DETAIL_RSP.enErrCode",6,5,2,false,nil,14,8)
M9G=D(1,"TMSG_RANK_DETAIL_RSP",".CSMsg.TMSG_RANK_DETAIL_RSP",false,{},{},nil,{})
F20D=F(2,"palID",".CSMsg.ArenaPalInfoMini.palID",1,0,2,false,0,13,3)
F21D=F(2,"level",".CSMsg.ArenaPalInfoMini.level",2,1,2,false,0,13,3)
F22D=F(2,"starLevel",".CSMsg.ArenaPalInfoMini.starLevel",3,2,2,false,0,13,3)
F23D=F(2,"rowNo",".CSMsg.ArenaPalInfoMini.rowNo",4,3,2,false,0,13,3)
F24D=F(2,"colNo",".CSMsg.ArenaPalInfoMini.colNo",5,4,2,false,0,13,3)
F25D=F(2,"exclusivelv",".CSMsg.ArenaPalInfoMini.exclusivelv",6,5,2,false,0,13,3)
F26D=F(2,"decorateCfgIDs",".CSMsg.ArenaPalInfoMini.decorateCfgIDs",7,6,3,false,{},13,3)
M11G=D(1,"ArenaPalInfoMini",".CSMsg.ArenaPalInfoMini",false,{},{},nil,{})
F27D=F(2,"palInfos",".CSMsg.ArenaPalInfos.palInfos",1,0,3,false,{},11,10)
M12G=D(1,"ArenaPalInfos",".CSMsg.ArenaPalInfos",false,{},{},nil,{})
M13G=D(1,"TMSG_HISTORY_PALINFOS_REQ",".CSMsg.TMSG_HISTORY_PALINFOS_REQ",false,{},{},{},{})
F28D=F(2,"enErrCode",".CSMsg.TMSG_HISTORY_PALINFOS_RSP.enErrCode",1,0,2,false,nil,14,8)
F29D=F(2,"reqData",".CSMsg.TMSG_HISTORY_PALINFOS_RSP.reqData",2,1,3,false,{},11,10)
M14G=D(1,"TMSG_HISTORY_PALINFOS_RSP",".CSMsg.TMSG_HISTORY_PALINFOS_RSP",false,{},{},nil,{})
F30D=F(2,"reqData",".CSMsg.TMSG_HISTORY_PALINFOS_NTF.reqData",1,0,3,false,{},11,10)
M16G=D(1,"TMSG_HISTORY_PALINFOS_NTF",".CSMsg.TMSG_HISTORY_PALINFOS_NTF",false,{},{},nil,{})
F31D=F(2,"roleID",".CSMsg.PalInfoData.roleID",1,0,2,false,0,13,3)
F32D=F(2,"szBattleID",".CSMsg.PalInfoData.szBattleID",2,1,2,false,"",9,9)
F33D=F(2,"palInfo",".CSMsg.PalInfoData.palInfo",3,2,1,false,nil,11,10)
F34D=F(2,"damage",".CSMsg.PalInfoData.damage",4,3,1,false,0,4,4)
M15G=D(1,"PalInfoData",".CSMsg.PalInfoData",false,{},{},nil,{})
F35D=F(2,"reqData",".CSMsg.TMSG_RANK_DETAIL_PALINFO_REQ.reqData",1,0,3,false,{},11,10)
M17G=D(1,"TMSG_RANK_DETAIL_PALINFO_REQ",".CSMsg.TMSG_RANK_DETAIL_PALINFO_REQ",false,{},{},nil,{})
F36D=F(2,"enErrCode",".CSMsg.TMSG_RANK_DETAIL_PALINFO_RSP.enErrCode",1,0,2,false,nil,14,8)
F37D=F(2,"reqData",".CSMsg.TMSG_RANK_DETAIL_PALINFO_RSP.reqData",2,1,3,false,{},11,10)
M18G=D(1,"TMSG_RANK_DETAIL_PALINFO_RSP",".CSMsg.TMSG_RANK_DETAIL_PALINFO_RSP",false,{},{},nil,{})
F38D=F(2,"reqData",".CSMsg.TMSG_RANK_DETAIL_PALINFO_NTF.reqData",2,0,3,false,{},11,10)
M19G=D(1,"TMSG_RANK_DETAIL_PALINFO_NTF",".CSMsg.TMSG_RANK_DETAIL_PALINFO_NTF",false,{},{},nil,{})
F39D=F(2,"roleID",".CSMsg.TDetailRank.roleID",1,0,2,false,0,13,3)
F40D=F(2,"roleLv",".CSMsg.TDetailRank.roleLv",2,1,2,false,0,13,3)
F41D=F(2,"faceID",".CSMsg.TDetailRank.faceID",3,2,2,false,0,13,3)
F42D=F(2,"name",".CSMsg.TDetailRank.name",4,3,2,false,"",9,9)
F43D=F(2,"time",".CSMsg.TDetailRank.time",5,4,1,false,0,13,3)
F44D=F(2,"mapID",".CSMsg.TDetailRank.mapID",6,5,1,false,0,13,3)
F45D=F(2,"rankKey",".CSMsg.TDetailRank.rankKey",7,6,1,false,0,4,4)
F46D=F(2,"rank",".CSMsg.TDetailRank.rank",8,7,1,false,0,13,3)
F47D=F(2,"frameID",".CSMsg.TDetailRank.frameID",9,8,1,false,0,13,3)
F48D=F(2,"szBattleID",".CSMsg.TDetailRank.szBattleID",10,9,1,false,"",9,9)
F49D=F(2,"faceStr",".CSMsg.TDetailRank.faceStr",11,10,1,false,"",9,9)
M20G=D(1,"TDetailRank",".CSMsg.TDetailRank",false,{},{},nil,{})
F50D=F(2,"enRankType",".CSMsg.TMSG_RANK_DETAIL_NTF.enRankType",1,0,2,false,nil,14,8)
F51D=F(2,"enStageType",".CSMsg.TMSG_RANK_DETAIL_NTF.enStageType",2,1,1,false,nil,14,8)
F52D=F(2,"enCampType",".CSMsg.TMSG_RANK_DETAIL_NTF.enCampType",3,2,1,false,nil,14,8)
F53D=F(2,"enActivity",".CSMsg.TMSG_RANK_DETAIL_NTF.enActivity",4,3,1,false,nil,14,8)
F54D=F(2,"enLeagueHelp",".CSMsg.TMSG_RANK_DETAIL_NTF.enLeagueHelp",5,4,1,false,nil,14,8)
F55D=F(2,"stDetail",".CSMsg.TMSG_RANK_DETAIL_NTF.stDetail",6,5,3,false,{},11,10)
M21G=D(1,"TMSG_RANK_DETAIL_NTF",".CSMsg.TMSG_RANK_DETAIL_NTF",false,{},{},nil,{})
F56D=F(2,"enRankType",".CSMsg.TMSG_RANK_TOP_REQ.enRankType",1,0,2,false,nil,14,8)
M22G=D(1,"TMSG_RANK_TOP_REQ",".CSMsg.TMSG_RANK_TOP_REQ",false,{},{},nil,{})
F57D=F(2,"enRankType",".CSMsg.TMSG_RANK_TOP_RSP.enRankType",1,0,2,false,nil,14,8)
F58D=F(2,"enErrCode",".CSMsg.TMSG_RANK_TOP_RSP.enErrCode",2,1,2,false,nil,14,8)
M23G=D(1,"TMSG_RANK_TOP_RSP",".CSMsg.TMSG_RANK_TOP_RSP",false,{},{},nil,{})
F59D=F(2,"stageType",".CSMsg.Top_Rank_RoleInfo.stageType",1,0,1,false,nil,14,8)
F60D=F(2,"campType",".CSMsg.Top_Rank_RoleInfo.campType",2,1,1,false,nil,14,8)
F61D=F(2,"stDetail",".CSMsg.Top_Rank_RoleInfo.stDetail",3,2,2,false,nil,11,10)
M24G=D(1,"Top_Rank_RoleInfo",".CSMsg.Top_Rank_RoleInfo",false,{},{},nil,{})
F62D=F(2,"enRankType",".CSMsg.TMSG_RANK_TOP_NTF.enRankType",1,0,2,false,nil,14,8)
F63D=F(2,"stDetail",".CSMsg.TMSG_RANK_TOP_NTF.stDetail",2,1,3,false,{},11,10)
M25G=D(1,"TMSG_RANK_TOP_NTF",".CSMsg.TMSG_RANK_TOP_NTF",false,{},{},nil,{})
F64D=F(2,"enRankType",".CSMsg.TMSG_RANK_GAIN_REQ.enRankType",1,0,2,false,nil,14,8)
F65D=F(2,"nTaskID",".CSMsg.TMSG_RANK_GAIN_REQ.nTaskID",2,1,2,false,0,5,1)
M26G=D(1,"TMSG_RANK_GAIN_REQ",".CSMsg.TMSG_RANK_GAIN_REQ",false,{},{},nil,{})
F66D=F(2,"enRankType",".CSMsg.TMSG_RANK_GAIN_RSP.enRankType",1,0,2,false,nil,14,8)
F67D=F(2,"nTaskID",".CSMsg.TMSG_RANK_GAIN_RSP.nTaskID",2,1,2,false,0,5,1)
F68D=F(2,"enErrCode",".CSMsg.TMSG_RANK_GAIN_RSP.enErrCode",3,2,2,false,nil,14,8)
M27G=D(1,"TMSG_RANK_GAIN_RSP",".CSMsg.TMSG_RANK_GAIN_RSP",false,{},{},nil,{})
F69D=F(2,"nTaskID",".CSMsg.TMSG_RANK_ACHV_TOP5_REQ.nTaskID",1,0,3,false,{},5,1)
M28G=D(1,"TMSG_RANK_ACHV_TOP5_REQ",".CSMsg.TMSG_RANK_ACHV_TOP5_REQ",false,{},{},nil,{})
F70D=F(2,"nTaskID",".CSMsg.Achv_Role.nTaskID",1,0,2,false,0,5,1)
F71D=F(2,"stDetail",".CSMsg.Achv_Role.stDetail",2,1,3,false,{},11,10)
M29G=D(1,"Achv_Role",".CSMsg.Achv_Role",false,{},{},nil,{})
F72D=F(2,"enErrCode",".CSMsg.TMSG_RANK_ACHV_TOP5_NTF.enErrCode",1,0,2,false,nil,14,8)
F73D=F(2,"nTaskRole",".CSMsg.TMSG_RANK_ACHV_TOP5_NTF.nTaskRole",2,1,3,false,{},11,10)
M30G=D(1,"TMSG_RANK_ACHV_TOP5_NTF",".CSMsg.TMSG_RANK_ACHV_TOP5_NTF",false,{},{},nil,{})
F74D=F(2,"taskID",".CSMsg.TMSG_RANK_ACHV_REACH_NTF.taskID",1,0,2,false,0,13,3)
F75D=F(2,"stDetail",".CSMsg.TMSG_RANK_ACHV_REACH_NTF.stDetail",2,1,2,false,nil,11,10)
M31G=D(1,"TMSG_RANK_ACHV_REACH_NTF",".CSMsg.TMSG_RANK_ACHV_REACH_NTF",false,{},{},nil,{})

E1M.values = {V1M,V2M,V3M,V4M,V5M,V6M,V7M,V8M}
E2M.values = {V9M,V10M,V11M,V12M,V13M,V14M,V15M,V16M,V17M}
E3M.values = {V18M,V19M,V20M,V21M,V22M,V23M,V24M,V25M,V26M,V27M,V28M,V29M}
E4M.values = {V30M}
E5M.values = {V31M}
E6M.values = {V32M,V33M,V34M,V35M,V36M,V37M,V38M,V39M,V40M}
M1G.fields={F1D, F2D, F3D, F4D}
F5D.enum_type=M3G
F8D.message_type=M1G
M2G.fields={F5D, F6D, F7D, F8D}
F9D.enum_type=M3G
F10D.enum_type=M5G
F11D.enum_type=prop_pb.E20M
F12D.enum_type=M7G
F13D.enum_type=M8G
M4G.fields={F9D, F10D, F11D, F12D, F13D}
F14D.enum_type=M3G
F15D.enum_type=M5G
F16D.enum_type=prop_pb.E20M
F17D.enum_type=M7G
F18D.enum_type=M8G
F19D.enum_type=error_code_pb.E1M
M9G.fields={F14D, F15D, F16D, F17D, F18D, F19D}
M11G.fields={F20D, F21D, F22D, F23D, F24D, F25D, F26D}
F27D.message_type=M11G
M12G.fields={F27D}
F28D.enum_type=error_code_pb.E1M
F29D.message_type=M15G
M14G.fields={F28D, F29D}
F30D.message_type=M15G
M16G.fields={F30D}
F33D.message_type=M12G
M15G.fields={F31D, F32D, F33D, F34D}
F35D.message_type=M15G
M17G.fields={F35D}
F36D.enum_type=error_code_pb.E1M
F37D.message_type=M15G
M18G.fields={F36D, F37D}
F38D.message_type=M15G
M19G.fields={F38D}
M20G.fields={F39D, F40D, F41D, F42D, F43D, F44D, F45D, F46D, F47D, F48D, F49D}
F50D.enum_type=M3G
F51D.enum_type=M5G
F52D.enum_type=prop_pb.E20M
F53D.enum_type=M7G
F54D.enum_type=M8G
F55D.message_type=M20G
M21G.fields={F50D, F51D, F52D, F53D, F54D, F55D}
F56D.enum_type=M3G
M22G.fields={F56D}
F57D.enum_type=M3G
F58D.enum_type=error_code_pb.E1M
M23G.fields={F57D, F58D}
F59D.enum_type=M5G
F60D.enum_type=prop_pb.E20M
F61D.message_type=M20G
M24G.fields={F59D, F60D, F61D}
F62D.enum_type=M3G
F63D.message_type=M24G
M25G.fields={F62D, F63D}
F64D.enum_type=M3G
M26G.fields={F64D, F65D}
F66D.enum_type=M3G
F68D.enum_type=error_code_pb.E1M
M27G.fields={F66D, F67D, F68D}
M28G.fields={F69D}
F71D.message_type=M20G
M29G.fields={F70D, F71D}
F72D.enum_type=error_code_pb.E1M
F73D.message_type=M29G
M30G.fields={F72D, F73D}
F75D.message_type=M20G
M31G.fields={F74D, F75D}

Achv_Role =M(M29G)
ActivityRank_BossTrial = 8
ActivityRank_BossTrial2 = 13
ActivityRank_Five = 5
ActivityRank_Max = 1000
ActivityRank_MidAutumnBossTrial = 11
ActivityRank_Min = 0
ActivityRank_NewBossTrial = 9
ActivityRank_NewBossTrial2 = 10
ActivityRank_QingRenFestival = 4
ActivityRank_Seven = 7
ActivityRank_Six = 6
ActivityRank_ThanksgivingTurkeyScore = 1
ArenaPalInfoMini =M(M11G)
ArenaPalInfos =M(M12G)
LeagueHelpRank_WeekRank = 0
LeagueTreasure_Flower = 0
PalInfoData =M(M15G)
RankPeriodRecord =M(M2G)
RankReward =M(M1G)
RankingReward_ArmsRace = 2
RankingReward_Commander = 3
RankingReward_PuzzleGameMiniLevel = 11
RankingReward_StormArena = 12
RankingReward_ViolentZombieLeader = 13
RankingReward_WorldBoss = 1
RankingReward_ZombieComing = 4
RankingReward_ZoneBattleDuel = 8
RankingReward_unkown = 0
TDetailRank =M(M20G)
TMSG_HISTORY_PALINFOS_NTF =M(M16G)
TMSG_HISTORY_PALINFOS_REQ =M(M13G)
TMSG_HISTORY_PALINFOS_RSP =M(M14G)
TMSG_RANK_ACHV_REACH_NTF =M(M31G)
TMSG_RANK_ACHV_TOP5_NTF =M(M30G)
TMSG_RANK_ACHV_TOP5_REQ =M(M28G)
TMSG_RANK_DETAIL_NTF =M(M21G)
TMSG_RANK_DETAIL_PALINFO_NTF =M(M19G)
TMSG_RANK_DETAIL_PALINFO_REQ =M(M17G)
TMSG_RANK_DETAIL_PALINFO_RSP =M(M18G)
TMSG_RANK_DETAIL_REQ =M(M4G)
TMSG_RANK_DETAIL_RSP =M(M9G)
TMSG_RANK_GAIN_REQ =M(M26G)
TMSG_RANK_GAIN_RSP =M(M27G)
TMSG_RANK_TOP_NTF =M(M25G)
TMSG_RANK_TOP_REQ =M(M22G)
TMSG_RANK_TOP_RSP =M(M23G)
Top_Rank_RoleInfo =M(M24G)
enActivityRank = 3
enArenaRank = 4
enCamp = 2
enChinaRed = 8
enLeagueHelp = 5
enLeagueTFlower = 6
enRankFactionType1 = 4
enRankFactionType2 = 5
enRankFactionType3 = 6
enRankFactionType4 = 7
enRankMax = 7
enRankZero = 0
enStage = 1
enStageZero = 0
enWeiMian = 1
enXingJi = 2
enYiLuo = 3

