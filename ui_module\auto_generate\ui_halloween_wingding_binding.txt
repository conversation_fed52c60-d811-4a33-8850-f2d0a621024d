local require = require
local typeof = typeof

local SpriteSwitcher = CS.War.UI.SpriteSwitcher
local Button = CS.UnityEngine.UI.Button
local Text = CS.UnityEngine.UI.Text
local Slider = CS.UnityEngine.UI.Slider
local RectTransform = CS.UnityEngine.RectTransform


module("ui_halloween_wingding_binding")

UIPath = "ui/prefabs/gw/activity/halloweenactivity/wingding/uihalloweenwingding.prefab"

WidgetTable ={
	ss_pumpkin5 = { path = "middleArea/ss&btn_pumpkin5", type = SpriteSwitcher, },
	btn_pumpkin5 = { path = "middleArea/ss&btn_pumpkin5", type = Button, event_name = "OnBtnPumpkin5ClickedProxy"},
	ss_pumpkin4 = { path = "middleArea/ss&btn_pumpkin4", type = SpriteSwitcher, },
	btn_pumpkin4 = { path = "middleArea/ss&btn_pumpkin4", type = Button, event_name = "OnBtnPumpkin4ClickedProxy"},
	ss_pumpkin3 = { path = "middleArea/ss&btn_pumpkin3", type = SpriteSwitcher, },
	btn_pumpkin3 = { path = "middleArea/ss&btn_pumpkin3", type = Button, event_name = "OnBtnPumpkin3ClickedProxy"},
	ss_pumpkin2 = { path = "middleArea/ss&btn_pumpkin2", type = SpriteSwitcher, },
	btn_pumpkin2 = { path = "middleArea/ss&btn_pumpkin2", type = Button, event_name = "OnBtnPumpkin2ClickedProxy"},
	ss_pumpkin1 = { path = "middleArea/ss&btn_pumpkin1", type = SpriteSwitcher, },
	btn_pumpkin1 = { path = "middleArea/ss&btn_pumpkin1", type = Button, event_name = "OnBtnPumpkin1ClickedProxy"},
	btn_submit = { path = "bottomArea/btn_submit", type = Button, event_name = "OnBtnSubmitClickedProxy"},
	txt_time = { path = "topArea/bgTime/txt_time", type = Text, },
	sld_leftSlider = { path = "topArea/sld_leftSlider", type = Slider, value_changed_event = "OnSliderLeftSliderValueChange"},
	txt_sldNum = { path = "topArea/sld_leftSlider/txt_sldNum", type = Text, },
	btn_gift = { path = "topArea/sld_leftSlider/btn_gift", type = Button, event_name = "OnBtnGiftClickedProxy"},
	txt_level = { path = "topArea/txt_level", type = Text, },
	txt_unLockTime = { path = "topArea/txt_unLockTime", type = Text, },
	btn_rank = { path = "topArea/btn_rank", type = Button, event_name = "OnBtnRankClickedProxy"},
	rtf_rankBubble = { path = "topArea/btn_rank/rtf_rankBubble", type = RectTransform, },
	rtf_rankRewardParent = { path = "topArea/btn_rank/rtf_rankBubble/rtf_rankRewardParent", type = RectTransform, },
	btn_rankBubble = { path = "topArea/btn_rank/rtf_rankBubble/btn_rankBubble", type = Button, event_name = "OnBtnRankBubbleClickedProxy"},
	btn_party = { path = "topArea/btn_party", type = Button, event_name = "OnBtnPartyClickedProxy"},
	rtf_arrow = { path = "topArea/rtf_arrow", type = RectTransform, },
}
