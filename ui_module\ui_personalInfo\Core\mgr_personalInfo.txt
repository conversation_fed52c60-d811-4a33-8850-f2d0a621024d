---
--- Created by: yuan<PERSON>.
--- DateTime: 2024/8/27.
--- Desc: player_mgr的子模块, 个人信息功能管理器,因为项目中的数据总是零零散散的分散在各个地方，把他组装起来一起放在这里
--- todo 如果后续有事件把一些分散在各个模块的数据和逻辑集合在这个模块，并且创建一个此模块的Data
---

local require = require
local ipairs = ipairs
local tostring = tostring
local PlayerPrefs = CS.UnityEngine.PlayerPrefs
local tonumber = tonumber
local string = string

local event = require "event"
local lang = require "lang"
local const_personalInfo = require "const_personalInfo"
local data_personalInfo = require "data_personalInfo"
local gameScheme = require "game_scheme"
local ui_window_mgr = require "ui_window_mgr"
local flow_text = require "flow_text"

local topic_pb = require "topic_pb"
local prop_pb = require "prop_pb"
local GWG = GWG
local event_personalInfo = require "event_personalInfo"

module("mgr_personalInfo")

local isInit = false
local isRegister = false
--正在请求数据 true表示正在请求数据，也就是数据还没好
local IsInitData = true
local IsPlayerInitDataFinish = false


local personalInfoChange_view = --配置使用RT转换功能的模块
{
    ["ui_chat_main"] = true,
    ["ui_chat_main_new"] = true,
    ["ui_personalInfo"] = true,
    ["ui_personalised"] = true
}

-- 打开界面检测
local function OnEventUIModuleShow(eventName, uiModuleName, uiRoot)
    local personalInfoChangeKey = const_personalInfo.pref_personalInfoChange .. "_" .. tostring(data_personalInfo.GetPersonalInfoValue(data_personalInfo.PropEnum.RoleID))
    if PlayerPrefs.HasKey(personalInfoChangeKey) or PlayerPrefs.GetInt(personalInfoChangeKey) == 1 then
        if isRegister then
            event.Unregister(event.UI_MODULE_SHOW, OnEventUIModuleShow)
            isRegister = false
        end
        return
    end
    if personalInfoChange_view[uiModuleName] then
        ui_window_mgr:ShowModule("ui_personal_change")
        PlayerPrefs.SetInt(personalInfoChangeKey, 1)
        PlayerPrefs.Save()
    end
end

function Init()
    if isInit then
        return
    end

    event.Register(event.UI_MODULE_SHOW, OnEventUIModuleShow)

    isRegister = true
    isInit = true
end



function GetIsInitData()
    return IsInitData
end



--- 打开个人信息界面
function ShowPersonalInfoView(personalInfoTag, onShow, onHide,isFaceTranslate)
    local gw_home_building_data = require "gw_home_building_data"
    local level = gw_home_building_data.GetBuildingMainLevel()
    if level >= const_personalInfo.value_UnlockLevel then
        local personalInfoData = {
            isFaceTranslate = isFaceTranslate,
            personalInfoTag = personalInfoTag,
        }
        ui_window_mgr:ShowModule("ui_personalInfo", onShow, onHide, personalInfoData)
    else
        flow_text.Clear()
        flow_text.Add(lang.Get(const_personalInfo.lang_LockPersonalInfo))
    end
end

function ClosePersonalInfoView()
    if ui_window_mgr:IsModuleShown("ui_personalInfo") then
        ui_window_mgr:UnloadModule("ui_personalInfo")
    end
end

--- 打开查看他人信息界面
function ShowRoleInfoView(roleId, chatPageState, onShow, onHide)
    if roleId then
        local player_mgr = require "player_mgr"
        if roleId == player_mgr.GetPlayerRoleID() then
            ShowPersonalInfoView()
            return
        end
        ui_window_mgr:ShowModule("ui_view_info", onShow, onHide,  {roleId=roleId,chatPageState=chatPageState})
    end
end

function CloseRoleInfoView()
    if ui_window_mgr:IsModuleShown("ui_view_info") then
        ui_window_mgr:UnloadModule("ui_view_info")
    end
end

--- 打开个性化界面
function ShowPersonalised(personalisedTag, goodsId, onShow, onHide)
    local viewData = {
        personalisedTag = personalisedTag,
        goodsId = goodsId,
    }
    ui_window_mgr:ShowModule("ui_personalised", onShow, onHide, viewData)
end

function CloseRoleInfoView()
    if ui_window_mgr:IsModuleShown("ui_personalised") then
        ui_window_mgr:UnloadModule("ui_personalised")
    end
end

--- 打开改名界面
function ShowPersonalChangeView(isFaceTranslate)
    ui_window_mgr:ShowModule("ui_personal_change",nil,nil,{ isFaceTranslate = isFaceTranslate})
end

function ClosePersonalChangeView()
    if ui_window_mgr:IsModuleShown("ui_personal_change") then
        ui_window_mgr:UnloadModule("ui_personal_change")
    end
end

--- 点赞
function PraiseRole(roleId,scenType)
    if roleId and scenType then
        local net_click_liking = require "net_click_liking"
        net_click_liking.MSG_ZONE_ROLE_PRAISE_UPDATE_REQ(roleId,scenType)
    end
end

--- 获得改名消耗
function GetChangeNameDiamonds()
    local cfg = gameScheme:InitBattleProp_0(const_personalInfo.cfgID_ChangeNameDiamonds)
    if cfg then
        return cfg.szParam.data[0]
    end
    return 0
end

--region 数据更新
--- 更新玩家属性
local function UpdatePropData(props)
    for key, propData in ipairs(props) do
        if propData.propid == prop_pb.PERSON_PROP_PDBID then
            data_personalInfo.SetPersonalInfoValue(data_personalInfo.PropEnum.RoleID, propData.propvalue)
        elseif propData.propid == prop_pb.PERSON_PROP_LV then
            data_personalInfo.SetPersonalInfoValue(data_personalInfo.PropEnum.RoleLevel, propData.propvalue)
        elseif propData.propid == prop_pb.PERSON_PROP_SEX then
            local sexValue = propData.propvalue
            if not sexValue or sexValue < const_personalInfo.PersonalSexTag.null or sexValue > const_personalInfo.PersonalSexTag.woman then
                sexValue = const_personalInfo.PersonalSexTag.null
            end
            data_personalInfo.SetPersonalInfoValue(data_personalInfo.PropEnum.RoleSex, sexValue)
        elseif propData.propid == prop_pb.PERSON_PROP_FACEID then
            data_personalInfo.SetPersonalInfoValue(data_personalInfo.PropEnum.FaceID, tonumber(propData.propvalue))
        elseif propData.propid == prop_pb.PERSON_PROP_FRAMEID then
            data_personalInfo.SetPersonalInfoValue(data_personalInfo.PropEnum.FrameID, propData.propvalue)
        elseif propData.propid == prop_pb.PERSON_PROP_SCHLOSS then
            data_personalInfo.SetPersonalInfoValue(data_personalInfo.PropEnum.CityID, propData.propvalue)
        elseif propData.propid == prop_pb.PERSON_PROP_POWER then
            data_personalInfo.SetPersonalInfoValue(data_personalInfo.PropEnum.RolePower, propData.propvalue)
        elseif propData.propid == prop_pb.PERSON_PROP_SCHLOSS_EFFECT then
            data_personalInfo.SetPersonalInfoValue(data_personalInfo.PropEnum.EffectID, propData.propvalue) 
        elseif propData.propid == prop_pb.PERSON_ADDPROP_PLATE then
            data_personalInfo.SetPersonalInfoValue(data_personalInfo.PropEnum.NamePlateID, propData.propvalue) 
        end
    end
end

--- 更新玩家部件
local function UpdatePartData(partDataArray)
    for i, partData in ipairs(partDataArray) do
        if partData.id == prop_pb.PERSONPART_FACEDATA then      
            local roleFacePart = prop_pb.TFaceDataPart()
            roleFacePart:ParseFromString(partData.data)

            data_personalInfo.SetPersonalInfoValue(data_personalInfo.PropEnum.RoleNameFree, not roleFacePart.bModifyName)
            data_personalInfo.SetPersonalInfoValue(data_personalInfo.PropEnum.RoleNameCD, roleFacePart.nModifyNameTime)
            data_personalInfo.SetPersonalInfoValue(data_personalInfo.PropEnum.RoleSexCD, roleFacePart.nModifySexTime)
            break
        elseif partData.id == prop_pb.PERSONPART_SUBJECT then
            local taskData = prop_pb.TSubjectPart()
            taskData:ParseFromString(partData.data)
            if taskData.topicData then
                for _, tTopicData in ipairs(taskData.topicData) do
                    -- 点赞数据
                    if tTopicData.topicID == topic_pb.TOPICNAME_LIKE_ROLE_DAILY_NUM then
                        local log = require "log"
                        log.Warning("topic_pb.TOPICNAME_LIKE_ROLE_DATA",tTopicData.data[topic_pb.ETopicKey_likeRole_Num_Common + 1])
                        -- 新增其他类型点赞数据
                        data_personalInfo.SetPersonalInfoValue(data_personalInfo.PropEnum.RolePraiseCount, tTopicData.data[topic_pb.ETopicKey_likeRole_Num_Common + 1])
                        data_personalInfo.SetPersonalInfoValue(data_personalInfo.PropEnum.PresidentialPraise, tTopicData.data[topic_pb.ETopicKey_likeRole_Num_PresidentialMail + 1])
                        data_personalInfo.SetPersonalInfoValue(data_personalInfo.PropEnum.AlliancePraise, tTopicData.data[topic_pb.ETopicKey_likeRole_Num_AllianceMail + 1])
                        data_personalInfo.SetPersonalInfoValue(data_personalInfo.PropEnum.TreasureMapPraise, tTopicData.data[topic_pb.ETopicKey_likeRole_Num_TreasureMap + 1])
                        data_personalInfo.SetPersonalInfoValue(data_personalInfo.PropEnum.PeakArenaPraise, tTopicData.data[topic_pb.ETopicKey_likeRole_Num_PeakArena + 1])
                        data_personalInfo.SetPersonalInfoValue(data_personalInfo.PropEnum.NoviceArenaPraise, tTopicData.data[topic_pb.ETopicKey_likeRole_Num_NoviceArena + 1])
                        data_personalInfo.SetPersonalInfoValue(data_personalInfo.PropEnum.the3v3ArenaPraise, tTopicData.data[topic_pb.ETopicKey_likeRole_Num_3v3Arena + 1])
                        --风暴竞技场
                        data_personalInfo.SetPersonalInfoValue(data_personalInfo.PropEnum.StormArena, tTopicData.data[topic_pb.ETopicKey_likeRole_Num_StormArena + 1])
                    elseif tTopicData.topicID == topic_pb.TOPICNAME_FRAME_PART then
                        data_personalInfo.SetPersonalInfoValue(data_personalInfo.PropEnum.TitleID, tTopicData.data[topic_pb.emTopicFrameIdx_TitleID + 1])
                    end
                end
            end
        end
    end
end

--- 更新玩家任务
local function UpdateTopicData(topicDataArray)
    for _, topicData in ipairs(topicDataArray) do
        if topicData.topicName == topic_pb.TOPICNAME_LIKE_ROLE_DATA then
            if topicData.topicKey == topic_pb.ETopicKey_likeRole_LikeCount then
                data_personalInfo.SetPersonalInfoValue(data_personalInfo.PropEnum.RolePraiseCount, topicData.value)
            end
        elseif topicData.topicName == topic_pb.TOPICNAME_FRAME_PART then
            if topicData.topicKey == topic_pb.emTopicFrameIdx_TitleID then
                data_personalInfo.SetPersonalInfoValue(data_personalInfo.PropEnum.TitleID, topicData.value)
            end
        end
        if topicData.topicName == topic_pb.TOPICNAME_LIKE_ROLE_DAILY_NUM then 
            if topicData.topicKey == topic_pb.ETopicKey_likeRole_Num_PeakArena then 
                data_personalInfo.SetPersonalInfoValue(data_personalInfo.PropEnum.PeakArenaPraise, topicData.value)
            end
            if topicData.topicKey == topic_pb.ETopicKey_likeRole_Num_3v3Arena then 
                data_personalInfo.SetPersonalInfoValue(data_personalInfo.PropEnum.the3v3ArenaPraise, topicData.value)
            end
            if topicData.topicKey == topic_pb.ETopicKey_likeRole_Num_NoviceArena then 
                data_personalInfo.SetPersonalInfoValue(data_personalInfo.PropEnum.NoviceArenaPraise, topicData.value)
            end
            if topicData.topicKey == topic_pb.ETopicKey_likeRole_Num_StormArena then
                data_personalInfo.SetPersonalInfoValue(data_personalInfo.PropEnum.StormArena, topicData.value)
            end
        end
    end
    event.Trigger(event_personalInfo.REFRESH_ARENA_TOPIC_LIKENUM)
end

---[服务器响应更新数据] 
function InitPersonalInfo(msg)
    data_personalInfo.InitPersonalInfoValue()

    data_personalInfo.SetPersonalInfoValue(data_personalInfo.PropEnum.RoleName, msg.name)
    data_personalInfo.SetPersonalInfoValue(data_personalInfo.PropEnum.RoleCreateTime, msg.roleCreateTime)
    data_personalInfo.SetPersonalInfoValue(data_personalInfo.PropEnum.RoleOpenSvrTime, msg.openSvrTime)

    UpdatePropData(msg.prop)
    UpdatePartData(msg.partData)

    -- 初始化完成之后请求一次个人信息,因为部分信息需要在这个协议中才能获取到
    local net_personalInfo = require "net_personalInfo"
    net_personalInfo.TMSG_ZONE_NEW_ROLEINFO_REQ(data_personalInfo.GetPersonalInfoValue(data_personalInfo.PropEnum.RoleID))
    IsInitData = true
end

function ReportLogin() 
    local json = require "dkjson"
    local power = data_personalInfo.GetPersonalInfoValue(data_personalInfo.PropEnum.RolePower)
    local json_str = json.encode({
        sum_power = power
    })
    event.Trigger(event.GAME_EVENT_REPORT,"login2",json_str)
end

function UpdatePersonalInfo(msg)
    for i, data in ipairs(msg.entitys) do
        if data.enType == prop_pb.enPerson then
            UpdatePropData(data.prop)
            if data:HasField("name") then
                data_personalInfo.SetPersonalInfoValue(data_personalInfo.PropEnum.RoleName, data.name)
            end
        end
    end
end

function UpdatePersonalTopicInfo(msg)
    if msg.topicdata then
        UpdateTopicData(msg.topicdata)
    end
end

function UpdateRoleInfo(msg)
    if msg and msg.roleInfo then
        local player_mgr = require "player_mgr"
        if player_mgr.GetPlayerRoleID() == msg.roleInfo.roleId then
            -- 更新一波数据
            data_personalInfo.SetPersonalInfoValue(data_personalInfo.PropEnum.WorldId, msg.roleInfo.worldID)

            data_personalInfo.SetPersonalInfoValue(data_personalInfo.PropEnum.RoleID, msg.roleInfo.roleId)
            data_personalInfo.SetPersonalInfoValue(data_personalInfo.PropEnum.RoleName, msg.roleInfo.roleName)
            data_personalInfo.SetPersonalInfoValue(data_personalInfo.PropEnum.RoleSex, msg.roleInfo.roleSex)
            data_personalInfo.SetPersonalInfoValue(data_personalInfo.PropEnum.RoleLevel, msg.roleInfo.level)
            data_personalInfo.SetPersonalInfoValue(data_personalInfo.PropEnum.RolePraise, msg.roleInfo.likeNums)
            if msg.roleInfo.roleFaceStr and not string.IsNullOrEmpty(msg.roleInfo.roleFaceStr) then 
                data_personalInfo.SetPersonalInfoValue(data_personalInfo.PropEnum.FaceID,msg.roleInfo.roleFaceStr)
            else
                data_personalInfo.SetPersonalInfoValue(data_personalInfo.PropEnum.FaceID,msg.roleInfo.roleFace)
            end
            data_personalInfo.SetPersonalInfoValue(data_personalInfo.PropEnum.FrameID, msg.roleInfo.frameid)
            data_personalInfo.SetPersonalInfoValue(data_personalInfo.PropEnum.CityID, msg.roleInfo.schlossid)
            data_personalInfo.SetPersonalInfoValue(data_personalInfo.PropEnum.RolePower, msg.roleInfo.totalice)
            data_personalInfo.SetPersonalInfoValue(data_personalInfo.PropEnum.RoleKillNum, msg.roleInfo.killnum)
            data_personalInfo.SetPersonalInfoValue(data_personalInfo.PropEnum.NamePlateID, msg.roleInfo.plateid)
            if msg.roleInfo.adornId then
                data_personalInfo.SetPersonalInfoValue(data_personalInfo.PropEnum.AnimalsID, msg.roleInfo.adornId)
                if GWG.GWHomeMgr and GWG.GWHomeMgr.droneData then
                    GWG.GWHomeMgr.droneData.SetCurAdroneSkin(msg.roleInfo.adornId)
                end
            end
            data_personalInfo.SetPersonalInfoValue(data_personalInfo.PropEnum.EffectID, msg.roleInfo.schlosseffectid)
        end
    end
    if IsInitData then 
        IsInitData = false
        ReportLogin() --登录事件上报战力打点数数
    end
    
end

--查看头像大图
function ShowAvatarBig(data)
    local gw_vip_data = require "gw_vip_data"
    local canShow = gw_vip_data.GetPrivilegeByType(gw_vip_data.PrivilegeType.viewavatar)
    if not canShow then
        local level = gw_vip_data.GetUnlockPrivilegeVipLevel(gw_vip_data.PrivilegeType.viewavatar)
        flow_text.Add(string.format2(lang.Get(664075), level))
        return
    else
        ui_window_mgr:ShowModule("ui_avatar_big",nil,nil,data)    
    end
    

end

--endregion