--- Created by nyz.
--- DateTime: 2024/5/30 12:13
--- Des:狂暴领主的管理器

local require = require
local pairs = pairs
local ipairs = ipairs
local debug = debug
local os = os
local xpcall = xpcall
local game_scheme = require "game_scheme"
local gw_const = require "gw_const"
local net_violentZombieLeader_module = require "net_violentZombieLeader_module"
local event_violentZombieLeader_define = require("event_violentZombieLeader_define")
local table = table
local event = require "event"
local string = string
local log = require "log"
local GWConst = require "gw_const"
local gw_sand_event_define = require"gw_sand_event_define"
local ui_window_mgr = require "ui_window_mgr"
local net_activity_module = require "net_activity_module"
local activity_pb = require "activity_pb"
local event_activity_define = require("event_activity_define")
local util = require "util"
-- - - - - - - - - - - - - - - - - - - - - 分割线 - - - - - - - - - - - - - - - - - - - -

---@class GWMadnessLordMgr
module("gw_madness_lord_mgr")
local GWMadnessLordMgr = {}
local self = GWMadnessLordMgr --简化写法，静态类中直接也用Self获取自身
local EVENT_HANDLERS = {}

local RedDot = false --弱红点，因为现有活动弱红点不能满足需求

function GWMadnessLordMgr.Init()
    --活动相关的，不卸载
    EVENT_HANDLERS =
    {
        [event_violentZombieLeader_define.TMSG_VIOLENT_ZOMBIE_DATA_RSP] = self.OnSetActivityData,
        [event_activity_define.TMSG_COMM_ACTIVITY_RANK_RSP] = self.OnSetRank,
    }
    self.RegisterEvents()
    RedDot = true
end

--打点通用
function GWMadnessLordMgr.ReportEvent(eventName, params)
    params = params or {}
    event.Trigger(event.GAME_EVENT_REPORT, eventName, params)
end

function GWMadnessLordMgr.SetRedDotState(value)
    RedDot = value
end

function GWMadnessLordMgr.GetRedDotState()
    return RedDot
end

---------获取相关部件----------

function GWMadnessLordMgr.GetMadnessLordData()
    if not GWMadnessLordMgr.madnessLordData then
        local GWMadnessLoraData = require "gw_madness_lord_data"
        GWMadnessLordMgr.madnessLordData = GWMadnessLoraData
        GWMadnessLordMgr.madnessLordData.Init()
    end
    return GWMadnessLordMgr.madnessLordData
end

---------协议相关-------------start
function GWMadnessLordMgr.RegisterEvents()
    for eventName,handler in pairs(EVENT_HANDLERS) do
        event.Register(eventName,handler)
    end
end

function GWMadnessLordMgr.UnregisterEvents()
    for eventName,handler in pairs(EVENT_HANDLERS) do
        event.Unregister(eventName,handler)
    end
end

function GWMadnessLordMgr.GetActivityData()
    return self.GetMadnessLordData().GetActivityData()
end

function GWMadnessLordMgr.OnGetActivityDataReq()
    net_violentZombieLeader_module.MSG_VIOLENT_ZOMBIE_DATA_REQ()
end

function GWMadnessLordMgr.OnSetActivityData(_,msg)
    self.GetMadnessLordData().SetActivityData(msg)
end

function GWMadnessLordMgr.OnGetPlayerRank()
    net_activity_module.MSG_COMM_ACTIVITY_RANK_REQ(activity_pb.ACTTYPE_VIOLENT_MONSTER_LEADER_PERSON) 
end

function GWMadnessLordMgr.OnGetMyAllianceRank()
    net_activity_module.MSG_COMM_ACTIVITY_RANK_REQ(activity_pb.ACTTYPE_VIOLENT_MONSTER_LEADER_ALLIANCE)
end

function GWMadnessLordMgr.OnGetAllianceRank()
    net_activity_module.MSG_COMM_ACTIVITY_RANK_REQ(activity_pb.ACTTYPE_VIOLENT_MONSTER_LEADER_ALLIANCE)
end

function GWMadnessLordMgr.OnGetBossRank(bossType)
    local paramInt =
    {
        bossType,
    }
    net_activity_module.MSG_COMM_ACTIVITY_RANK_REQ(activity_pb.ACTTYPE_VIOLENT_MONSTER_LEADER_TYPE_DAMAGE,nil,nil,nil,paramInt)
end

function GWMadnessLordMgr.OnSetRank(_,msg)
    if msg.actRankType == activity_pb.ACTTYPE_VIOLENT_MONSTER_LEADER_PERSON then
        self.GetMadnessLordData().SetPlayerRank(msg)
    elseif msg.actRankType == activity_pb.ACTTYPE_VIOLENT_MONSTER_LEADER_ALLIANCE then
        self.GetMadnessLordData().SetMyAllianceRank(msg)
    elseif msg.actRankType == activity_pb.ACTTYPE_VIOLENT_MONSTER_LEADER_TYPE_DAMAGE then
        self.GetMadnessLordData().SetBossRank(msg)
    elseif msg then
        
    end
end


--三个地方，三个计时器，会导致肉眼可见的计时器偏差，所以由这里统一计时，刷新其他界面
function GWMadnessLordMgr.StartTimer()
    if self.timeTicker then
        util.RemoveDelayCall(self.timeTicker)
        self.timeTicker = nil;
    end
    local activityData = self.GetActivityData()
    local timeLeft = activityData.clockDown - os.server_time()
    if timeLeft > 0 then
        self.timeTicker = util.IntervalCall(1,function()
            timeLeft = timeLeft - 1;

            if timeLeft <= 0 then
                if self.timeTicker then
                    util.RemoveDelayCall(self.timeTicker)
                    self.timeTicker = nil;
                end
            end
        end)
    end
end
---------协议相关-------------end
function GWMadnessLordMgr.Dispose()
    self.UnregisterEvents()
    if self.timeTicker then
        util.RemoveDelayCall(self.timeTicker)
        self.timeTicker = nil;
    end
    RedDot = false
end

GWMadnessLordMgr.Init()
return GWMadnessLordMgr