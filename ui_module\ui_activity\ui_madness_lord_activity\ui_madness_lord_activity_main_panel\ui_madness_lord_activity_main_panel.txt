local print = print
local require = require
local pairs = pairs     
local ipairs = ipairs
local string = string
local table = table
local tostring = tostring
local tonumber = tonumber
local type = type
local Common_Util = CS.Common_Util.UIUtil

local enum_define = require "enum_define"
local ui_base = require "ui_base"
local lang = require "lang"
local util = require "util"
local class = require "class"
local game_scheme = require "game_scheme"
local binding = require "ui_madness_lord_activity_main_panel_binding"
local gw_madness_lord_mgr = require "gw_madness_lord_mgr"
local face_item = require "face_item_new"
local math = math
local LayoutRebuilder = CS.UnityEngine.UI.LayoutRebuilder
local os = os

--region View Life
module("ui_madness_lord_activity_main_panel")
local ui_path = binding.UIPath
local window = nil
local UIView = {}

UIView.widget_table = binding.WidgetTable

function UIView:SetVCTypeUI()
    return self.__base.SetVCTypeUI(self, enum_define.enum_ui_vc_type.vc)
end

function UIView:Init()
    self:SetVCTypeUI()
    self.__base.Init(self)

    self.VData = {}
end

function UIView:OnShow()
    self.__base.OnShow(self)
end

function UIView:OnHide()
    self.__base.OnHide(self)
end

function UIView:Close(data)   
    if self.VData then
        for i, v in pairs(self.VData) do
            if type(v) == "table" and v.Dispose then
                v:Dispose()
            end
        end
    end
    self:ClearCacheItems()
    self.VData = nil    
    self.__base.Close(self)
    window = nil
end
--endregion

--region View Logic
function UIView:ShowPanel(data)
    self.data = data
    self.selectIndex = 1
    self.selectBoss = 0
    for i,v in ipairs(data.monsterList) do
        if i == self.selectIndex then
            self:ShowTargetPage(i)
        end
        self[string.format2("txt_boss{%s1}Lv",i)].text = string.format2("Lv.{%s1}")
        local createTime = v.monsterActData.createTime
        if createTime ~= 0 and createTime - os.server_time() > 0 then
            Common_Util.SetColor(self[string.format2("text_boss{%s1}}StateText",i)],"#FFD563")
            self[string.format2("text_boss{%s1}}StateText",i)].text = lang.Get(1009808)
            self[string.format2("ss_boss{%s1}StateIcon",i)]:Switch(1)
        else
            Common_Util.SetColor(self[string.format2("text_boss{%s1}}StateText",i)],"#FF886D")
            if v.monsterActData.weekStatus == 1 then
                self[string.format2("ss_boss{%s1}StateIcon",i)]:Switch(0)
                self[string.format2("text_boss{%s1}}StateText",i)].text = lang.Get(1009812)
            else
                self[string.format2("ss_boss{%s1}StateIcon",i)]:Switch(2)
                self[string.format2("text_boss{%s1}}StateText",i)].text = lang.Get(1009810)
            end
        end
    end
end

function UIView:OnPageChange(index)
    self.selectIndex = index
    self.selectBoss = 0
    self:ShowTargetPage(index)
end

function UIView:ShowTargetPage(index)
    local v = self.data.monsterList[index]
    --TODO 这里要动态加载图片，要打图集，但现在没有对应的图，所以留一个todo
    local sliderValue = math.clamp(1 - (v.monsterActData.hp / v.monsterActData.maxHp),0,1)
    Common_Util.SetLocalScale(self.tf_slider,sliderValue,1,1)
    self.txt_hpText.text = string.format2("{%s1}/{%s2}",v.monsterActData.hp,v.monsterActData.maxHp)
    self.ss_heardBroken:Switch(v.monsterActData.weekStatus)
    self.txt_BossName = string.format2(lang.Get(1009802),v.monsterActData.bossLevel,lang.Get(v.monsterCfg.name))
    local hStr, mStr = v.monsterCfg.refreshTime:match("^([0-9][0-9]?):([0-9][0-9]?)$")
    self.txt_timeTips.text = string.format2(lang.Get(1009807),hStr)
    self.selectBoss = v.monsterCfg.bosstype
    self.txt_bossTips.text = lang.Get(v.monsterCfg.restraintextid)
    self.txt_bossDamageRate.text = string.format2("{%s1}%",self.data.cfg.restrained / 100)
    LayoutRebuilder.ForceRebuildLayoutImmediate(self.tf_bossTipsBg)
    local createTime = v.monsterActData.createTime
    if createTime ~= 0 and createTime - os.server_time() > 0 then
        self.txt_weakBoss.text = lang.Get(1009809)
    else
        if v.monsterActData.weekStatus == 1 then
            self.txt_weakBoss.text = lang.Get(1009813)
        else
            self.txt_weakBoss.text = lang.Get(1009811)
        end
    end
    Common_Util.SetActive(self.obj_weakBoss,v.monsterActData.weekStatus == 1)
    Common_Util.SetActive(self.tf_topRankBg,false) --先隐藏起来，避免服务器迟迟未返回
    gw_madness_lord_mgr.OnGetBossRank(self.selectBoss)
end

function UIView:UpdateRank(bossType)
    if bossType == self.selectBoss then
        local rankInfo = gw_madness_lord_mgr.GetMadnessLordData().GetBossRank(bossType)
        self:SetTopRankInfos(rankInfo.data)
        local myRank = rankInfo.selfRank
        if myRank and myRank.score > 0 then
            Common_Util.SetActive(self.txt_myRank,true)
            Common_Util.SetActive(self.obj_noRank,false)
            self.txt_myRank.text = myRank.rank
        else
            Common_Util.SetActive(self.txt_myRank,false)
            Common_Util.SetActive(self.obj_noRank,true)
        end
        self.txt_myDamage.text = string.format2("{%s1}{%s2}",lang.Get(1009804),myRank and myRank.score or 0)
        if #rankInfo.data > 0 then
            Common_Util.SetActive(self.txt_100Damage,true)
            local index = #rankInfo.data
            self.txt_100Damage.text = string.format2("{%s1}{%s2}",string.format2(lang.Get(1009805),index),rankInfo.data[index].score)
        else
            Common_Util.SetActive(self.txt_100Damage,false)
        end
        
    end
end

function UIView:SetTopRankInfos(rankInfos)
    local player_mgr = require "player_mgr"
    Common_Util.SetActive(self.tf_topRankBg,#rankInfos>0 and true or false)
    Common_Util.SetAllChildActive(self.tf_Rank,false)
    if rankInfos then
        --设置一下排行版的图片位置
        --545 135
        if #rankInfos == 0 then
            Common_Util.SetLocalPos(self.btn_rank,0,545,0)
        else
            Common_Util.SetLocalPos(self.btn_rank,0,155,0)
        end
        self:SetActive(self.btn_rank,true)
        self:ClearCacheItems()
        for i, v in ipairs(rankInfos) do
            if not self.faceItems then
                self.faceItems = {}
            end
            if i > 3 then
                break
            end
            local info = v
            local faceStr = info.faceID
            if info.faceStr and not string.IsNullOrEmpty(info.faceStr) then
                faceStr = info.faceStr
            end
            local faceID = faceStr
            local roleLv = info.roleLv
            local frameID = info.frameID
            if info.dbid == player_mgr.GetPlayerRoleID() then
                roleLv =  player_mgr.GetPlayerLV()
                local custom_avatar_data = require "custom_avatar_data"
                local customHeadData=custom_avatar_data.GetMyAvatar()
                --适配 faceID 2025.4.2 将faceID转换为faceStr
                faceStr = player_mgr.GetRoleFaceID()
                if customHeadData then
                    faceStr=customHeadData.remoteUrl
                end
                faceID = faceStr
                frameID =player_mgr.GetPlayerFrameID()
            end

            self.faceItems[i] = self.faceItems[i]  or face_item.CFaceItem()
            self.faceItems[i]:Init(self.tf_Players,nil,1)
            self.faceItems[i]:SetFaceInfo(faceID,function()
                local mgr_personalInfo = require "mgr_personalInfo"
                mgr_personalInfo.ShowRoleInfoView(v.dbid)
            end)
            if roleLv then
                self.faceItems[i]:SetActorLvText(true, roleLv)
            end
            if frameID then
                self.faceItems[i]:SetFrameID(frameID, true)
            end
            Common_Util.SetActive(self.tf_Rank,true,tostring(i))
        end
    end
end

function UIView:ClearCacheItems()
    if self.faceItems then
        for i, v in pairs(self.faceItems) do
            v:Dispose()
        end
        self.faceItems = nil
    end
end
--endregion

--region View Static 
local CUIView = class(ui_base, nil, UIView)

function Show(data)
    if window == nil then
        window = CUIView()
        window._NAME = _NAME

        if data and type(data) == "table" then
            local tempPath = data.uiPath or ui_path
            local tempParent = data.uiParent or nil
			window:LoadUIResource(tempPath, nil, tempParent, nil, nil, false)
        else
			window:LoadUIResource(ui_path, nil, nil, nil,nil, false)
        end
    end
    window:Show()
    return window
end

function Close(data)
    if window ~= nil then
        window:Close(data)
        window = nil
    end
end

function Hide()
    if window ~= nil then
        window:Hide()
    end
end
--endregion
