--- ui_alliance_gift_controller.txt
--- Generated by <PERSON><PERSON><PERSON>(https://github.com/EmmyLua)
--- Created by .
--- DateTime: 
--- desc:    
---
local require = require
local newclass = newclass
local table = table
local reward_mgr = require "reward_mgr"
local flow_good = require "flow_good"
local lang = require "lang"
local alliance_const = require "alliance_const"
local iui_item_detail = require "iui_item_detail"
local item_data = require "item_data"
local util = require "util"
local module_scroll_list = require "scroll_list"
local log = require "log"
local controller_base = require "controller_base"
local alliance_pb = require "alliance_pb"
local flow_text = require "flow_text"
local game_scheme = require "game_scheme"
local windowMgr = require "ui_window_mgr"
local net_alliance_module = require "net_alliance_module"
local alliance_gift_data = require "alliance_gift_data"
local event_alliance_define = require "event_alliance_define"
local table_util = require "table_util"
local gw_common_util = require "gw_common_util"
local module_jumping = require "module_jumping"

module("ui_alliance_gift_controller")

local controller = nil
local UIController = newclass("ui_alliance_gift_controller", controller_base)
local boxCurrentIndex = 0
local giftCurrentIndex = 0
local boxListScrollIndex = 0
local giftListScrollIndex = 0
local toggleType = 0
-- 0宝箱 1赠礼
local ToggleType = {
    Box = 0,
    Gift = 1
}
--[[窗口初始化]]
function UIController:Init(view_name, controller_name, data)
    self.__base.Init(self, view_name, controller_name)
    self:SubscribeEvents()
    self:InitView(data)
end

function UIController:InitView(data)
    self:RefreshGiftBaseInfo()
    self:RefreshAnonymousInfo()
    self:RefreshBoxListData()
    self:BuildPrivilegesData()
    alliance_gift_data.CheckTimeBoxData()
end

--[[界面被显示的时候调用]]
function UIController:OnShow()
    self.__base.OnShow(self)
end
function UIController:OnBtn_closeClickedProxy()
    windowMgr:UnloadModule(self.view_name)
end
--联盟礼物界面一键领取
function UIController:OnBtn_ReceiveAllBtnClickedProxy()
    --local data = alliance_gift_data.GetBoxIndexData()
    --if util.get_len(data) > 0 then
    if alliance_gift_data.GetBoxReceiveNum() > 0 then
        net_alliance_module.MSG_ALLIANCE_GIFT_GET_REQ(alliance_pb.EAllianceGiftType_Loot, alliance_pb.emAllianceOneKey_Yes, nil)
    else
        flow_text.Add(lang.Get(600597))
    end
end
--联盟礼物前往按钮
function UIController:OnBtn_btnGoBattleClickedProxy()
    gw_common_util.JumpToSandAndOpenSearch(3,nil)
    windowMgr:UnloadModule(self.view_name)
    windowMgr:UnloadModule("ui_alliance_main")
end
--盟友赠礼前往按钮
function UIController:OnBtn_btnGoClickedProxy()
    --module_jumping.Jump("MenuType.valueGift")
    local ui_festival_activity_mgr = require "festival_activity_mgr"
    local festival_activity_cfg = require "festival_activity_cfg"
    
    ui_festival_activity_mgr.OpenActivityUIByEntranceID(festival_activity_cfg.ActivityEntranceType.Mall)
    
    windowMgr:UnloadModule(self.view_name)
    --windowMgr:UnloadModule("ui_alliance_main")
end

--联盟赠礼一键领取
function UIController:OnBtn_btnReceiveClickedProxy()
    local openState = alliance_gift_data.CheckGifiReceiveAllState()
    if openState then
        --local data = alliance_gift_data.GetGiftIndexData()
        if alliance_gift_data.GetGiftReceiveNum() > 0 then
            net_alliance_module.MSG_ALLIANCE_GIFT_GET_REQ(alliance_pb.EAllianceGiftType_Ally, alliance_pb.emAllianceOneKey_Yes, nil)
        else
            flow_text.Add(lang.Get(600597))
        end
    end
end
--联盟 匿名状态
function UIController:OnToggle_AnonymousValueChanged(value)
    local opt = value and alliance_pb.emAllianceRSwitchOpt_Open or alliance_pb.emAllianceRSwitchOpt_Close
    net_alliance_module.MSG_ALLIANCE_ANONYMOUS_REQ(opt)
end
--联盟宝箱toggle点击
function UIController:OnToggle_BoxValueChanged(value)
    if not value then
        return
    end
    alliance_gift_data.CheckTimeBoxData()
    toggleType = ToggleType.Box
    self:TriggerUIEvent( "SwitchPanel", 1)
    self:RefreshBoxListData()
end
function UIController:OnToggle_GiftValueChanged(value)
    if not value then
        return
    end
    alliance_gift_data.CheckTimeGiftData()
    toggleType = ToggleType.Gift
    self:TriggerUIEvent( "SwitchPanel", 2)
    self:RefreshGiftListData()
end

function UIController:BuildPrivilegesData()
    local num = game_scheme:LeagueGiftPrivilege_nums()
    self.privilegesData = {}

    for i = 0, num - 1 do
        local cfg = game_scheme:LeagueGiftPrivilege(i)
        table.insert(self.privilegesData, cfg)
    end
    table.sort(self.privilegesData, function(a, b)
        return a.UnlockLevel < b.UnlockLevel
    end)
end
function UIController:OnBtn_GiftIconClickedProxy()
    local giftData = alliance_gift_data.GetGiftLevelData()

    if giftData then
        self:TriggerUIEvent("UpdateAlliancePrivilegeList", self.privilegesData,giftData.allianceGiftLV)
    end

end

function UIController:OnBtn_PrivilegeCloseBtnClickedProxy()
    self:TriggerUIEvent("ShowPrivilegePanel", false)
end

--icon点击
function BtnIconOnClick(index, dataItem)
    --打开宝箱详情 --获取数据
    if dataItem.rewardId then
        local rewardInfo = game_scheme:Reward_0(dataItem.rewardId)
        local data = {}
        if rewardInfo then
            data.produceId = rewardInfo.arrParam[0]
            data.num = rewardInfo.arrParam[1]
        end
        iui_item_detail.Show(data.produceId, nil, item_data.Item_Show_Type_Enum.Reward_Interface, data.num, nil, nil)
        return
    end

    local data = {
        boxCfgId = dataItem.levelRewardItemID
    }
    windowMgr:ShowModule("ui_random_box_odds", nil, nil, data)
    
    --local data = alliance_gift_data.GetRewardProbability(dataItem.levelRewardItemID)
    --windowMgr:ShowModule("ui_gift_odds", nil, nil, data)
end
--已领取
function BtnReceivedOnClick()
    flow_text.Add(lang.Get(600386))
    --flow_text.Add("#配置多语言 已领取")
end
--宝箱领取
function BtnReceiveOnClick(index, dataItem)
    net_alliance_module.MSG_ALLIANCE_GIFT_GET_REQ(alliance_pb.EAllianceGiftType_Loot, alliance_pb.emAllianceOneKey_No, dataItem.boxId)
    
end
--赠礼领取
function BtnGifiReceiveOnClick(index, dataItem)
    net_alliance_module.MSG_ALLIANCE_GIFT_GET_REQ(alliance_pb.EAllianceGiftType_Ally, alliance_pb.emAllianceOneKey_No, dataItem.boxId)
end

function UIController:SubscribeEvents()
    --刷新宝箱数据
    self.refreshBoxData = function(eventName, data)
        self:RefreshGiftBaseInfo()
        self:RefreshBoxListData(data)
    end
    self:RegisterEvent(event_alliance_define.UPDATE_ALLIANCE_BOX_DATA, self.refreshBoxData)
    --刷新赠礼数据
    self.refreshGiftData = function(eventName, data)
        self:RefreshGiftListData(data)
    end
    self:RegisterEvent(event_alliance_define.UPDATE_ALLIANCE_GIFT_DATA, self.refreshGiftData)
    --刷新联盟基础礼物数据 -- 界面展示
    self.refreshGiftBaseData = function(eventName, data)
        self:RefreshGiftBaseInfo()
    end
    self:RegisterEvent(event_alliance_define.UPDATE_ALLIANCE_GIFT_BASE_DATA, self.refreshGiftBaseData)
    --刷新显示匿名赠礼
    self.refreshAnonymousInfo = function(eventName, data)
        self:RefreshAnonymousInfo()
    end
    self:RegisterEvent(event_alliance_define.UPDATE_ALLIANCE_ANONYMOUS_DATA, self.refreshAnonymousInfo)
    self.exitAlliance = function(eventName, data)
        windowMgr:UnloadModule(self.view_name)
    end
    self:RegisterEvent(event_alliance_define.EXIT_ALLIANCE, self.exitAlliance)
    self.allGiftReceive = function(_,msg)  
        --log.Error("giftExp:",msg.giftExp)
        local boxNum = 1
        if msg.boxReward then
            boxNum = #msg.boxReward
            
        end

        if msg.oneKey == alliance_pb.emAllianceOneKey_No then
            --不用弹窗 改成别的方式展示
            --local rewardID = msg.boxReward[1].rewardIds
            --local rewardData = reward_mgr.GetRewardGoodsList(rewardID)
            --flow_good.Add(rewardData)
        end
      
      
        
        self:TriggerUIEvent("FlowAllReceiveReward",boxNum)
    end
    self:RegisterEvent(event_alliance_define.ALL_GIFT_RECEIVE,self.allGiftReceive)
end
--刷新 界面基础数据
function UIController:RefreshGiftBaseInfo()
    local levelData = alliance_gift_data.GetGiftLevelData()
    self:TriggerUIEvent( "UpdateListData", levelData)
    --检测 宝箱一键领取功能是否开启
    local openState = alliance_gift_data.CheckGifiReceiveAllState()
    self:TriggerUIEvent( "ShowGiftReceiveAll", openState)
end
--刷新匿名状态
function UIController:RefreshAnonymousInfo()
    local state = alliance_gift_data.GetAnonymousSate()
    self:TriggerUIEvent( "SetGiftAnonymousSate", state == alliance_pb.emAllianceRSwitchOpt_Open)
end

function UIController:BoxItemCountDownEnd(index, dataItem)
    if toggleType == ToggleType.Box then
        alliance_gift_data.CheckTimeBoxData()
        self:RefreshBoxListData()
    end
end

function UIController:GiftCountDownEnd(index, dataItem)
    if toggleType == ToggleType.Gift then
        alliance_gift_data.CheckTimeGiftData()
        self:RefreshGiftListData()
    end
end

--列表刷新下
function UIController:OnScorllBoxRectItemRender(index, dataItem)
    boxCurrentIndex = index
    local curMaxIndex = alliance_const.LIST_SCROLL_TABLE_NUM + boxListScrollIndex * alliance_const.LIST_SCROLL_TABLE_ADD_NUM
    local sortData = alliance_gift_data.GetBoxIndexData()
    local sortLen = #sortData
    if index == curMaxIndex and sortLen > index then
        boxListScrollIndex = boxListScrollIndex + 1
        local count = curMaxIndex + alliance_const.LIST_SCROLL_TABLE_ADD_NUM
        if count > sortLen then
            count = sortLen
        end
        --需要扩容
        local data = self:GetScorllData(count)
        self:TriggerUIEvent( "UpdateAllianceBoxListData", data, index + 1)
    end
end
function UIController:OnScorllGiftRectItemRender(index, dataItem)
    giftCurrentIndex = index
    local curMaxIndex = alliance_const.LIST_SCROLL_TABLE_NUM + giftListScrollIndex * alliance_const.LIST_SCROLL_TABLE_ADD_NUM
    local sortData = alliance_gift_data.GetGiftIndexData()
    local sortLen = #sortData
    if index == curMaxIndex and sortLen > index then
        giftListScrollIndex = giftListScrollIndex + 1
        local count = curMaxIndex + alliance_const.LIST_SCROLL_TABLE_ADD_NUM
        if count > sortLen then
            count = sortLen
        end
        --需要扩容
        local data = self:GetScorllData(count)
        self:TriggerUIEvent( "UpdateAllianceGiftListData", data, index + 1)
    end
end
--刷新宝箱
function UIController:RefreshBoxListData()
    if toggleType ~= ToggleType.Box then
        return
    end
    local curMaxIndex = alliance_const.LIST_SCROLL_TABLE_NUM + boxListScrollIndex * alliance_const.LIST_SCROLL_TABLE_ADD_NUM
    local sortData = alliance_gift_data.GetBoxIndexData()
    local sortLen = #sortData
    if sortLen < curMaxIndex then
        curMaxIndex = sortLen
    end
    local data = self:GetScorllData(curMaxIndex)
    self:TriggerUIEvent( "UpdateAllianceBoxListData", data, boxCurrentIndex)
end
--刷新赠礼
function UIController:RefreshGiftListData()
    if toggleType ~= ToggleType.Gift then
        return
    end
    local curMaxIndex = alliance_const.LIST_SCROLL_TABLE_NUM + giftListScrollIndex * alliance_const.LIST_SCROLL_TABLE_ADD_NUM
    local sortData = alliance_gift_data.GetGiftIndexData()
    local sortLen = #sortData
    if sortLen < curMaxIndex then
        curMaxIndex = sortLen
    end
    local data = self:GetScorllData(curMaxIndex)
    self:TriggerUIEvent( "UpdateAllianceGiftListData", data, giftCurrentIndex)
end
--赠礼数据
function CustomGiftList(data_table, len)
    function GetItem(t, k)
        local boxData = alliance_gift_data.GetGiftIndexData()
        local strIndex = boxData[k].boxId
        local info = {}
        info = alliance_gift_data.GetGiftInfoData(strIndex)
        info.BtnIconOnClick = BtnIconOnClick
        info.BtnReceiveOnClick = BtnGifiReceiveOnClick
        info.BtnReceivedOnClick = BtnReceivedOnClick
        return info
    end
    --设置好对应的GetItem函数
    --注意 只有你想要动态获取item的值时才需要设置，如果list很短，其实没必要，直接全部传过去
    table_util.SetDynamicGetItem(data_table, GetItem)
    table_util.SetDynamicDataLen(data_table, len);
    return data_table
end
--宝箱数据
function CustomBoxList(data_table, len)
    function GetItem(t, k)
        local boxData = alliance_gift_data.GetBoxIndexData()
        local strIndex = boxData[k].boxId
        local info = {}
        info = alliance_gift_data.GetBoxInfoData(strIndex)
        info.BtnIconOnClick = BtnIconOnClick
        info.BtnReceiveOnClick = BtnReceiveOnClick
        info.BtnReceivedOnClick = BtnReceivedOnClick
        return info
    end
    --设置好对应的GetItem函数
    --注意 只有你想要动态获取item的值时才需要设置，如果list很短，其实没必要，直接全部传过去
    table_util.SetDynamicGetItem(data_table, GetItem)
    table_util.SetDynamicDataLen(data_table, len);
    return data_table
end

function UIController:GetScorllData(curMaxIndex)
    self.boxData = {}
    self.giftData = {}
    if toggleType == ToggleType.Box then
        CustomBoxList(self.boxData, curMaxIndex)
        return self.boxData
    else
        CustomGiftList(self.giftData, curMaxIndex)
        return self.giftData
    end
end
function UIController:Close()
    boxCurrentIndex = 0
    giftCurrentIndex = 0
    toggleType = ToggleType.Box
    boxListScrollIndex = 0
    self.__base.Close(self)
    controller = nil
end
--region ModuleFunction 
function Show()
    if controller == nil then
        controller = UIController.new()
    end
    return controller
end

--endregion
