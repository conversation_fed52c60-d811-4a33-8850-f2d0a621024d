local print = print
local require = require
local pairs = pairs     
local ipairs = ipairs
local string = string
local table = table
local newClass = newclass
local type = type

local arena_common_const = require "arena_common_const"
local evt_sourceSupply_define = require "evt_sourceSupply_define"
local event = require "event"
local newarena_pb = require "newarena_pb"
local event_arena_common_define = require "event_arena_common_define"
local arena_common_mgr = require "arena_common_mgr"
local controller_base = require "controller_base"
local ui_window_mgr = require "ui_window_mgr"
local lang = require "lang"

--region Controller Life
module("ui_arena_challenge_controller")
local controller = nil
local UIController = newClass("ui_arena_challenge_controller", controller_base)

function UIController:Init(view_name, controller_name, data)
    self.CData = {}
    self.__base.Init(self, view_name, controller_name)
    self.arenaData = data
    self.arenaType = data.arenaType
    self:InitBaseData()
    arena_common_mgr.RequestChallengeList(self.arenaType, false)
end

function UIController:OnShow()
    self.__base.OnShow(self)
end

function UIController:Close(data)   
    if self.CData then
        for i, v in pairs(self.CData) do
            if type(v) == "table" and v.Dispose then
                v:Dispose()
            end
        end
    end

    arena_common_mgr.SetSkipBattle(self.arenaType, self.canSkip)
    self.curChallengeData = nil
    self.CData = nil
    if not data or not data.isRecycleUI then
        self.__base.Close(self)
        controller = nil
    end
end

function UIController:AutoSubscribeEvents()
    self.onChangeChallengeListRsp = function(_, msg)
        self:OnChangeChallengeListRsp(msg) 
    end
    
    self:RegisterEvent(event_arena_common_define.TMSG_NEW_ARENA_REFRESH_CHALLENGE_LIST_RSP,self.onChangeChallengeListRsp)
    
    self.onGetArenaInfoRsp = function(_, msg)
        local battle_extern = require "battle_extern"
        battle_extern.SetupStormChallenge(self.arenaType, self.curChallengeData, msg.arrDefenseLineup, self.canSkip)
        self.curChallengeData = nil
    end
    
    self:RegisterEvent(event_arena_common_define.TMSG_NEW_ARENA_GET_DEFENCE_LINEUP_RSP,self.onGetArenaInfoRsp)
end

function UIController:AutoUnsubscribeEvents() 
end
--endregion

--region Controller Logic
function  UIController:OnBtnBuyTicketClickedProxy()
    
end
function  UIController:OnTogSkipBattleValueChange(state)
    self.canSkip = state
end
function  UIController:OnBtnCloseBtnClickedProxy()
	ui_window_mgr:UnloadModule(self.view_name)
end

function  UIController:OnBtnRefreshListBuyClickedProxy()
    local player_mgr = require "player_mgr"
    local item_data = require "item_data"
    local canBuy = player_mgr.GetPlayerAllDiamond() >= self.arenaData.buyRefreshExpend
    if not canBuy then
        local supplyData = {
            list={
                {
                    goodsId = item_data.Item_Enum.Diamond,
                    needNum = self.arenaData.buyRefreshExpend - player_mgr.GetPlayerAllDiamond()
                }
            }
        }
        event.Trigger(evt_sourceSupply_define.Evt_ShowSupplyPanel, supplyData)
    else
        arena_common_mgr.RequestChallengeList(self.arenaType, true)
    end
end

function  UIController:OnBtnRefreshListFreeClickedProxy()
    if self.curRefreshNum >= self.arenaData.maxFreeRefreshTime then
        return
    end
    arena_common_mgr.RequestChallengeList(self.arenaType, true)
end

function UIController:InitBaseData()
    local data = {
        needChallengeNum = self.arenaData.needChallengeNum,     --跳过战斗需要的挑战次数
        buyRefreshExpend = self.arenaData.buyRefreshExpend,
    }
    self:TriggerUIEvent("InitBaseShow", data)
end

function UIController:OnChangeChallengeListRsp(msg)
    self:SetListData(msg.arrChallengeInfo)
    self:UpdateRefreshBtn(msg)
    self:UpdateChallengeNumShow(msg)
end

function UIController:SetListData(listData)
    if not listData then
        return
    end
    local fightBtnEvent = function(index, data)
        if self.curChallengeData then
            return
        end
        self.curChallengeData = data
        arena_common_mgr.RequestDefendLineUp(self.arenaType, data.roleID)
    end
    self.challengeList = {}
    for k, v in ipairs(listData) do
        local roleInfo = arena_common_mgr.ParseRoleInfo(v.tRoleInfo, self.arenaType)
        if roleInfo then
            roleInfo.rank = v.nRank
            roleInfo.power = v.nDefenseLineUpPower
            roleInfo.canChallenge = v.nStatus == newarena_pb.enArenaChallengeStatus_None
            roleInfo.fightBtnEvent = fightBtnEvent
            table.insert(self.challengeList, roleInfo)
        end
    end
    self:TriggerUIEvent("UpdateChallengeList", self.challengeList)
end

--刷新按钮显示
function UIController:UpdateRefreshBtn(msg)
    
    if msg.bRefresh or not self.curRefreshNum then
        self.curRefreshNum = msg.nRefreshedNum
        local data = {
            curFreeRefreshTime = self.curRefreshNum,
            maxFreeRefreshTime = self.arenaData.maxFreeRefreshTime,
            canFreeRefresh = self.curRefreshNum < self.arenaData.maxFreeRefreshTime
        }
        self:TriggerUIEvent("UpdateRefreshBtn", data)
    end
end

--刷新挑战次数显示
function UIController:UpdateChallengeNumShow(msg)
    local canShowSkip = msg.nTotalChallengedNum >= self.arenaData.needChallengeNum
    if canShowSkip then
        --满足条件获取上次选择
        self.canSkip = arena_common_mgr.GetSkipBattle(self.arenaType)
    end
    local data = {
        canSkip = self.canSkip and true or false,
        canShowSkip = canShowSkip,
        ticketNum = self.arenaData.maxChallengedNum - msg.nChallengedNum,
    }
    self:TriggerUIEvent("UpdateChallengeNumShow", data)
end

--endregion

--region Controller Static 
function Show()
    if controller == nil then
        controller = UIController.new()
    end
    return controller
end
--endregion
