local print = print
local require = require
local pairs = pairs
local ipairs = ipairs
local string = string
local table = table
local newClass = newclass
local type = type
local math = math

local event = require "event"
local halloween_wingding_data = require "halloween_wingding_data"
local log = require "log"
local net_activity_module = require "net_activity_module"
local player_mgr = require "player_mgr"
local controller_base = require "controller_base"
local ui_window_mgr = require "ui_window_mgr"
local lang = require "lang"

--region Controller Life
module("ui_goods_submit_controller")
local controller = nil
local UIController = newClass("ui_goods_submit_controller", controller_base)

function UIController:Init(view_name, controller_name, data)
    self.CData = {}
    self.__base.Init(self, view_name, controller_name)

    self.submitData = data or {}
    self.submitData.itemID = data.itemID or 8041 --物品ID
    self.submitData.submitMinCount = data.submitMinCount or 10 --提交的最小数量
    self.submitData.preReward  = data.preReward --奖励预览
    
    self.submitData.titleStr = data.titleStr or lang.Get(1009297) --标题
    self.submitData.tipStr = data.tipStr or lang.Get(1009305) --提示
    self.submitData.btnTextStr = data.btnTextStr or lang.Get(7121) --按钮文本
    
    self:InitViewData()
end

function UIController:OnShow()
    self.__base.OnShow(self)
end

function UIController:Close(data)
    if self.CData then
        for i, v in pairs(self.CData) do
            if type(v) == "table" and v.Dispose then
                v:Dispose()
            end
        end
    end

    self.CData = nil
    if not data or not data.isRecycleUI then
        self.__base.Close(self)
        controller = nil
    end
end

function UIController:AutoSubscribeEvents()
    --道具补充监听
    self.goodsChangeFun = function(e,id,sid,num)
        if id == self.submitData.itemID then
            self:InitViewData()
        end
    end
    self:RegisterEvent(event.UPDATE_GOODS_NUM_CHANGE, self.goodsChangeFun)
end

function UIController:AutoUnsubscribeEvents()
end
--endregion

--region Controller Logic
function UIController:OnBtnCloseBtnClickedProxy()
    ui_window_mgr:UnloadModule(self.view_name)
end
function UIController:OnSliderNumSliderValueChange(value)
    self.curCount = value
    self.submitTotalNum = self.curCount * self.submitData.submitMinCount --总数
    self:TriggerUIEvent("RefreshSubmitNum", self.curCount,self.submitTotalNum)

    self:TriggerUIEvent("SetAddButtonGray",self.curCount == self.submitData.maxCount)
    self:TriggerUIEvent("SetMinusButtonGray",self.curCount == 1)
end

function UIController:OnBtnMinusClickedProxy()
    if self.curCount > 1  then
        self:TriggerUIEvent("AddSliderValue", -1)
    end
end
function UIController:OnBtnAddClickedProxy()
    if self.curCount < self.submitData.maxCount  then
        self:TriggerUIEvent("AddSliderValue", 1)
    end
end
function UIController:OnInputNumInputValueChange(text)
end
function UIController:OnInputNumInputEndEdit(text)
end
function UIController:OnBtnOkClickedProxy()
    if self.submitData.submitMinCount > self.submitData.haveItemCount then
        return
    end
    local msgData = {
        nActivityID = halloween_wingding_data.GetRankActivityID(),
        itemId = self.submitData.itemID,
        nCount = self.submitTotalNum,
    }
    net_activity_module.MSG_ACT_RANK_SUBMIT_REQ(msgData)
end

---@public function 获取拥有活动道具数量
function UIController:GetHaveItemNum()
    local sid = player_mgr.GetGoodsSidById(self.submitData.itemID)
    local itemCount = 0
    if sid and sid >0 then
        itemCount = player_mgr.GetGoodsNumberBySid(sid)
    end
    return itemCount
end

function UIController:InitViewData()
    local haveItemCount = self:GetHaveItemNum()
    self.submitData.haveItemCount = haveItemCount

    local maxCount = 1
    if self.submitData.submitMinCount > 0 then
        maxCount = math.floor(self.submitData.haveItemCount/self.submitData.submitMinCount)
        maxCount = maxCount < 1 and 1 or maxCount
        self.submitData.maxCount = maxCount
    end
    self.curCount = maxCount
    self.submitTotalNum = self.submitData.maxCount * self.submitData.submitMinCount
    
    self:TriggerUIEvent("ShowGoodsSubmitPanel", self.submitData)
    
    self:TriggerUIEvent("SetAddButtonGray",true)
    self:TriggerUIEvent("SetMinusButtonGray",maxCount == 1)
    
end

--endregion

--region Controller Static 
function Show()
    if controller == nil then
        controller = UIController.new()
    end
    return controller
end
--endregion
