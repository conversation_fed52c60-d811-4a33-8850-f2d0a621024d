local print = print
local require = require
local pairs = pairs     
local ipairs = ipairs
local string = string
local table = table
local newClass = newclass
local type = type
local os = os

local controller_base = require "controller_base"
local ui_window_mgr = require "ui_window_mgr"
local lang = require "lang"
local log = require "log"

local event = require "event"



local event_define = require "event_define"

local flow_text = require "flow_text"
local halloween_activity_slot_machine_history_data = require "halloween_activity_slot_machine_history_data"
local halloween_activity_slot_machine_setting_data = require "halloween_activity_slot_machine_setting_data"
local halloween_activity_slot_machine_const = require "halloween_activity_slot_machine_const"
local time_util = require "time_util"

local gw_task_mgr = require "gw_task_mgr"
local gw_task_const = require "gw_task_const"
local event_taskpart_define = require "event_taskpart_define"
local event_task_define = require "event_task_define"

local skep_mgr = require "skep_mgr"
---@type halloween_slot_machine_mgr
local halloween_slot_machine_mgr = require "halloween_slot_machine_mgr"

local festival_activity_mgr = require "festival_activity_mgr"
local game_scheme = require("game_scheme")
local net_halloween_slot_machine_panel = require "net_halloween_slot_machine_panel"

local slot_machine_ani_helper = require "slot_machine_ani_helper"

local reward_mgr = require "reward_mgr"
local util = require "util"
local halloween_activity_slot_machine_rate_data = require("halloween_activity_slot_machine_rate_data")


local activeTimer = nil

--region Controller Life
module("ui_halloween_activity_slot_machine_panel_controller")
local controller = nil
---@class ui_halloween_activity_slot_machine_panel_controller : ControllerBase
local UIController = newClass("ui_halloween_activity_slot_machine_panel_controller", controller_base)

function UIController:Init(view_name, controller_name, data)
    self.CData = {}
    self.__base.Init(self, view_name, controller_name)

    self.activityMainId = halloween_slot_machine_mgr.GetActivityID()
    self:GetActivityTime()

    halloween_activity_slot_machine_setting_data:Init()
    halloween_activity_slot_machine_history_data:Init()

    -- 数据
    self.CData.SettingData = halloween_activity_slot_machine_setting_data
    self.slotPatterns = {}
    for i = 0, game_scheme:SlotPattern_nums() - 1 do
        local slotPattern = game_scheme:SlotPattern(i)
        if slotPattern.AtyID == halloween_slot_machine_mgr.GetSlotGameActivityId() then
            table.insert(self.slotPatterns, slotPattern)
        end
    end
    self.slotPatternsDic = {}
    for i, v in ipairs(self.slotPatterns) do
        self.slotPatternsDic[v.PatternID] = v.cardID
    end
    self.slotPatternsList = {}
    for i, v in ipairs(self.slotPatterns) do
        table.insert(self.slotPatternsList, v.PatternID)
    end
    self.slotPatterIconIDList = {}
    for i, v in ipairs(self.slotPatterns) do
        table.insert(self.slotPatterIconIDList, v.cardID)
    end
end

function UIController:OnShow()
    self.__base.OnShow(self)

    self:RefreshUI()
    
    -- 老虎机
    self:TriggerUIEvent("RenderSlotMachine", self.slotPatterIconIDList, self.slotPatternsList,halloween_activity_slot_machine_history_data:GetLastPattern())
end

function UIController:Close(data)   
    if self.CData then
        for i, v in pairs(self.CData) do
            if type(v) == "table" and v.Dispose then
                v:Dispose()
            end
        end
    end

    self.CData = nil
    if not data or not data.isRecycleUI then
        self.__base.Close(self)
        controller = nil
    end
end

function UIController:AutoSubscribeEvents() 

    self:RegisterEvent(event_define.EVENT_HALLOWEEN_SLOT_MECHINE_DRAW_RESULT, function(event_name, data)

            -- result_index = msg.nIndex,
            -- count = msg.nDrawsCnt,
        local result_index = data.result_index
        local cfg = game_scheme:SlotGame_0(result_index, halloween_slot_machine_mgr.GetSlotGameActivityId())
        local patternGroup = string.split(cfg.TaskID, "#")
        local patternGroupRandomed = slot_machine_ani_helper.RollPattern(patternGroup, self.slotPatternsList)
        local duplicated_bool_list = slot_machine_ani_helper.checkDuplicates(patternGroupRandomed)
        local iconIDList = {}
        for i, v in ipairs(patternGroupRandomed) do
            local iconID = self.slotPatternsDic[v]
            if iconID then
                table.insert(iconIDList, iconID)
            else
                log.Error("halloween_activity_slot_machine_panel_controller", "iconID is nil, patternID = " .. v)
            end
        end
        halloween_activity_slot_machine_history_data:AddHistory(os.server_time(), cfg.ID, self:GetDrawCountSetting(), patternGroupRandomed)

        local selectRewardData = reward_mgr.GetRewardGoodsList2(cfg.RewardID) or {}
        local is_skip = self:IsSkip()

        selectRewardData[1].num = selectRewardData[1].num * self:GetDrawCountSetting()
        --cfg, act_id, rate, count
        local reward_extra_data = {
            halloween_extra_data = {
                cfg = cfg,
                act_id = halloween_slot_machine_mgr.GetSlotGameActivityId(),
                rate = halloween_activity_slot_machine_rate_data.rate_dist[cfg.ID],
                count = self:GetDrawCountSetting(),
            }
        }
        if is_skip then
            self:TriggerUIEvent("RenderSlotMachine", self.slotPatterIconIDList, self.slotPatternsList,patternGroupRandomed)
            --reward_mgr.ShowReward(selectRewardData, nil, nil, false)
            halloween_slot_machine_mgr.ShowReward(selectRewardData, nil, nil, false, reward_extra_data)
            self:RefreshUI()
        else
            self:TriggerUIEvent("RunSlotMachine", iconIDList, duplicated_bool_list, function ()
                halloween_slot_machine_mgr.ShowReward(selectRewardData, nil, nil, false, reward_extra_data)
                self:RefreshUI()
            end)
        end
        
    end)

    local update_task = function () 
        self:RefreshUI()
        self:RefreshProgressPanel()
    end
    self:RegisterEvent(event_taskpart_define.TMSG_ACT_TASK_GETREWARD_RSP,function() update_task() end)
    self:RegisterEvent(event_taskpart_define.TMSG_ACT_TASK_DATA_NTF,function() update_task() end )
    self:RegisterEvent(event_task_define.REFRESH_TASK, function() update_task() end)
    self:RegisterEvent(event_define.UPDATE_TASK_DATA, function() update_task() end)


        --道具补充监听
    self.goodsChangeFun = function(e,id,sid,num)
        if id == halloween_slot_machine_mgr.GetCoinID() then
            self:RefreshUI()
        end
    end
    self:RegisterEvent(event.UPDATE_GOODS_NUM_CHANGE, self.goodsChangeFun)
end

function UIController:AutoUnsubscribeEvents() 
end
--endregion

--region Controller Logic


function UIController:IsSkip()
    local v = halloween_activity_slot_machine_setting_data:GetValue("speed_up")
    return v == 1 or v == true
end

function UIController:RefreshUI()
    local festivalCfgData = festival_activity_mgr.GetActivityCfgByActivityID(halloween_slot_machine_mgr.GetActivityID())
 
    
    -- 活动倒计时
    if activeTimer then
        self:RemoveTimer(activeTimer)
        activeTimer = nil
    end
    local activeEndTime = self:GetActivityTime()
    local activeTimerTemp = self:CreateTimer(1, function()
        local curTime = os.server_time()
        local time = time_util.FormatTime5(activeEndTime - curTime)
        self:TriggerUIEvent("RenderActivityCountDownTime", time)
    end)
    activeTimer = activeTimerTemp
    
    -- 代币
    --local slot_coin_id = halloween_activity_slot_machine_const.slot_coin_id
    local slot_coin_id = halloween_slot_machine_mgr.GetCoinID()
    local slot_coin_num = skep_mgr.GetGoodsNum(slot_coin_id)
    self:TriggerUIEvent("RenderCoinItem", slot_coin_id, skep_mgr.GetGoodsNum(slot_coin_id))
    local draw_count_per_click = halloween_activity_slot_machine_setting_data:GetValue("draw_count_per_click")
    
    -- -- 奖励道具图标
    -- RenderProgressItem(reward_id, progress_value, progress_max, onClick)
    local next_task, cur_task = halloween_slot_machine_mgr.GetNextProgressTask()
    local current_draw_count = halloween_slot_machine_mgr.GetCurDrawCount()
    if next_task then
        local reward, reward_id = halloween_slot_machine_mgr.GetTaskReward(next_task)
        local rate = 0
        if cur_task then
            local total = next_task.completeValue - cur_task.completeValue
            rate = (cur_task.completeValue - current_draw_count) / total
        else
            -- 当前是第一个任务
            rate = current_draw_count / next_task.completeValue
        end
        self:TriggerUIEvent("RenderProgressItem", reward_id, 
            current_draw_count, next_task.completeValue, rate, 
            function(is_show)
                --log.Log("点击进度图标")
                self:OnClickGoalRewardItem(is_show)
            end
            )
    else
        -- 没有下一个，表示已经领完， 展示最后一个
        if cur_task then
            local reward, reward_id = halloween_slot_machine_mgr.GetTaskReward(cur_task)
            self:TriggerUIEvent("RenderProgressItem", reward_id, 
                current_draw_count, cur_task.completeValue, 1, 
                function(v1, v2, v3)
                    log.Log("点击进度图标")
                    self:OnClickGoalRewardItem(v1)
                end
                )
        end
    end
    
    
    -- 每次抽奖次数设置
    self:TriggerUIEvent("RenderDrawTimesBtnInfo", draw_count_per_click)

    --RenderDrawBtnInfo(item_id, count, enough)
    self:TriggerUIEvent("RenderDrawBtnInfo", slot_coin_id, draw_count_per_click, slot_coin_num >= draw_count_per_click)

    local todayCount, maxPerDayCount = halloween_slot_machine_mgr.GetTodayDrawCount()
    self:TriggerUIEvent("RenderRemainTimesInfo", maxPerDayCount - todayCount )

    self:TriggerUIEvent("RenderSkipState", self:IsSkip())
end

function UIController:OnBtnHistoryClickedProxy() 
    ui_window_mgr:ShowModule("ui_halloween_activity_slot_machine_history_pop", nil, nil, nil)
end

function UIController:OnBtnTipsBtnClickedProxy() 
    ui_window_mgr:ShowModule("ui_halloween_activity_slot_machine_rule_info_pop", nil, nil, nil)
end

function UIController:OnBtnRuleClickedProxy() 
    --跳转战令
    festival_activity_mgr.OpenActivityUIByActivityID(halloween_activity_slot_machine_const.act_id.pass)
end

function UIController:OnBtnShopClickedProxy() 
    --跳转商店
    festival_activity_mgr.OpenActivityUIByActivityID(halloween_activity_slot_machine_const.act_id.shop)
end





function UIController:OnBtnDrawClickedProxy()
    if halloween_activity_slot_machine_const.use_local_test then
        util.DelayCallOnce(0.2, function()
            event.Trigger(event_define.EVENT_HALLOWEEN_SLOT_MECHINE_DRAW_RESULT, {
                result_index = 1,
                count = 1,
            })
        end)
        return
    end
    -- 资源检测
    local slot_coin_id = halloween_slot_machine_mgr.GetCoinID()
    local slot_coin_num = skep_mgr.GetGoodsNum(slot_coin_id)
    local draw_count_per_click = self:GetDrawCountSetting()
    local enough = slot_coin_num >= draw_count_per_click
    if not enough then
        flow_text.Add(lang.Get(halloween_activity_slot_machine_const.lang_key.not_enough))
        -- 弹礼包页面
        ui_window_mgr:ShowModule("ui_halloween_daily_gift", nil, nil, nil)
        return
    end
    --点击抽奖，发送请求
    net_halloween_slot_machine_panel.MSG_SLOT_MACHINE_DRAW_REQ(halloween_slot_machine_mgr.GetSlotGameActivityId(), draw_count_per_click)
end
function UIController:OnBtnTimesClickedProxy()
    local v = self:GetDrawCountSetting()
    local new_v = v == 1 and 5 or 1
    self:SetDrawCountSetting(new_v)
    -- 每次抽奖次数设置
    --self:TriggerUIEvent("RenderDrawTimesBtnInfo", new_v)
    --self:TriggerUIEvent("RenderDrawBtnInfo", new_v)
    self:RefreshUI()
end
function UIController:OnTogSpeedValueChange(state)
    halloween_activity_slot_machine_setting_data:SetValue("speed_up", state)
    self:TriggerUIEvent("RenderSkipState", state)
end
function UIController:OnBtnItem_addClickedProxy()
    -- 弹礼包页面
    ui_window_mgr:ShowModule("ui_halloween_daily_gift", nil, nil, nil)
end

function UIController:OnClickGoalRewardItem(is_show)
    is_show = not is_show

    self:ShowProgressPanel(is_show)
    self:RenderProgressPanel()
end

function UIController:ShowProgressPanel(is_show)
    self:TriggerUIEvent("ShowProgressPanel", is_show)
end

function UIController:RenderProgressPanel()
    self:RefreshProgressPanel()
end

function UIController:RefreshProgressPanel()
    local task_data_list, task_cfg_list = halloween_slot_machine_mgr.GetProgressRewardTasks()
    local next_task, cur_task = halloween_slot_machine_mgr.GetNextProgressTask()
    local current_draw_count = halloween_slot_machine_mgr.GetCurDrawCount()

    local slot_game_progress_panel_data = require "slot_game_progress_panel_data"
    ---@type slot_game_progress_panel_data
    local panel_data = slot_game_progress_panel_data.new()
    panel_data.score = current_draw_count
    panel_data.total_score = task_data_list[#task_data_list].completeValue
    panel_data.progress_data_list = {}
    for i = 1, #task_cfg_list do
        local task_data = task_data_list[i]
        local task_cfg = task_cfg_list[i]
        ---@type slot_game_progress_item_data
        local progress_data = {}
        progress_data.bProgressEnough = task_data.completeValue <= task_data.rate
        progress_data.bShowLine = i < #task_data_list
        progress_data.bTaken = task_data.status
        progress_data.progress =task_data.completeValue
        progress_data.reward_id = task_cfg.TaskReward

        table.insert(panel_data.progress_data_list, progress_data)
    end

    local clickClose = function ()
        self:TriggerUIEvent("ShowProgressPanel", false) 
    end

    local clickItem = function (index, progress_data, id, count, reward_type, _goodsItem)
        if not progress_data.bTaken and progress_data.bProgressEnough then
            -- 领取
            local receiveTable = halloween_slot_machine_mgr.CheckReceiveList()
            gw_task_mgr.ReceiveTaskListReward(receiveTable,halloween_slot_machine_mgr.GetProgressTaskActivityID(),
            gw_task_const.TaskModuleType.HalloweenSignIn)
        end
        if progress_data.bTaken or (not progress_data.bProgressEnough) then
            _goodsItem:DefultClick()
            return
        end
    end
    self:TriggerUIEvent("RenderProgressPanel", panel_data,clickClose, clickItem)
end

function UIController:OnShowPersonalisedTip(id, root)
    -- local util = require "util"
    -- util.DelayCallOnce(0.1, function()
    --         local tipData = {
    --         id = id,
    --         type = self.labelIndex,
    --         position = root.transform.position,
    --         sizeDelta = root.sizeDelta,
    --     }

    --     windowMgr:ShowModule("ui_personalised_tip", nil, nil, tipData)
    -- end)

end


function UIController:GetDrawCountSetting()
    return halloween_activity_slot_machine_setting_data:GetValue("draw_count_per_click")
end
function UIController:SetDrawCountSetting(count)
    return halloween_activity_slot_machine_setting_data:SetValue("draw_count_per_click", count)
end




-- 获取活动结束时间
function UIController:GetActivityTime()
    local actData = festival_activity_mgr.GetActivityDataByActivityID(halloween_slot_machine_mgr.GetActivityID())
    return actData and actData.endTimeStamp or 0
end


--endregion

--region Controller Static 
function Show()
    if controller == nil then
        controller = UIController.new()
    end
    return controller
end
--endregion
