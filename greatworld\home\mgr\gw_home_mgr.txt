--- Created by fgy.
--- DateTime: 2024/5/28 20:41
--- Des:大世界家园管理器  数据获取中心 整合cfg与服务器data数据中心

local require = require
local table = table
local tostring = tostring
local pairs = pairs
local UnityEngine = CS.UnityEngine
local debug = debug
local xpcall = xpcall
local gw_ed = require("gw_ed")
local event = require "event"
local event_define = require "event_define"
local gw_home_cfg = require "gw_home_cfg"
local gw_home_config_mgr = require "gw_home_config_mgr"
local GWHomeInternalMgr = require("gw_home_internal_mgr")
local GWHomeBubbleMgr = require("gw_home_bubble_mgr")
local GWHomeInputMgr = require("gw_home_input_mgr")
local GWHomeModelMgr = require("gw_home_model_mgr")
local player_mgr = require "player_mgr"
local prop_pb = require "prop_pb"
local city_pb = require "city_pb"
local net_city_module = require "net_city_module"
local util = require "util"
local GWG = GWG
local Time = CS.UnityEngine.Time
local print = print
-- - - - - - - - - - - - - - - - - - - - - 分割线 - - - - - - - - - - - - - - - - - - - -

---@class gw_home_mgr 大世界家园 管理器
module("gw_home_mgr")
-- 开启log日志
logger = require("logger").new("sw_login_progress_home", 0)
Warning = logger.Warning
Warning0 = logger.Warning0
--错误上报
function _OnLuaErr(err, ...)
    Warning0("OnLuaErr GWHomeMgr", err, ...)
    util.AssetBundleManagerTrackEvent("lua_err", {
        type = "gw_home_mgr_error",
        err = err,
        trackback = debug.traceback(),
    })
end

local GWHomeMgr = {}
local self = GWHomeMgr
function GWHomeMgr.Init()
    Warning(4, "GWHomeMgr.Init")
    GWHomeMgr.m_field = {}
    player_mgr.AddInitEvent(prop_pb.PERSONPART_CITY, GWHomeMgr.OnLoginEvent)
end
function GWHomeMgr.InitData()
    --m_field内部数据域
    --m_scene_state-场景加载状态 
    --home_map_data-登录下发的主城数据
    --m_edit_state --编辑状态 GWG.GWConst.EHomeBuildingOperateType
    --m_edit_building_id --编辑的建筑
    --m_is_charge_long_click --是否在蓄力点击    
    Warning(4, "GWHomeMgr.InitData")
    gw_home_cfg.Init()
    GWHomeMgr.cfg = gw_home_cfg
    GWHomeMgr.inputMgr = GWHomeInputMgr
    GWHomeMgr.internalMgr = GWHomeInternalMgr
    GWHomeMgr.bubbleMgr = GWHomeBubbleMgr
    GWHomeMgr.modelMgr = GWHomeModelMgr
    GWHomeMgr.homeNode = require("gw_home_node")
    GWHomeMgr.data = require "gw_home_data"
    GWHomeMgr.commonData = require("gw_home_common_data")
    GWHomeMgr.gridData = require "gw_home_grid_data"
    GWHomeMgr.buildingData = require "gw_home_building_data"
    GWHomeMgr.buildingQueueData = require("gw_home_building_queue_data")
    GWHomeMgr.soldierData = require "gw_home_soldier_data"
    GWHomeMgr.survivorData = require "gw_home_survivor_data"
    GWHomeMgr.chapterData = require "gw_home_chapter_data"
    GWHomeMgr.decorateData = require "gw_home_decorate_data"
    GWHomeMgr.droneData = require "gw_home_drone_data"
    GWHomeMgr.honourWallData = require "gw_home_honour_wall_data"
    GWHomeMgr.effectMgr = require "gw_home_effect_mgr"
    GWHomeMgr.noviceChapterData = require "gw_home_novice_chapter_data"
    GWHomeMgr.data.Init()
    GWHomeMgr.commonData.Init()
    GWHomeMgr.gridData.Init()
    GWHomeMgr.buildingData.Init()
    GWHomeMgr.buildingQueueData.Init()
    GWHomeMgr.soldierData.Init()
    GWHomeMgr.survivorData.Init()
    GWHomeMgr.chapterData.InitData()
    GWHomeMgr.droneData.Init()
    GWHomeMgr.honourWallData.Init()
    GWHomeMgr.effectMgr.Init()
    GWHomeMgr.decorateData.Init()
    GWHomeMgr.noviceChapterData.Init()
    GWHomeMgr.noviceChapterData.InitData()
    GWHomeMgr.m_field.isInitData = true
    local function_open_mgr = require "function_open_mgr"
    function_open_mgr.RegisterFunctionOpen("", function_open_mgr.OpenIdEnum.HomeLord, self.OnRefreshLordAndStrayDog)
    event.Register(event.UPDATE_MODULE_OPEN,self.OnRefreshLordAndStrayDog)
    event.Register(event.STRAY_DOG_PACKAGE_ACTIVE, self.OnRefreshLordAndStrayDog)
    gw_ed.mgr:Register(gw_ed.GW_HOME_BUILDING_UPGRADE, self.OnDogBuildingUpgrade)
end

--新家园camera流程
function GWHomeMgr.InitHomeNewInput()
    --初始化相机数据
    GWHomeMgr.inputMgr.InitCameraValues()
    GWHomeMgr.inputMgr.Dispose()
    GWHomeMgr.inputMgr.Init()
end

--@region 自身场景流程管理
--进入场景
function GWHomeMgr.EnterHomeScene(data)
    Warning(4, "GWHomeMgr.EnterHomeScene")
    xpcall(function()
        local gw_mgr = require "gw_mgr"
        local gw_const = require "gw_const"
        if gw_mgr.GetFsmCurScene() == gw_const.ESceneType.Loading then
            return
        end
        --进入城建前，需要进行清理
        --GWHomeMgr.DisposeWithoutData()--后续应该需要全部清理，目前未降低英雄，只处理node
        GWHomeMgr.homeNode.Dispose()
        if not GWHomeMgr.m_field.isInitData then
            GWHomeMgr.InitData()
        end
        Warning(4, "GWHomeMgr.EnterHomeScene ready to ESceneType.Home ")
        gw_mgr.SetSceneCfg(gw_const.ESceneType.Home,data and data.showEffect or false)
        UnityEngine.Shader.DisableKeyword("_FOG_ENABLED")
    end, _OnLuaErr)
end

--离开场景
---@public 离开场景，当前会保留数据
function GWHomeMgr.LeaveHomeScene()
    Warning(4, "GWHomeMgr.LeaveHomeScene")
    xpcall(function()
        -- 销毁领主系统（父对象会被销毁，需要完全清理）
        local gw_home_lord_util = require "gw_home_lord_util"
        gw_home_lord_util.DisposeLordSystem()

        -- 销毁流浪狗系统（父对象会被销毁，需要完全清理）
        local gw_home_stray_dog_util = require "gw_home_stray_dog_util"
        gw_home_stray_dog_util.DisposeStrayDogSystem()

        --卸载实体
        GWHomeMgr.RecycleComponents()
        if not GWG.GWMgr.openSceneFsm then
            GWHomeMgr.m_field.m_scene_state = GWG.GWConst.EHomeSceneState.Leaved
        end
        if GWHomeMgr.m_field.isReturnToLogin then
            GWHomeMgr.Dispose()
        else
            GWHomeMgr.DisposeWithoutData()
        end
    end, _OnLuaErr)
end

---@public 显示场景 todo by hsj
function GWHomeMgr.ShowHomeScene()
end

---@public 隐藏场景 todo by hsj  如果是被动触发，建议把函数改成 ONHideHomeScene
function GWHomeMgr.HideHomeScene()
end

--加载进入场景通知
function GWHomeMgr.OnLoadScene()
    xpcall(function()
        if not GWG.GWMgr.openSceneFsm then
            GWHomeMgr.m_field.m_scene_state = GWG.GWConst.EHomeSceneState.Loaded
        end
        GWG.GWAdmin.SwitchUtility.HomeLog("OnLoadScene is first =", GWHomeMgr.m_field.home_map_data)
        GWHomeMgr.InitHomeNewInput()      
        GWHomeInternalMgr.Init()
        GWHomeBubbleMgr.Init()
        GWHomeModelMgr.Init()
        GWHomeMgr.chapterData.InitSceneEvent()
        GWHomeMgr.noviceChapterData.InitSceneEvent()
        --启动地图单例
        GWG.GWAdmin.HomeMapUtil.OnFirstEnter()
        if not GWHomeMgr.m_field.home_map_data then
            GWG.GWAdmin.SwitchUtility.Error("没有登录数据--城建推送")
            return
        end
        gw_ed.mgr:Trigger(gw_ed.GW_HOME_COMMON_DATA_CHANGE, "RefreshShowByData")
        util.DelayCallOnce(0.1, function()
            GWG.GWHomeMgr.buildingData.CreateAllDecorateData()
            
            -- 初始化领主和流浪狗系统
            self.OnRefreshLordAndStrayDog()
        end)
        --判断一下；
        local ui_window_mgr = require "ui_window_mgr"
        if not ui_window_mgr:IsModuleShown("ui_common_item_anim") then
            ui_window_mgr:ShowModule("ui_common_item_anim")
        end
        print("LD: check init time ", Time.realtimeSinceStartup)
    end, _OnLuaErr)
end
--@endregion 自身场景流程管理
---@region网络事件
--监听登录城建部件下发的数据
function GWHomeMgr.OnLoginEvent(data)
    local home_map_data = city_pb.TMSG_CITY_GET_ALLINFO_RSP()
    home_map_data:ParseFromString(data)
    GWHomeMgr.m_field.home_map_data = home_map_data   
    local isInGame = GWG.GWMgr.curScene  and  GWG.GWMgr.curScene ~= GWG.GWConst.ESceneType.None
    Warning(4, "GWHomeMgr.OnLoginEvent ", GWHomeMgr.m_field.haveConnected, " mainReady=", GWHomeMgr.m_field.mainReady,"scene=",GWG.GWMgr.curScene,"isInGame=",isInGame)
    event.RecodeTrigger("BHomeProgress_LoginEvent", { state_name = "OnLoginEvent", haveConnected = GWHomeMgr.m_field.haveConnected, mainReady = GWHomeMgr.m_field.mainReady,isInGame = isInGame or false})
    xpcall(function()
        --如果在游戏中，且触发了重连，则直接重连
        if isInGame then
            GWHomeMgr.m_field.haveConnected = false
            gw_ed.mgr:Trigger(gw_ed.GW_HOME_NET_EVENT, "OnHomeServerNTF", GWHomeMgr.m_field.home_map_data, false)
        else
            GWHomeMgr.InitData()
            if GWHomeMgr.m_field.mainReady then
                GWHomeMgr.FirstLoginInit()
            end
        end
    end, _OnLuaErr)
    event.Trigger(event.FIRST_LOGIN_OPTIMIZE_REQUIRE_STEP,3)
end
---@public 设置主流程已经准备好
function GWHomeMgr.SetMainReady()
    Warning(4, "GWHomeMgr.SetMainReady ", GWHomeMgr.m_field.mainReady)
    event.RecodeTrigger("BHomeProgress_MainReady", { state_name = "SetMainReady", haveConnected = GWHomeMgr.m_field.haveConnected, mainReady = GWHomeMgr.m_field.mainReady })
    if GWHomeMgr.m_field.mainReady then
        return
    end
    --主流程已经准备好了
    GWHomeMgr.m_field.mainReady = true
    if GWHomeMgr.m_field.home_map_data then
        GWHomeMgr.FirstLoginInit()
    end
end
---@public 首次登录
function GWHomeMgr.FirstLoginInit()
    Warning(4, "GWHomeMgr.FirstLoginInit ")
    event.RecodeTrigger("BHomeProgress_FirstLogin", { state_name = "FirstLoginInit", haveConnected = GWHomeMgr.m_field.haveConnected, mainReady = GWHomeMgr.m_field.mainReady })
    xpcall(function()
        GWHomeMgr.m_field.isReturnToLogin = false
        GWHomeMgr.InitDependencyData()
        --监听到城建信息时，我们才初始化城建数据   
        net_city_module.MSG_CITY_GET_ALL_TROOP_REQ(); --拉取队伍请求
        GWHomeMgr.EnterHomeScene()
        gw_ed.mgr:Trigger(gw_ed.GW_HOME_NET_EVENT, "OnHomeServerNTF", GWHomeMgr.m_field.home_map_data, true)
    end, _OnLuaErr)
end
---@public 初始化依赖的必要数据
function GWHomeMgr.InitDependencyData()
    GWG.GWInputMgr:InitBeginEvent()
    --引入初始化
    local home_ui_mgr = require "home_ui_mgr"
    home_ui_mgr.Init()
end

---@public 断线事件
function GWHomeMgr.OnDisconnectEvent()
end

---@public 重连成功事件
function GWHomeMgr.OnReconnectSuccessEvent()
    Warning(4, "GWHomeMgr.OnReconnectSuccessEvent")
    GWHomeMgr.m_field.haveConnected = true
end
---@endregion

---@public 卸载实体数据
---当前场景必须后卸载
---tood 这里应该设计成每个模块自身来卸载实体，因为有先后问题
function GWHomeMgr.RecycleComponents()
    Warning(4, "GWHomeMgr.RecycleComponents")
    local comps = GWG.GWAdmin.m_bs_components
    for i, v in pairs(comps) do
        if v.compName ~= "gw_home_comp_scene" then
            GWG.GWAdmin.PushBSComponent(v)
        end
    end
end

function GWHomeMgr.OnRefreshLordAndStrayDog()    
    --如果不是在主城，直接返回
    local isInHome =GWG.GWMgr.curScene  and  GWG.GWMgr.curScene == GWG.GWConst.ESceneType.Home
    --local  log  = require "log"
    --log.Error("OnRefreshLordAndStrayDog isInHome",isInHome)
    if not isInHome then
        return
    end    
    -- 初始化配置管理器
    gw_home_config_mgr.InitConfig()

    -- 初始化领主和流浪狗系统
    local gw_home_lord_util = require "gw_home_lord_util"
    gw_home_lord_util.InitLordSystem()

    local gw_home_stray_dog_util = require "gw_home_stray_dog_util"
    gw_home_stray_dog_util.InitStrayDogSystem()
end

function GWHomeMgr.OnDogBuildingUpgrade(_, data)
     if not data then
        return
    end
    -- 判断建筑类型 如果是狗屋的话 升级流浪狗
    local buildingType = GWHomeMgr.buildingData.GetBuildingTypeByBuildingId(data.nBuildingID)
    if buildingType == GWG.GWConst.enBuildingType.enBuildingType_DogHouse then
        local gw_home_stray_dog_util = require "gw_home_stray_dog_util"
        gw_home_stray_dog_util.UpgradeStrayDog()
    end
end

---@public 重置到登录界面
function GWHomeMgr.OnReturnToLogin()
    --当前这里延迟一点实现；是为了保证监听USER_DATA_RESET的其他事件在我之前
    util.DelayCallOnce(0,function()
        Warning(4, "GWHomeMgr.OnReturnToLogin")
        GWHomeMgr.m_field.isReturnToLogin = true
        if GWHomeMgr.m_field and GWHomeMgr.m_field.isInitData then
            GWHomeMgr.Dispose()
        end
    end)
end
---场景销毁 彻底销毁场景时才调用
function GWHomeMgr.Dispose()
    Warning(4, "GWHomeMgr.Dispose")
    event.RecodeTrigger("BHomeProgress_Dispose", { state_name = "Dispose" })

    -- 彻底销毁领主和流浪狗系统（只在完全退出时调用）
    GWG.GWAdmin.SwitchUtility.HomeLog("彻底销毁领主和流浪狗系统")
    local gw_home_lord_util = require "gw_home_lord_util"
    gw_home_lord_util.DisposeLordSystem()

    local gw_home_stray_dog_util = require "gw_home_stray_dog_util"
    gw_home_stray_dog_util.DisposeStrayDogSystem()

    GWHomeMgr.DisposeData()
    GWHomeMgr.DisposeWithoutData()
    GWHomeMgr.m_field = {}
    GWHomeMgr.m_field.home_map_data = nil
    GWHomeMgr.m_field.isInitData = false
    GWHomeMgr.m_field.isReturnToLogin = false
    GWHomeMgr.m_field.haveConnected = false
end

---场景销毁,但不销毁数据 干掉场景时必定调用
function GWHomeMgr.DisposeWithoutData()
    Warning(4, "GWHomeMgr.DisposeWithoutData")
    if GWHomeMgr.m_field and not GWHomeMgr.m_field.isInitData then
        return
    end
    Warning(4, "GWHomeMgr.DisposeWithoutData success")
    xpcall(function()
        GWHomeInternalMgr.Dispose()
        GWHomeBubbleMgr.Dispose()
        GWHomeModelMgr.Dispose()
        GWHomeMgr.inputMgr.Dispose()
        GWHomeMgr.internalMgr.Dispose()
        GWHomeMgr.chapterData.DisposeEntity()
        GWHomeMgr.noviceChapterData.DisposeEntity()
        GWHomeMgr.effectMgr.Dispose()
        GWHomeMgr.decorateData.DisposeEntity()
        GWHomeMgr.homeNode.Dispose()
    end, _OnLuaErr)
end

---场景销毁,销毁数据
function GWHomeMgr.DisposeData()
    Warning(4, "GWHomeMgr.DisposeData")
    if GWHomeMgr.m_field and not GWHomeMgr.m_field.isInitData then
        return
    end
    Warning(4, "GWHomeMgr.DisposeData success")
    event.Unregister(event.UPDATE_MODULE_OPEN,self.OnRefreshLordAndStrayDog)
    event.Unregister(event.STRAY_DOG_PACKAGE_ACTIVE, self.OnRefreshLordAndStrayDog)
    gw_ed.mgr:Unregister(gw_ed.GW_HOME_BUILDING_UPGRADE, self.OnDogBuildingUpgrade)
    xpcall(function()
        gw_home_cfg.Clear()
        GWHomeMgr.data.Dispose()
        GWHomeMgr.commonData.Dispose()
        GWHomeMgr.gridData.Dispose()
        GWHomeMgr.buildingData.Dispose()
        GWHomeMgr.buildingQueueData.Dispose()
        GWHomeMgr.soldierData.Dispose()
        GWHomeMgr.chapterData.Dispose()
        GWHomeMgr.noviceChapterData.Dispose()
        GWHomeMgr.decorateData.Dispose()
        GWHomeMgr.droneData.Dispose()
    end, _OnLuaErr)
end
-----region 监听上报接口时打印日志
---@public 拼接所有的字符串
function GetConcatStr(...)
    local d = { ... }
    local dd = { " " }
    local ind = 2
    for k, v in pairs(d) do
        d[k] = v and tostring(v) or "nil"
        dd[ind] = d[k]
        ind = ind + 1
    end
    local pstr = table.concat(dd, "\t")
    return pstr or ""
end
function GWHomeMgr.CollectHomeInfo()
    --local dump = _G["Edump"]
    --if not dump then
    --    return
    --end    
    local strInfo = "开始收集当前城建信息：\n"
    if not GWHomeMgr.m_field then
        strInfo = strInfo .. GetConcatStr("城建Mgr还未初始化")
    else
        --local d = dump(GWHomeMgr.m_field)
        --strInfo = strInfo ..GetConcatStr("field =",d)
        for k, v in pairs(GWHomeMgr.m_field) do
            if k ~= "home_map_data" then
                strInfo = strInfo .. tostring(k) .. " = " .. (v and tostring(v) or "nil") .. "\n"                
            end
        end
        if GWHomeMgr.m_field.home_map_data then
            strInfo = strInfo .. "home_map_data" .. " = " .. tostring(GWHomeMgr.m_field.home_map_data) .. "\n"
        end
    end
    if GWG.GWMgr then
        strInfo = strInfo .. "\n开始收集GWMgr信息：\n"
        for k, v in pairs(GWG.GWMgr) do
            strInfo = strInfo .. tostring(k) .. " = " .. (v and tostring(v) or "nil") .. "\n"
        end
    end
    --LogHelp.clipboard = strInfo
    local log = require "log"
    log.Warning(strInfo)
end
----endregion
event.Register(event.BEFORE_SEND_BUG_REPORT, GWHomeMgr.CollectHomeInfo)
event.Register(event_define.USER_DATA_RESET, GWHomeMgr.OnReturnToLogin)
event.Register(event_define.ACCOUNT_CHANGE_WORLD_RSP, GWHomeMgr.OnReturnToLogin)
event.Register(event_define.NET_RECONNECT_SUCCESS_EVENT, GWHomeMgr.OnReconnectSuccessEvent)
event.Register(event_define.NET_DISCONNECT_EVENT, GWHomeMgr.OnDisconnectEvent)
return GWHomeMgr