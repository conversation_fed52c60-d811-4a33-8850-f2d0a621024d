local require = require
local GWG = GWG
local print = print
local typeof = typeof
local function_open_mgr = require "function_open_mgr"
-- Unity类型引用
local Vector3 = CS.UnityEngine.Vector3
local Collider = CS.UnityEngine.Collider
local SphereCollider = CS.UnityEngine.SphereCollider
local event				= require "event"
local util = require "util"

module("gw_home_stray_dog_util")

local M = {}

-- 流浪狗组件引用
M.strayDogCompId = nil
M.strayDogComp = nil

-- 创建流浪狗组件
function M.CreateStrayDogComp()
    local modelPath = "art/characters/xiaogou/prefabs/xiaogou_simple.prefab" 
    -- local modelPath = "art/characters/xiaogou01/prefabs/xiaogou01_simple.prefab" 
    
    -- 创建组件
    local comp, id = GWG.GWAdmin.PopBSComponent(GWG.GWCompName.gw_home_comp_stray_dog)
    comp.entityType = GWG.GWConst.EHomeEntityType.StrayDog
    -- 获取配置
    local gw_home_config_mgr = require "gw_home_config_mgr"
    local strayDogConfig = gw_home_config_mgr.GetStrayDogConfig()
    
    -- 加载模型
    comp.entity = GWG.GWAssetMgr:Load(modelPath, function(asset)
        comp:Bind(asset)
        
        -- 设置模型缩放和位置调整
        if asset and asset.transform then
            asset.transform.localScale = { x = strayDogConfig.modelScale, y = strayDogConfig.modelScale, z = strayDogConfig.modelScale }
            -- 添加碰撞体用于点击检测
            local collider = asset:GetComponent(typeof(Collider))
            if not collider then
                -- 如果没有碰撞体，添加一个球形碰撞体
                collider = asset:AddComponent(typeof(SphereCollider))
                if collider then
                    collider.radius = 0.3  -- 设置碰撞体半径，比领主小
                    collider.isTrigger = false  -- 不是触发器，用于点击检测
                end
            end
        end
        
    end, GWG.GWHomeNode.heroNode(), "Home_StrayDog_" .. id)
    
    return id, comp
end

-- 获取流浪狗节点（用于挂载流浪狗实体）
function M.GetStrayDogParentNode()
    -- 使用实体层级作为父节点
    return GWG.GWHomeNode.heroNode()
end

-- 创建流浪狗（服务器通知后调用）
function M.CreateStrayDog()
    -- 检查是否已经创建了流浪狗
    if M.strayDogCompId then
        return M.strayDogCompId, M.strayDogComp
    end
    
    -- 创建流浪狗组件
    local id, comp = M.CreateStrayDogComp()
    
    -- 保存引用
    M.strayDogCompId = id
    M.strayDogComp = comp
    
    print("流浪狗已创建，ID:", id)
    return id, comp
end

-- 获取当前流浪狗组件
function M.GetStrayDogComp()
    return M.strayDogComp
end

function M.IsModuleOpen()
    local net_module_open = require "net_module_open"
    local moduleOpenPro_pb = require "moduleOpenPro_pb"
    local isShow = net_module_open.GetModuleIsOn(moduleOpenPro_pb.emModuleID_StrayDog)
    return isShow
end


-- 初始化流浪狗系统
function M.InitStrayDogSystem()
    if not M.IsModuleOpen() then
        return
    end
    local gw_straydog_mgr = require "gw_straydog_mgr"
    local isBuy = gw_straydog_mgr.HasBuyFirstPackage()
    if not isBuy then
        return
    end
    ---防御性编程 节点不能为空
    if GWG.GWHomeNode == nil or GWG.GWHomeNode.heroNode()==nil then
        return
    end
    -- 检查是否已经创建了流浪狗
    if M.strayDogCompId then
        return M.strayDogCompId, M.strayDogComp
    end

    -- 创建流浪狗
    return M.CreateStrayDog()
end

-- 升级流浪狗
function M.UpgradeStrayDog()
    -- 刷新狗的模型
    if M.strayDogComp then
        M.strayDogComp:Upgrade()
    end
end

-- 暂停流浪狗系统
function M.PauseStrayDogSystem()
    if M.strayDogComp then
        M.strayDogComp:Pause()
        return true
    end
    return false
end

-- 恢复流浪狗系统
function M.ResumeStrayDogSystem()
    if M.strayDogComp then
        M.strayDogComp:Resume()
        return true
    end
    return false
end

-- 检查流浪狗系统是否存在
function M.IsStrayDogSystemExists()
    return M.strayDogComp ~= nil and M.strayDogCompId ~= nil
end

-- 处理场景点击事件（集成到输入系统）
function M.OnSceneClick(worldX, worldY)
    if not M.strayDogComp then
        return false
    end
    
    -- 转换为网格坐标
    local gw_home_grid_data = require "gw_home_grid_data"
    local gridX, gridY = gw_home_grid_data.GetGridXYdByXZ(worldX, worldY)
    
    -- 检查点击位置是否可通行
    local gw_home_astar_pathfinding = require "gw_home_astar_pathfinding"
    if gw_home_astar_pathfinding.IsWalkable(gridX, gridY) then
        local properties = {LordPos = gridX.."#"..gridY}
        event.Trigger(event.GAME_EVENT_REPORT, "LordOrder", properties)
        
        -- 设置流浪狗的手动目标点
        M.strayDogComp:SetManualTarget(gridX, gridY)
        return true  -- 表示已处理该点击事件
    end
    
    return false  -- 未处理该点击事件
end

-- 处理流浪狗点击事件
function M.OnStrayDogClick()
    if M.strayDogComp then
        M.strayDogComp:OnClick()
        return true
    end
    return false
end

-- 检查点击是否命中流浪狗
function M.IsClickOnStrayDog(worldX, worldY)
    if not M.strayDogComp or util.IsObjNull(M.strayDogComp.transform) then
        return false
    end
    
    local dogPos = M.strayDogComp.transform.position
    local distance = Vector3.Distance(
        Vector3(worldX, dogPos.y, worldY),
        dogPos
    )
    
    -- 检查点击距离是否在流浪狗的交互范围内（0.8个网格单位）
    return distance <= 0.8
end

-- 销毁流浪狗
function M.DestroyStrayDog()
    if M.strayDogComp then
        M.strayDogComp:Recycle()
        M.strayDogComp = nil
    end
    M.strayDogCompId = nil
    print("流浪狗已销毁")
end

-- 清理流浪狗系统
function M.DisposeStrayDogSystem()
    M.DestroyStrayDog()
end

-- 检查流浪狗是否存在
function M.HasStrayDog()
    return M.strayDogComp ~= nil
end

-- 服务器通知相关方法

-- 服务器通知：玩家购买了流浪狗礼包
function M.OnServerNotifyStrayDogUnlocked()
    if not M.HasStrayDog() then
        M.CreateStrayDog()
    end
end

-- 服务器通知：移除流浪狗（如果需要的话）
function M.OnServerNotifyStrayDogRemoved()
    if M.HasStrayDog() then
        M.DestroyStrayDog()
    end
end


return M
