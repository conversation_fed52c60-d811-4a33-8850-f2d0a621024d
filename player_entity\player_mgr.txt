-- player_mgr.txt ------------------------------------------
-- author:  李婉璐
-- date:    2018.11.01
-- ver:     1.0
-- desc:    玩家实体管理
--------------------------------------------------------------
local require = require
local ipairs = ipairs
local pairs = pairs
local table = table
local print = print
local string = string
local tostring = tostring
local dump = dump
local math = math
local type = type
local Time = CS.UnityEngine.Time
local event = require "event"
local prop = require "prop"
local prop_pb = require "prop_pb"
local game_scheme = require "game_scheme"
local skep_mgr = require "skep_mgr"
local util = require "util"
local time_util = require "time_util"
local q1sdk = require "q1sdk"
local hero_prop_enum_data = require "hero_prop_enum_data"
local Adjust = require "adjust"
local Firebase = require "firebase"
local Facebook = require "facebook"
local log = require "log"
local const = require "const"

local PlayerPrefs = CS.UnityEngine.PlayerPrefs


module("player_mgr")
--玩家数据重置类型
userDataResetType = {
    Logout = 1, -- "登出", 
    SwitchServer = 2, -- "切换区服",
    SceneDestroy = 3, -- "场景销毁"  
}
local props = {
    roleName = "", -- 角色名字
    exp = 0, -- 经验
    lv = 0, -- 等级
    coin = 0, -- 金币
    diamond = 0, -- 钻石（点券/玩家花钱购买）
    bindDiamond = 0, -- 绑定钻石	
    buyNum = 0, --购买英雄列表上限次数
    faceID = 0, --头像id
    facePropID = 0, --头像属性id
    vipLevel = 0,
    vipExp = 0, --买的VIP经验
    vipEndTime = 0,
    freeVipExp = 0, --送的VIP经验
    createTime = 0, --创建角色时间
    openSvrTime = 0, --开服时间
    universeStar = 0, --宇宙之星
    galaxyStar = 0, --银河之星
    frameID = 0, --头像框id(头像框属性id)
    oldVipLevel = 0,
    food = 0, --食物
    iron = 0, --铁矿
    power = 0, --战力
    createRoleServerID = 0,    --创角区服id
    fireCnt = 0, --灭火次数
}
local initEventLists = {}--初始化事件列表
local showVip = nil
local isFirst = true
local playerEntity = nil
local HeroIDs = {}--打点上报S+英雄
local ManualUpdateHeroPower = false
local nationalFlagReddot = false --国旗设置红点

local showDiamondTips = true
local showDiamondItemTips = true
local showDroneTips = true
local showActivityBubble = true
local showDismissCongressTips = true

HeroPropModule = hero_prop_enum_data.HeroPropModule
GlobleDirtyConfig = hero_prop_enum_data.UniPropModule

--[[创建玩家实体]]
function CreatePlayerEntity(msg)  
    event.Trigger(event.RECORD_LOGIN_COST_TIME_POINT,"CostTime_CreatePlayerEntity")
    event.Trigger(event.FIRST_LOGIN_OPTIMIZE_REQUIRE_STEP,2)
    --if playerEntity == nil then
    playerEntity = {
        userID = nil, -- 账号id
        roleID = nil, -- 角色id
        playerProp = prop.CProp(props), -- 玩家属性
        packetPartData = {}, -- 背包部件数据（所有物品，包括英雄装备的）
        palPartData = {}, -- 英雄部件数据
        idlePartData = {}, -- 挂机部件数据
        wishPoolPartData = {}, -- 许愿池部件数据
        tavernPartData = {}, -- 酒馆部件数据
        roleFacePartData = {}, -- 角色头像信息部件数据
        heroTransformData = {}, -- 英雄置换数据
        --dungeonData = {},			   	   -- 地牢数据
        techPartData = {}, -- 联盟科技数据
        techResetTimes = {}, -- 联盟科技重置次数
        techResetTimes2 = {}, -- 联盟科技2重置次数
        palHandBookData = {}, -- 英雄图鉴点亮数据
        BiographyData = {}, -- 角色剧情数据

        mazePalPartData = {}, -- 迷宫招募英雄部件数据

        peakPalPartData = {}, -- 时空之巅英雄部件数据

        trialPalPartData = {}, -- 试用英雄部件数据		

        totalBattlePower = 0, -- 玩家排名前6英雄总战斗力
        allHeroBattlePower = 0, -- 所有英雄总战斗力
        maskPhone = "", -- 绑定手机
        nationalFlag = 0, --国家国旗
        flagnextsetime = 0, --国旗设置的cd
        starTempleData = {}, --星罗神殿数据
    }
    --end
    -- 再清除一下Skep数据
    skep_mgr.ClearSkepData()
    -- 设置玩家属性
    SetRoleName(msg.name)
    SetRoleCreateTime(msg.roleCreateTime)
    SetRoleOpenSvrTime(msg.openSvrTime)
    util.PeekWatch("Login", "开始更新玩家数据信息")
    --util.ReportCreateEntityTime("更新玩家数据信息", true)
    for key, propData in ipairs(msg.prop) do
        UpdateNumProp(propData.propid, propData.propvalue, true)
    end
    util.PeekWatch("Login", "更新玩家数据信息完成")
    --util.ReportCreateEntityTime("更新玩家数据信息", false)
    local frame_task_queue_optimize = require "frame_task_queue_optimize" --注意，下面故意不缩进，降低合并冲突概率，等统一确定后再修改缩进  
    for i, partData in ipairs(msg.partData) do
        if frame_task_queue_optimize.optimize_player_part_forbid_frame_config[partData.id] then
           DispatchInitEvent(partData.id, partData.data)
        end
    end
    -- 设置部件数据
    for i, partData in ipairs(msg.partData) do           
        frame_task_queue_optimize.AddSimpleTask(function()
        if not frame_task_queue_optimize.optimize_player_part_forbid_frame_config[partData.id] then
            DispatchInitEvent(partData.id, partData.data)            
        end      
        if partData.id == prop_pb.PERSONPART_PACKET then
            -- 背包
            util.PeekWatch("Login", "开始更新玩家背包信息")
            --util.ReportCreateEntityTime("更新玩家背包信息", true)
            local packetData = prop_pb.TPacketPart()
            packetData:ParseFromString(partData.data)
            -- 容器	
            skep_mgr.SetSkepData(packetData.skepData)

            for i, goodsData in ipairs(packetData.goodsData) do
                UpdatePacketPartData(goodsData)
            end
            util.PeekWatch("Login", "更新玩家背包信息完成")
            --util.ReportCreateEntityTime("更新玩家背包信息", false)
            event.Trigger(event.FORCE_WEAPON_PACKDATA_UPDATE)
            event.Trigger(event.GOODS_PROP_UPDATE_FINISH)
        elseif partData.id == prop_pb.PERSONPART_PAL then
            -- 英雄
            util.PeekWatch("Login", "开始更新英雄信息")
            --util.ReportCreateEntityTime("更新英雄部件", true)
            local palPart = prop_pb.TPalPart()
            palPart:ParseFromString(partData.data)
            -- for i, data in ipairs(palPart.palData) do
            --     UpdatePalPartData(data)
            -- end
            -- local hero_trial_mgr = require "hero_trial_mgr"
            -- print("qsy_yxsy:[player_mgr]CreatePlayerEntity>>>>")
            -- dump(palPart.palTrialData)
            -- hero_trial_mgr.SetHeroTrialData(palPart.palTrialData)
            GameReport()
            util.PeekWatch("Login", "更新英雄部件完成")
            --util.ReportCreateEntityTime("更新英雄部件", false)
            --英雄图鉴点亮数据
            --util.ReportCreateEntityTime("更新英雄图鉴", true)
            UpdatePalHandBookData(palPart.palHBookData)
            util.PeekWatch("Login", "更新英雄图鉴完成")
            --util.ReportCreateEntityTime("更新英雄图鉴", false)
            --角色剧情数据
            --util.ReportCreateEntityTime("更新英雄剧情", true)
            SetBiographyData(palPart.palRolePlotData)
            util.PeekWatch("Login", "更新英雄剧情完成")
            --util.ReportCreateEntityTime("更新英雄剧情", false)
            util.PeekWatch("Login", "更新英雄信息完成")
        elseif partData.id == prop_pb.PERSONPART_IDLE then
            -- 挂机
            util.PeekWatch("Login", "开始更新挂机信息")
            --util.ReportCreateEntityTime("更新挂机信息", true)
            local idlePart = prop_pb.TIdlePart()
            idlePart:ParseFromString(partData.data)
            local laymain_data = require("laymain_data")
            local hook_hero_data = require "hook_hero_data"
            local source_data = require "source_data"

            laymain_data.setPassLevel(idlePart.passStage)
            laymain_data.setHangLevel(idlePart.idleStage)

            laymain_data.SetStarReward(idlePart.starReward)--玩家领取了多少星星的奖励 
            laymain_data.SetIdleReward(idlePart.idleReward)--玩家领取了多少关卡的奖励
            laymain_data.SetStarNum(idlePart.starNum)--星星总数
            laymain_data.SetIdleStar(idlePart.idleStar)--每一关的星星数
            laymain_data.SetGiftIdleReward(idlePart.giftIdleReward)--付费关卡宝箱ID
            laymain_data.SetGiftStarReward(idlePart.giftStarReward)--付费星星宝箱ID
            laymain_data.InitLaymainData(nil, idlePart.passStage)
            source_data.refreshRewardInfo(idlePart)
            hook_hero_data.SetSaveHeroData(idlePart.pals)
            event.Trigger(event.CHANGE_PASS_LEVEL, idlePart.passStage)
            event.Trigger(event.CHANGE_IDLE_MAP, idlePart.passStage)
            util.PeekWatch("Login", "更新挂机信息完成")
            -- util.ReportCreateEntityTime("更新挂机信息", false)
        elseif partData.id == prop_pb.PERSONPART_LOTTERY_WISH then
            -- -- 许愿池
            -- util.PeekWatch("Login", "开始更新许愿池信息")
            -- --util.ReportCreateEntityTime("更新许愿池信息", true)
            -- local wishPoolPart = lotterywish_pb.TPersonLotteryWishPartData()
            -- wishPoolPart:ParseFromString(partData.data)
            -- local lottery_data = require "lottery_data"
            -- for i, data in ipairs(wishPoolPart.arrLotteryWishData) do
            --     UpdateWishPartData(data.iType, data)
            --     lottery_data.UpdateLotteryWishData(data.iType, data)
            -- end
            -- util.PeekWatch("Login", "更新许愿池信息完成")
            --util.ReportCreateEntityTime("更新许愿池信息", false)
        elseif partData.id == prop_pb.PERSONPART_TAVERN then
            -- 酒馆
            -- util.PeekWatch("Login", "开始更新酒馆信息")
            -- --util.ReportCreateEntityTime("更新酒馆信息", true)
            -- local tavernPart = tavern_pb.TTavernPartData()
            -- tavernPart:ParseFromString(partData.data)
            -- UpdateTavernPartData(tavernPart)
            -- util.PeekWatch("Login", "更新酒馆信息完成")
            --util.ReportCreateEntityTime("更新酒馆信息", false)
        elseif partData.id == prop_pb.PERSONPART_FACEDATA then
            -- 角色信息
            util.PeekWatch("Login", "开始更新角色信息")
            --util.ReportCreateEntityTime("更新角色信息", true)
            local roleFacePart = prop_pb.TFaceDataPart()
            roleFacePart:ParseFromString(partData.data)
            UpdateRoleFacePartData(roleFacePart)
            util.PeekWatch("Login", "更新角色信息完成")
            --util.ReportCreateEntityTime("更新角色信息", false)
        elseif partData.id == prop_pb.PERSONPART_SUBJECT then
            --			--任务
            util.PeekWatch("Login", "开始更新任务信息")

            --util.ReportCreateEntityTime("更新任务topic数据", true)
            local taskData = prop_pb.TSubjectPart()
            taskData:ParseFromString(partData.data)
            local task_data = require "task_data"
            -- local target_task_data = require "target_task_data" -- 指挥官之路相关
            -- local day7_challenge_data = require "day7_challenge_data" -- 七天挑战
            if taskData.topicData then
                -- for i, value in ipairs(taskData.topicData) do
                --     day7_challenge_data.SetTaskData(value)
                --     -- activity_mgr.SetTaskData(value)
                --     --------print("任务",value.topicID,value.data)
                -- end
                task_data.CreateTopicData(taskData.topicData)
                -- target_task_data.CreateTopicData(taskData.topicData)
            end
            --util.ReportCreateEntityTime("更新任务topic数据", false)

            --util.ReportCreateEntityTime("更新任务新手引导数据", true)
            --新手引导数据
            if taskData.guideData then
                local force_guide_data = require "force_guide_data"
                force_guide_data.SetRawData(taskData.guideData)
            end
            --util.ReportCreateEntityTime("更新任务新手引导数据", false)

            --util.ReportCreateEntityTime("更新任务关卡剧情数据", true)
            -- 关卡剧情数据			
            -- if taskData.plotData and #taskData.plotData > 0 then
            --     local hook_hero_data = require "hook_hero_data"
            --     for i, value in ipairs(taskData.plotData) do
            --         hook_hero_data.SetPlotData(value)
            --     end
            -- end
            --util.ReportCreateEntityTime("更新任务关卡剧情数据", false)

            util.PeekWatch("Login", "更新任务信息完成")
        elseif partData.id == prop_pb.PERSONPART_HEROTRANSFORM then
            --英雄置换
            -- util.PeekWatch("Login", "开始更新英雄置换信息")
            -- --util.ReportCreateEntityTime("更新英雄置换信息", true)
            -- local heroTransformPart = herotransform_pb.THeroTransformPartData()
            -- if partData.data then
            --     heroTransformPart:ParseFromString(partData.data)
            --     UpdateHeroTransformPartData(heroTransformPart.lastTransform)
            -- end
            -- util.PeekWatch("Login", "更新英雄置换信息完成")
            --util.ReportCreateEntityTime("更新英雄置换信息", false)
        -- elseif partData.id == prop_pb.PERSONPART_ASHDUNGEON then
        --     --- 地牢
        --     util.PeekWatch("Login", "开始更新地牢信息")
        --     --util.ReportCreateEntityTime("更新地牢信息", true)
        --     local ashdungeon_pb = require "ashdungeon_pb"
        --     local data = ashdungeon_pb.TAshDungeonPartData()
        --     data:ParseFromString(partData.data)
        --     local dungeon_mgr = require "dungeon_mgr"
        --     --playerEntity.dungeonData = dungeon_mgr.parseTAshDungeonPartData(data)
        --     dungeon_mgr.OnS2CUpdateData({ data = data })
        --     -- dump(playerEntity.dungeonData )
        --     --dungeon_mgr.init()

        --     util.PeekWatch("Login", "更新地牢信息完成")
        --     --util.ReportCreateEntityTime("更新地牢信息", false)
        -- elseif partData.id == prop_pb.PERSONPART_TRIAL then
        --     -- 勇者试炼
        --     util.PeekWatch("Login", "开始更新勇者试炼信息")
        --     --util.ReportCreateEntityTime("更新勇者试炼信息", true)
        --     local trialData = trialpro_pb.TTrialPartData()
        --     trialData:ParseFromString(partData.data)
        --     if trialData:HasField("stData") then
        --         -- 达到等级开发权限
        --         local trial_mgr = require "trial_mgr"
        --         trial_mgr.UpdateTrialData(trialData.stData)
        --     end
        --     util.PeekWatch("Login", "更新勇者试炼信息完成")
        --     --util.ReportCreateEntityTime("更新勇者试炼信息", false)
        elseif partData.id == prop_pb.PERSONPART_ALLIEDTECH then
            -- -- 联盟科技
            -- --------print("联盟科技>>>>>>>>>>>>>>>>>>>>>>")
            -- util.PeekWatch("Login", "开始更新联盟科技信息")
            -- --util.ReportCreateEntityTime("更新联盟科技信息", true)
            -- local techPartData = prop_pb.TAllianceTechPart()
            -- techPartData:ParseFromString(partData.data)
            -- UpdateTechPartData(techPartData)
            -- util.PeekWatch("Login", "更新联盟科技信息完成")
            --util.ReportCreateEntityTime("更新联盟科技信息", false)
        elseif partData.id == prop_pb.PERSONPART_ACTIVITY then
            --成长礼包2数据
            util.PeekWatch("Login", "开始更新成长礼包2信息")
            local activity_pb = require "activity_pb"
            local giftData = activity_pb.TMSG_ACTIVITY_GROW_GIFT2_STATE()
            giftData:ParseFromString(partData.data)
            local activity_mgr = require "activity_mgr"
            activity_mgr.OnGrowGift2RSP(giftData)
            util.PeekWatch("Login", "更新成长礼包2信息完成")
        elseif partData.id == prop_pb.PERSONPART_HALO then
            -- -- 光环
            -- util.PeekWatch("Login", "开始更新光环信息")
            -- --util.ReportCreateEntityTime("更新光环信息", true)
            -- local haloPartData = prop_pb.THaloDataPart()
            -- haloPartData:ParseFromString(partData.data)
            -- local halo_mgr = require "halo_mgr"
            -- halo_mgr.UpdateHaloPartData(haloPartData)
            -- util.PeekWatch("Login", "更新光环信息完成")
            --util.ReportCreateEntityTime("更新光环信息", false)
        elseif partData.id == prop_pb.PERSONPART_FARM then
            -- -- 时空幻域之 奇迹农场
            -- util.PeekWatch("Login", "开始更新奇迹农场信息")
            -- --util.ReportCreateEntityTime("更新奇迹农场信息", true)
            -- local framPartData = prop_pb.TFramDataPart()
            -- framPartData:ParseFromString(partData.data)
            -- local timearea_data = require "timearea_data"
            -- timearea_data.SetFarmData(framPartData)
            -- local homeland_mgr = require "homeland_mgr"
            -- homeland_mgr.SetMyHomelandData(framPartData)
            -- util.PeekWatch("Login", "更新奇迹农场信息完成")
            --util.ReportCreateEntityTime("更新奇迹农场信息", false)
        elseif partData.id == prop_pb.PERSONPART_WEAPON then
            -- -- 武器部件
            -- --------print("接收到武器部件")
            -- util.PeekWatch("Login", "开始更新武器信息")
            -- --util.ReportCreateEntityTime("更新武器信息", true)
            -- local weaponPartData = prop_pb.TWeaponDataPart()
            -- weaponPartData:ParseFromString(partData.data)
            -- -- local weapon_data = require "weapon_data"
            -- event.Trigger(event.UPDATE_WEAPON_LIST, weaponPartData.weapon)
            -- util.PeekWatch("Login", "更新武器信息完成")
            -- --util.ReportCreateEntityTime("更新武器信息", false)
        elseif partData.id == prop_pb.PERSONPART_LOTTERY then
            --------print("PERSONPART_LOTTERY===============================")
            -- util.PeekWatch("Login", "开始更新抽奖信息")
            -- --util.ReportCreateEntityTime("更新抽奖信息", true)
            -- local lottery_pb = require "lottery_pb"
            -- local hero_lottery_data = require "hero_lottery_data"
            -- local LobPersonLotteryPartData = lottery_pb.TLobPersonLotteryPartData()
            -- LobPersonLotteryPartData:ParseFromString(partData.data)
            -- hero_lottery_data.UpdateLotteryData(LobPersonLotteryPartData)
            -- event.Trigger(event.CHECK_DATA_FINISH, "lottery")-- 弹窗数据记录
            -- util.PeekWatch("Login", "更新抽奖信息完成")
            --util.ReportCreateEntityTime("更新抽奖信息", false)
        -- elseif partData.id == prop_pb.PERSONPART_MAZE then
            -- -- 异界迷宫
            -- util.PeekWatch("Login", "开始更新异界迷宫信息")
            -- --util.ReportCreateEntityTime("更新异界迷宫信息", true)
            -- local mazepro_pb = require "mazepro_pb"
            -- local mazePartData = mazepro_pb.TMazePartData()
            -- mazePartData:ParseFromString(partData.data)
            -- event.Trigger(event.UPDATE_MAZE_PART_DATA, mazePartData)
            -- util.PeekWatch("Login", "更新异界迷宫信息完成")
            -- print("PERSONPART_MAZE",mazePartData)
            --util.ReportCreateEntityTime("更新异界迷宫信息", false)
        elseif partData.id == prop_pb.PERSONPART_PASSPORT then
            -- -- 通行证
            -- util.PeekWatch("Login", "开始更新通行证信息")
            -- --util.ReportCreateEntityTime("更新通行证信息", true)
            -- local passport_pb = require "passport_pb"
            -- local passportPartData = passport_pb.TPassportPartData()
            -- passportPartData:ParseFromString(partData.data)
            -- event.Trigger(event.UPDATE_PASSPORT_PART_DATA, passportPartData)
            -- util.PeekWatch("Login", "更新通行证信息完成")
            --util.ReportCreateEntityTime("更新通行证信息", false)
        elseif partData.id == prop_pb.PERSONPART_SOULLINK then
            -- -- 灵魂链接
            -- util.PeekWatch("Login", "开始更新灵魂链接信息")
            -- --util.ReportCreateEntityTime("更新灵魂链接信息", true)
            -- local soulLinkPartData = prop_pb.TSoulLinkMoudleDataPart()
            -- soulLinkPartData:ParseFromString(partData.data)
            -- local st = PlayerPrefs.GetInt("dump_msg", 0)
            -- if st == 1 then
            --     log.Warning("CreatePlayerEntity soulLinkPartData", util.pb2str(soulLinkPartData))
            -- end
            -- UpdatePalPriestData(soulLinkPartData.flamenSidGroup)
            -- SetSoulLinkingHeroSourceLevel(soulLinkPartData.slotStat)
            -- local ui_soul_link_mgr = require "ui_soul_link_mgr"
            -- ui_soul_link_mgr.InitSoulLinkData("update_soul_link_data", soulLinkPartData)
            -- event.Trigger(event.UPDATE_SOUL_LINK_DATA, soulLinkPartData)--问题：可能触发器失效导致数据未保存
            -- util.PeekWatch("Login", "更新灵魂链接信息完成")
            --util.ReportCreateEntityTime("更新灵魂链接信息", false)
        -- elseif partData.id == prop_pb.PERSONPART_PEAK then
        --     util.PeekWatch("Login", "开始更新时光之巅信息")
        --     --util.ReportCreateEntityTime("更新时光之巅信息", true)
        --     local peak_pb = require "peak_pb"
        --     local data = peak_pb.TPeakPartData()
        --     data:ParseFromString(partData.data)
        --     local peak_mgr = require "peak_mgr"
        --     peak_mgr.OnPeakUpdateNtf(data)
        --     util.PeekWatch("Login", "更新时光之巅信息完成")
            --util.ReportCreateEntityTime("更新时光之巅信息", false)
        elseif partData.id == prop_pb.PERSONPART_SEVENCHALLENGE then
            -- -- 七天挑战
            -- util.PeekWatch("Login", "开始更新七天挑战信息")
            -- --util.ReportCreateEntityTime("更新七天挑战信息", true)
            -- local data = prop_pb.TSevenChallengePartData()
            -- data:ParseFromString(partData.data)
            -- local day7_challenge_data = require "day7_challenge_data"
            -- day7_challenge_data.OnSeventUpdateNtf(data)
            -- -- TODO 更新七天挑战界面数据
            -- -- event.Trigger(event.UPDATE_SOUL_LINK_DATA, soulLinkPartData)
            -- util.PeekWatch("Login", "更新七天挑战信息完成")
            --util.ReportCreateEntityTime("更新七天挑战信息", false)
        elseif partData.id == prop_pb.PERSONPART_OPERATIVE then
            util.PeekWatch("Login", "开始更新公测活动信息")
            --util.ReportCreateEntityTime("更新公测活动信息", true)
            local data = prop_pb.TMSG_OPERATIVE_PART_DATA()
            data:ParseFromString(partData.data)
            local open_test_mgr = require "open_test_mgr"
            open_test_mgr.InitData(data)
            util.PeekWatch("Login", "更新公测活动信息完成")
            --util.ReportCreateEntityTime("更新公测活动信息", false)
        elseif partData.id == prop_pb.PERSONPART_MERGESERVER then
            -- util.PeekWatch("Login", "开始更新合服活动")
            -- --util.ReportCreateEntityTime("更新合服活动", true)
            -- local mergeServer_pb = require "mergeServer_pb"
            -- local data = mergeServer_pb.TMergeData()
            -- data:ParseFromString(partData.data)
            -- local return_user_data = require "return_user_data"
            -- return_user_data.SetCombieServerInfo(data)
            -- util.PeekWatch("Login", "更新合服活动完成")
            --util.ReportCreateEntityTime("更新合服活动", false)
        elseif partData.id == prop_pb.PERSONPART_RETURNACT then
            ----by bxz
            --log.Error("该部件数据本地已屏蔽，回归活动部件id=",partData.id)
            ----util.PeekWatch("Login", "开始更新回归活动")
            ------util.ReportCreateEntityTime("更新回归活动", true)
            ----local returnAct_pb = require "returnAct_pb"
            ----local data = returnAct_pb.TReturnActData()
            ----data:ParseFromString(partData.data)
            ----local new_return_player_mgr = require "new_return_player_mgr"
            ----new_return_player_mgr.SetReturnPlayerData(data)
            ----util.PeekWatch("Login", "更新回归活动完成")
            ------util.ReportCreateEntityTime("更新回归活动", false)
        -- elseif partData.id == prop_pb.PERSONPART_SPACEEXPLORATION then
        --     util.PeekWatch("Login", "开始更新星际探索数据")
        --     local data = prop_pb.TSpaceExplorationDataPart()
        --     data:ParseFromString(partData.data)
        --     local space_exploration_mgr = require "space_exploration_mgr"
        --     space_exploration_mgr.SetSpaceExplorationData(data)
        --     util.PeekWatch("Login", "更新星际探索数据完成")
        -- elseif partData.id == prop_pb.PERSONPART_BATTLESHIPTECH then
        --     util.PeekWatch("Login", "开始更新战舰科技数据")
        --     local data = prop_pb.TBattleshipTechDataPart()
        --     data:ParseFromString(partData.data)
        --     local space_exploration_mgr = require "space_exploration_mgr"
        --     space_exploration_mgr.SetWarShipTechnologyData(data)
        --     local hero_prop_mgr = require "hero_prop_mgr"
        --     hero_prop_mgr.SetGlobleDirty(hero_prop_mgr.GlobleDirtyConfig.WantedProperty)
        --     util.PeekWatch("Login", "更新战舰科技数据完成")
        -- elseif partData.id == prop_pb.PERSONPART_CHINARED then
        --     util.PeekWatch("Login", "开始更新舰队远征数据")
        --     local chinared_pb = require "chinared_pb"
        --     local data = chinared_pb.TMSG_CHINARED_UPDATE_PART_NTF()
        --     data:ParseFromString(partData.data)
        --     local fleet_expedition_data = require "fleet_expedition_data"
        --     fleet_expedition_data.SetPartData(data)
        --     util.PeekWatch("Login", "更新舰队远征数据完成")
        elseif partData.id == prop_pb.PERSONPART_SHOP_DECORATE then
            -- util.PeekWatch("Login", "开始更新饰品工坊数据")
            -- local decoration_mgr = require "decoration_mgr"
            -- local lottery_pb = require "lottery_pb"
            -- local data = lottery_pb.TMSG_DERACOTE_SHOP_GET_INFO_RSP()
            -- data:ParseFromString(partData.data)
            -- decoration_mgr.Response_Get_Decarote_Info(data)
            -- util.PeekWatch("Login", "更新饰品工坊数据完成")
        elseif partData.id == prop_pb.PERSONPART_TREASURERARE then
            -- -- 轮回秘宝数据
            -- util.PeekWatch("Login", "开始更新轮回秘宝数据")
            -- local data = prop_pb.TTreasureRarePart()
            -- data:ParseFromString(partData.data)
            -- -- dump(partData.data)
            -- local treasure_rare_mgr = require "treasure_rare_mgr"
            -- local net_treasuer_rare_module = require "net_treasuer_rare_module"
            -- net_treasuer_rare_module.MSG_TREASURERARE_OPENACTIVITY_REQ()
            -- treasure_rare_mgr.UpdateRareData(data)
            -- util.PeekWatch("Login", "更新轮回秘宝数据完成")
        -- elseif partData.id == prop_pb.PERSONPART_STARTEMPLE then
        --     --星罗神殿数据
        --     util.PeekWatch("Login", "开始更新星罗神殿数据数据")
        --     local data = prop_pb.TStarTempleDataPart()
        --     data:ParseFromString(partData.data)
        --     --dump(partData.data)
        --     local star_temple_game_data = require "star_temple_game_data"
        --     --local net_star_temple_module = require "net_star_temple_module"
        --     --net_star_temple_module.StarTempleBaseReq()
        --     star_temple_game_data.UpdatePartData(data)
        --     util.PeekWatch("Login", "更新星罗神殿数据数据完成")
        elseif partData.id == prop_pb.PERSONPART_PICKTHEROUTE then
            --log.Error("该部件数据本地已屏蔽，双旦救援活动部件id=",partData.id)
            ----双旦救援活动数据
            ----util.PeekWatch("Login", "开始更新双旦救援数据数据")
            ----local data = prop_pb.TPickTheRoute()
            ----data:ParseFromString(partData.data)
            ----local pick_the_route_mgr = require "pick_the_route_mgr"
            ----pick_the_route_mgr.SetArchivedData(data.activityID, data.stLevelArchived)
            ----util.PeekWatch("Login", "更新双旦救援数据数据完成")

        elseif partData.id == prop_pb.PERSONPART_XYX then
            -- 小游戏数据
            util.PeekWatch("Login", "小游戏数据开始更新")
            local activity_pb = require "activity_pb"
            local data = activity_pb.TBXyxBasePartData()
            data:ParseFromString(partData.data)
            --[[local puzzlegame_mgr = require "puzzlegame_mgr"
            puzzlegame_mgr.UpdatePartData(data)]]
            local gw_independent_game_data = require "gw_independent_game_data"
            gw_independent_game_data.UpdatePartData(data)
            
            
            
            util.PeekWatch("Login", "小游戏数据更新完成")

        elseif partData.id == prop_pb.PERSONPART_SCIENTIFICRESEARCH then
            local ScientificResearch_pb = require "ScientificResearch_pb"
            local data = ScientificResearch_pb.TPbScientificResearchPartData()
            data:ParseFromString(partData.data)
            --[[
            for j,v in ipairs(data.scientificBuildData) do
                log.Error("#############")
                log.Error(v.buildID)
                log.Error(v.curTypeID)
                log.Error(v.curScienticID)
                log.Error(v.curResearchCompleteTime)
                log.Error("###########")
            end
            ]]
            --[[
            for i,v in ipairs(data.scientificData) do
                log.Error("____________")
                log.Error(v.typeID)
                log.Error(v.scientificID)
                log.Error(v.scientificLv)
                log.Error("____________")
            end
            ]]
            local technology_data = require "technology_data"
            technology_data.InitConfig()
            technology_data.SetData(data)
        elseif partData.id == prop_pb.PERSONPART_DRONECENTER then
            --local DroneCenter_pb = require "DroneCenter_pb"
            --local data = DroneCenter_pb.TDroneDataPart()
            --data:ParseFromString(partData.data)
            --for j,v in ipairs(data.droneInfo) do
            --    log.Error(v.nDroneId)
            --    log.Error(v.bActive)
            --    for k,l in ipairs(v.part) do
            --        log.Error(l.bLock)
            --        log.Error(l.partType)
            --        log.Error(l.nLv)
            --    end
            --    for k,l in ipairs(v.props) do
            --        log.Error(l.propid)
            --        log.Error(l.propvalue)
            --    end
            --end
            local gw_home_drone_data = require "gw_home_drone_data"
            gw_home_drone_data.Init()
            gw_home_drone_data.OnSetDroneData(partData.data)
        elseif partData.id == prop_pb.PERSONPART_RECHARGEGIFT then
            --充值礼包
            local rechargeGift_pb = require "rechargeGift_pb"
            local data = rechargeGift_pb.TPbRechargeGiftPart()
            data:ParseFromString(partData.data)
            local gw_recharge_mgr = require "gw_recharge_mgr"
            gw_recharge_mgr.SetRechargeData(data.data)
        end            
        end)
    end
    
    frame_task_queue_optimize.AddSimpleTask(function()
    --util.ReportCreateEntityTime("请求在线奖励", true)
    -- local net_onlineReward_module = require "net_onlineReward_module"
    -- net_onlineReward_module.ReqUpdateUI(0, 10003, 30)
    --util.ReportCreateEntityTime("请求在线奖励", false)


    --util.ReportCreateEntityTime("请求狩猎关卡数据", true)
    local send_storyMsg = require "send_storyMsg"
    send_storyMsg.OnEnterStroyReq()
    --util.ReportCreateEntityTime("请求狩猎关卡数据", false)
    -- local net_topgame_module = require "net_topgame_module"
    -- net_topgame_module.Send_TOPRACE_DATA(2)
    -- net_topgame_module.Send_TOPRACE_DATA(1)

    --util.ReportCreateEntityTime("请求竞技场数据", true)
    --local net_arena_module = require "net_arena_module"
    --local common_new_pb = require "common_new_pb"
    --net_arena_module.Send_ARENA_DEFEND_LINEUP(common_new_pb.CrystalCrown)
    --net_arena_module.Send_ARENA_DEFEND_LINEUP(common_new_pb.Advance)

    --util.ReportCreateEntityTime("请求竞技场数据", false)

    --util.ReportCreateEntityTime("请求好友数据", true)
    -- Todo 修改 等级改成关卡
    -- local net_friend_module = require "net_friend_module"
    -- net_friend_module.Send_Friend_List()
    --util.ReportCreateEntityTime("请求好友数据", false)

    -- local net_slave_module = require "net_slave_module"
    -- net_slave_module.Send_Salve_List_Info(GetPlayerRoleID())
    -- net_slave_module.ReqGetAllSlaveMail()
    --util.ReportCreateEntityTime("请求苦力数据", false)

    --请求军备竞赛数据
    local gw_arm_competition_activity_data = require "gw_arm_competition_activity_data"
    gw_arm_competition_activity_data.GetTaskData()
    gw_arm_competition_activity_data.OnGetArmCompetitionInfo()
        
    --请求狂暴领主数据
    local gw_madness_lord_mgr = require "gw_madness_lord_mgr"
    gw_madness_lord_mgr.OnGetActivityDataReq()
        
    --local city_siege_activity_data = require "city_siege_activity_data"
    --city_siege_activity_data.OnGetCitySiegeInfo()

    --请求关卡玩家数据
    -- local hook_level_tournament_mgr = require "hook_level_tournament_mgr"
    -- hook_level_tournament_mgr.StartUpdateHookLevelRoleData()

    --请求杀戮场数据
    -- local secretplace_mgr = require "secretplace_mgr"
    -- secretplace_mgr.Single_Info_REQ()
    -- secretplace_mgr.Multi_Info_REQ()

    -- 初始化印记相关配置
    -- local sigil_mgr = require "sigil_mgr"
    -- sigil_mgr.InitSigilData()

    --试炼之地数据初始化
    -- local faction_wanted_mgr = require "faction_wanted_mgr"
    -- faction_wanted_mgr.InitPrepareData()

    --邮件订阅数据
    -- local email_subscribe_mgr = require "email_subscribe_mgr"
    -- if email_subscribe_mgr.GetIsCreateRoleOverTime() then
    --     email_subscribe_mgr.RequestIsReward()
    --     email_subscribe_mgr.RequestIsBind()
    -- else
    --     --未开放隐藏
    --     event.Trigger(event.UPDATE_EMAIL_SUBSCRIBE_REWARD_VIEW)
    -- end
    --
    --util.YieldFrame(function()
    --    event.Trigger(event.FIRST_LOGIN_CREATE_DATA_FINISH)
    --end)

    ----TODO 在这里获取军备竞赛的数据……
    --local gw_arm_competition_activity_data = require "gw_arm_competition_activity_data"
    --gw_arm_competition_activity_data.Init()

    -- ios提审版本发送灵石命令，玩家数据由服务器模板刷新，暂时屏蔽灵石请求
    --local ReviewingUtil = require "ReviewingUtil"
    --if ReviewingUtil.IsReviewing() then
    --	if playerEntity.playerProp.lv == 1 then
    --		local msg = goldfinger_pb.TMSG_GOLD_FINGER_NTF()
    --		msg.commandLine = "modifylv 69"
    --		net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, msg_pb.MSG_GOLD_FINGER_NTF, msg)
    --		msg = goldfinger_pb.TMSG_GOLD_FINGER_NTF()
    --		msg.commandLine = "modifyIdleStage 1320"
    --		net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, msg_pb.MSG_GOLD_FINGER_NTF, msg)
    --	end
    --end
    event.Trigger(event.RECORD_LOGIN_COST_TIME_POINT,"CostTime_playerCreateEnd",true)
    end,nil,function()
        --当最后一个分帧完成时，关闭优化
        frame_task_queue_optimize.SetOptimizeActive(false)       
    end)
end

function GetPlayerEnemyKill()
    -- todo 设置杀敌数
    return 0
end

function SetPlayerEnemyKill(enemyKill)
    -- todo 设置杀敌数
end

function GetPlayerBattlePower()
    if playerEntity == nil then
        return 0
    end
    return playerEntity.totalBattlePower
end

function SetPlayerBattlePower(power)
    if playerEntity == nil then
        log.Error("获得玩家数据前错误设置战斗力")
    else
        if playerEntity.totalBattlePower == power then
            return
        end
        playerEntity.totalBattlePower = power
        event.Trigger(event.PLAYER_BATTLE_POWER_UPDATE, power)
    end
end

function GetPlayerAllHeroBattlePower()
    if playerEntity == nil then
        return 0
    end
    return playerEntity.allHeroBattlePower
end

function SetPlayerAllHeroBattlePower(power)
    if playerEntity == nil then
        log.Error("获得玩家数据前错误设置所有英雄总战斗力")
    else
        if playerEntity.allHeroBattlePower == power then
            return
        end
        playerEntity.allHeroBattlePower = power
    end
    event.Trigger(event.PLAYER_ALL_HERO_POWER_UPDATE, power)
end

--[[英雄身上的物品增删改时需要改变战斗属性值]]
function UpdateHeroBattleProp(skepID, goodsSid)
    local heroSids = {}
    local heroSid = nil
    -- 装备
    if skep_mgr.GetSkepType(skepID) == prop_pb.SKEP_TYPE_PALEQUIP then
        heroSid = GetHeroOfEquipSkep(skepID)
        table.insert(heroSids, heroSid)

        --@TODO 2019-10-22 prop_pb.SKEP_TYPE_MAZE ???
        heroSid = GetMazeHeroOfEquipSkep(skepID)
        table.insert(heroSids, heroSid)
    elseif skep_mgr.GetSkepType(skepID) == prop_pb.SKEP_TYPE_PALSIGIL then
        -- 印记
        heroSid = GetHeroOfSigilSkep(skepID)
        table.insert(heroSids, heroSid)
    end
    -- 技能
    if skep_mgr.GetSkepType(skepID) == prop_pb.SKEP_TYPE_PALSKILL
            or skep_mgr.GetSkepType(skepID) == prop_pb.SKEP_TYPE_MAZETEMP
            or skep_mgr.GetSkepType(skepID) == prop_pb.SKEP_TYPE_PEAKTEMP
            or skep_mgr.GetSkepType(skepID) == prop_pb.SKEP_TYPE_PALDECORATE
    then
        local goodsEntity = GetPacketPartDataBySid(goodsSid)
        if goodsEntity then
            for i, v in pairs(goodsEntity:GetHeroSkillSkeps()) do
                if v and v ~= 0 then
                    table.insert(heroSids, v)
                end
            end
        end
    end

    -- local hero_prop_mgr = require "hero_prop_mgr"
    -- local calpro_mgr = require "calpro_mgr"
    -- for i, heroSid in ipairs(heroSids) do
    --     local entity = GetPalPartDataBySid(heroSid) or GetMazePalPartDataBySid(heroSid) or GetPeakPalPartDataBySid(heroSid)
    --     if entity then
    --         if const.USE_NEW_HERO_PROP_COUNT then
    --             hero_prop_mgr.SetHeroDrity(entity.heroSid, hero_prop_mgr.HeroPropModule.equipment)
    --             hero_prop_mgr.SetHeroDrity(entity.heroSid, hero_prop_mgr.HeroPropModule.godExclusiveEquipment)
    --             hero_prop_mgr.SetHeroDrity(entity.heroSid, hero_prop_mgr.HeroPropModule.enhanceEquipment)
    --             hero_prop_mgr.DelayCallRecalculate(entity, true)
    --         else
    --             calpro_mgr.CalculateHeroProp(heroSid, entity.numProp.stepLV, entity.numProp.starLv, true)
    --         end
    --     end
    -- end
end

--[[更新属性数据]]
function UpdateEntityProp(data)
    if not playerEntity then
        return
    end
    if data.enType == prop_pb.enPerson then
        -- 人物
        for key, propData in ipairs(data.prop) do
            UpdateNumProp(propData.propid, propData.propvalue, false)
        end

        if data:HasField("name") then
            SetRoleName(data.name)
        end
    elseif data.enType == prop_pb.enHero then
        -- 英雄
        -- local hEntity = playerEntity.palPartData[data.sid] or playerEntity.mazePalPartData[data.sid] or playerEntity.peakPalPartData[data.sid]
        -- if hEntity then
        --     local reCal = false
        --     for key, propData in ipairs(data.prop) do
        --         if propData.propid == prop_pb.PAL_PROP_LV or propData.propid == prop_pb.PAL_PROP_STEP_LV or propData.propid == prop_pb.PAL_PROP_SKILL_LV_3 or propData.propid == prop_pb.PAL_PROP_SKILL_LV_4 or propData.propid == prop_pb.PAL_PROP_STAR_LV then
        --             reCal = true
        --         end
        --         hEntity:UpdateHeroNumProp(propData.propid, propData.propvalue)
        --     end
        --     -- --判断是否手动更新战力模式
        --     -- if not ManualUpdateHeroPower then
        --     --     -- 属性变化时重算战斗属性
        --     --     if const.USE_NEW_HERO_PROP_COUNT then
        --     --         local tab = { HeroPropModule.equipment, HeroPropModule.godExclusiveEquipment, HeroPropModule.enhanceEquipment, HeroPropModule.decoration,
        --     --                       HeroPropModule.base, HeroPropModule.customLevel, HeroPropModule.skill }
        --     --         event.Trigger(event.RECALCULATE_HERO_POWER, tab, hEntity.heroSid, hEntity)
        --     --     else
        --     --         local calpro_mgr = require "calpro_mgr"
        --     --         calpro_mgr.CalculateHeroProp(hEntity.heroSid, hEntity.numProp.stepLV, hEntity.numProp.starLv, true)
        --     --     end
        --     -- end
        -- end
    elseif data.enType == prop_pb.enGoods then
        -- 物品 Todo:要考虑如果在英雄身上，需要重新计算属性值
        local pEntity = playerEntity.packetPartData[data.sid]
        if pEntity then
            local tempSkepId = pEntity.numProp.skepID
            --英雄身上的物品信息变化时要改变战斗属性值

            for key, propData in ipairs(data.prop) do
                pEntity:UpdateGoodsProp(propData.propid, propData.propvalue)
                if (pEntity.goodsID == skep_mgr.const_id.unionCoin or pEntity.goodsID == skep_mgr.const_id.fessionAlloy) and propData.propid == prop_pb.GOODS_PROP_QTY then
                    UpdateSociatyTechnologyRed()
                end
            end
            if skep_mgr.GetSkepType(tempSkepId) ~= prop_pb.SKEP_TYPE_PALEQUIP and skep_mgr.GetSkepType(tempSkepId) ~= prop_pb.SKEP_TYPE_PALSKILL and skep_mgr.GetSkepType(tempSkepId) ~= prop_pb.SKEP_TYPE_PALSIGIL then
                --如果是穿装，物品的skepID发生改变，从背包物品栏SkepID变成英雄物品栏SkepID
                tempSkepId = pEntity.numProp.skepID
            end

            UpdateHeroBattleProp(tempSkepId, data.sid)
            event.Trigger(event.FORCE_WEAPON_PACKDATA_UPDATE)
            event.Trigger(event.GOODS_PROP_UPDATE_FINISH)
        end
    end
end

function SetHeroPowerManualMode(state)
    ManualUpdateHeroPower = state
end

--[[销毁物品或英雄数据]]
function DestroyEntityPart(eType, sids)
    if eType == prop_pb.enGoods then
        for i, sid in ipairs(sids) do
            DestroyPacketPart(sid)
        end
        event.Trigger(event.DELETE_GOODS_UPDATE_EVENT)
    elseif eType == prop_pb.enHero then
        for i, sid in ipairs(sids) do
            event.Trigger(event.DESTORY_HERO_ENTITY, sid)
            DestroyHeroPart(sid)
        end
    end
end

--[[销毁玩家实体]]
function DestroyPlayerEntity()
    playerEntity = nil
end

--[[获取玩家实体]]
function GetPlayerEntity()
    return playerEntity
end

--[[获取角色名字]]
function GetRoleName()
    if playerEntity then
        return playerEntity.playerProp.roleName
    end
    return ""
end
--[[获取创角时间]]
function GetRoleCreateTime()
    if playerEntity then
        return playerEntity.playerProp.createTime
    end
    return 0
end

--获取创角天数(从创角的零点开始算)
function GetRoleCreateDaysFromZeroTime()
    return time_util.GetDayOffsetBySecond(time_util.GetServerTime0(), time_util.GetServerTime0(GetRoleCreateTime()))+1
end

--[[获取创角时间]]
function GetRoleCreateDays()
    local net_login_module = require "net_login_module"

    return time_util.GetDayOffsetBySecond(net_login_module.GetServerTime(), GetRoleCreateTime())
end

--[[获取开服时间]]
function GetRoleOpenSvrTime()
    if playerEntity then
        return playerEntity.playerProp.openSvrTime
    end
    return 0
end

--[[获取服务器开服天数 单位为秒]]
function GetServerOpenDaysBySecond()
    local net_login_module = require "net_login_module"

    return time_util.GetDayOffsetBySecond(net_login_module.GetServerTime(), GetRoleOpenSvrTime())
end

--[[获取角色id]]
function GetPlayerRoleID()
    if playerEntity then
        return playerEntity.roleID
    end
    return 0
end

--[[获取账号id]]
function GetPlayerUserID()
    if playerEntity then
        return playerEntity.userID
    end
    return 0
end

--[[获取角色属性]]
function GetPlayerProp()
    if playerEntity then
        return playerEntity.playerProp
    end
end

function GetPlayerProp_default()
    --假的属性，用于防报错卡流程
    return prop.CProp(props)
end

--[[获取玩家经验]]
function GetPlayerExp()
    if playerEntity then
        return playerEntity.playerProp.exp
    end
    return 0
end

--[[获取玩家等级]]
function GetPlayerLV()
    if playerEntity then
        return playerEntity.playerProp.lv
    end
    return 0
end

--[[获取玩家性别]]
function GetPlayerSex()
    -- todo 服务器联调,先默认3是无性别
    return 3
end

--[[获取玩家点赞数量]]
function GetPlayerKudos()
    -- todo 服务器联调,默认0赞
    return 0
end

--[[获取玩家旧的Vip等级]]
function GetPlayerOldVipLevel()
    if playerEntity then
        return playerEntity.playerProp.oldVipLevel
    end
end

function SetPlayerOldVipLevel(value)
    if playerEntity then
        playerEntity.playerProp.oldVipLevel = value
    end
end

function SetIsShowVipPanel(isShow)
    showVip = isShow
end

--[[是否显示Vip升级面板]]
function GetIsShowVipPanel()
    return showVip
end

--[[获取玩家VIP等级]]
function GetPlayerVipLevel()
    if playerEntity then
        return playerEntity.playerProp.vipLevel
    end
    return 0
end

--[[获取玩家VIP结束时间]]
function GetPlayerVipEndTime()
    if playerEntity then
        return playerEntity.playerProp.vipEndTime or 0
    end
    return 0
end

--[[获取玩家金币]]
function GetPlayerCoin()
    if playerEntity then
        return playerEntity.playerProp.coin
    end
    return 0
end

--[[获取玩家粮食]]
function GetPlayerFood()
    if playerEntity then
        return playerEntity.playerProp.food
    end
    return 0
end

--[[获取玩家铁矿]]
function GetPlayerIron()
    if playerEntity then
        return playerEntity.playerProp.iron
    end
    return 0
end

--[[获取玩家钻石]]
function GetPlayerDiamond()
    if playerEntity then
        return playerEntity.playerProp.diamond
    end
    return 0
end

--[[获取玩家绑定钻石]]
function GetPlayerBindDiamond()
    if playerEntity then
        return playerEntity.playerProp.bindDiamond
    end
    return 0
end

--[[获取玩家钻石]]
function GetPlayerAllDiamond()
    if playerEntity then
        return playerEntity.playerProp.bindDiamond + playerEntity.playerProp.diamond
    end
    return 0
end

--[[获取玩家银河之星]]
function GetPlayerGalaxyStar()
    if playerEntity then
        return playerEntity.playerProp.galaxyStar
    end
    return 0
end

--[[获取玩家宇宙之星]]
function GetPlayerUniverseStar()
    if playerEntity then
        return playerEntity.playerProp.universeStar
    end
    return 0
end

--[[获取玩家国家国旗]]
function GetPlayerNationalFlag()
    if playerEntity then
        return playerEntity.nationalFlag, playerEntity.flagnextsetime
    end
    return 0, 0
end


--[[获取玩家购买英雄容量上限次数]]
function GetPlayerBuyNum()
    if playerEntity then
        return playerEntity.playerProp.buyNum
    end
    return 0
end

--获取当前英雄容量
function GetLimiteHeroNum()
    -- local playerProp = GetPlayerProp()
    -- if playerProp == nil then
    --     return
    -- end
    -- local iHeroPackIncrNum = playerProp.vipLevel <= 0 and 0 or game_scheme:VipPrivilege_0(playerProp.vipLevel).iHeroPackIncrNum

    -- --初始化当前英雄数量
    -- local increNum = 5
    -- local buyNum = GetPlayerBuyNum()
    -- local heroLimit = 0
    -- local cfg = game_scheme:InitBattleProp_0(8)
    -- if cfg then
    --     heroLimit = cfg.szParam
    -- end

    -- local hero_mgr = require("hero_mgr")
    -- local _CurrentHeroNum = GetPalPartCount()
    -- local maxHeroNum = (heroLimit.data[0] + increNum * buyNum + iHeroPackIncrNum)
    -- hero_mgr.SetHeroLimiteNum(_CurrentHeroNum, maxHeroNum)
    --return maxHeroNum
    return 0
end

--[[获取玩家英魂数量]]
function GetPlayerHeroExpNum()
    local entity = skep_mgr.GetGoodsEntity(skep_mgr.const_id.heroExp)
    return entity and entity:GetGoodsNum() or 0
end

--[[获取玩家是否绑定手机]]
function GetPlayerHadBindPhone()
    if playerEntity then
        if playerEntity.maskPhone ~= "" then
            return playerEntity.maskPhone
        else
            return PlayerPrefs.GetString(GetPlayerRoleID() .. "SetPlayerBindPhone") or ""
        end
    end
    return ""
end

function GetSoliderNum()
    local gw_home_soldier_data = require("gw_home_soldier_data")
    local soldiers = gw_home_soldier_data.GetSoldierInfos()
    local soldierNum = 0
    if soldiers then
        for i,v in pairs(soldiers) do
            if v.nInCount > 0 then
                soldierNum = soldierNum + v.nInCount
            end
        end
    end
    return soldierNum
end

local money_func = {
    [1] = GetPlayerCoin, ---1    金币
    [2] = GetPlayerAllDiamond, ---2    钻石
    [3] = GetPlayerHeroExpNum, ---3    英魂
    [4] = GetPlayerExp, ---4     角色经验
    [35] = GetPlayerFood, ---35粮食
    [36] = GetPlayerIron, ---36铁矿

    [41008] = GetPlayerUniverseStar, --宇宙之星
    [321] = GetSoliderNum,     --士兵数量
    
}


--[[获取玩家对应物品数量]]
function GetPlayerOwnNum(good_cfg_id)
    local mFunc = money_func[good_cfg_id]
    if mFunc then
        return mFunc()
    end
    local entity = skep_mgr.GetGoodsEntity(good_cfg_id)
    if entity then
        local expNum = entity:GetGoodsNum()
        return expNum
    end
    return 0
end

--[[获取玩家的头像ID]]
function GetRoleFaceID()
    if playerEntity then
        return playerEntity.playerProp.faceID
    end
end

--[[获取玩家的头像属性ID]]
function GetPlayerFacePropID()
    if playerEntity then
        return playerEntity.playerProp.facePropID
    end
end

--[[获取玩家头像框ID]]
function GetPlayerFrameID()
    if playerEntity then
        return playerEntity.playerProp.frameID
    end
end

--[[获取背包部件，所有物品，包括英雄装备上的]]
function GetPacketPartData()
    local partData = {}
    if playerEntity then
        partData = playerEntity.packetPartData
    end
    return partData
end

--[[通过物品序列号获取背包部件，所有物品，包括英雄装备上的]]
function GetPacketPartDataBySid(sid)
    if playerEntity then
        return playerEntity.packetPartData[sid]
    end
end

--[[获取背包中的物品，不包括英雄装备上的]]
function GetPacketGoods(func)
    local packetGoods = {}
    if playerEntity then
        local item_data = require "item_data"
        local skillType = item_data.Item_Type_Enum.Skill
        for i, goodsEntity in pairs(playerEntity.packetPartData) do
            local skepID = goodsEntity:GetSkepID()
            if skep_mgr.GetSkepType(skepID) == prop_pb.SKEP_TYPE_PACK then
                -- 除开技能（技能默认都是放在背包skep下面）
                if goodsEntity.itemType ~= skillType then
                    if func then
                        func(goodsEntity, itemcfg)
                    end
                    table.insert(packetGoods, goodsEntity)
                end
            end
        end
    end
    return packetGoods
end

--[[根据函数结果遍历持有道具]]
function PacketGoodsJudge(func)
    if not func or not playerEntity then
        return
    end
    local item_data = require "item_data"
    local skillType = item_data.Item_Type_Enum.Skill
    local result = false
    for i, goodsEntity in pairs(playerEntity.packetPartData) do
        local skepID = goodsEntity:GetSkepID()
        if skep_mgr.GetSkepType(skepID) == prop_pb.SKEP_TYPE_PACK then
            if goodsEntity.itemType ~= skillType then
                result = func(goodsEntity)
                if result then
                    break
                end
            end
        end
    end
end


--[[获取挂机背包物品sid  弃用]]
--[[
	function GetIdlePacketGoods()
		local idleGoods = {}
		if playerEntity then
			local skepIDs = skep_mgr.GetSkepIDs(prop_pb.SKEP_TYPE_IDLEPACK)
			-- 挂机背包skepID 只有一个
			if #skepIDs > 0 then
				idleGoods = skep_mgr.GetGoodsSidOfSkep(skepIDs[1])
			end
		end
		return idleGoods
	end
]]


--[[获取时空之巅背包物品sid]]
function GetPeakPacketGoods()
    local mazeGoods = {}
    local length = 0
    if playerEntity then
        local skepIDs = skep_mgr.GetSkepIDs(prop_pb.SKEP_TYPE_PEAK)
        -- 挂机背包skepID 只有一个
        if #skepIDs > 0 then
            mazeGoods, length = skep_mgr.GetGoodsSidOfSkep(skepIDs[1])
        end
    end
    return mazeGoods, length
end

--[[获取异界迷宫背包物品sid]]
function GetMazePacketGoods()
    local mazeGoods = {}
    local length = 0
    if playerEntity then
        local skepIDs = skep_mgr.GetSkepIDs(prop_pb.SKEP_TYPE_MAZE)
        -- 挂机背包skepID 只有一个
        if #skepIDs > 0 then
            mazeGoods, length = skep_mgr.GetGoodsSidOfSkep(skepIDs[1])
        end
    end
    return mazeGoods, length
end

-- [[获取遗落之境背包物品id]]
function GetAshdungeonPacketGoods()
    local mazeGoods = {}
    local length = 0
    if playerEntity then
        local skepIDs = skep_mgr.GetSkepIDs(prop_pb.SKEP_TYPE_ASHDUNGEON)
        -- 挂机背包skepID 只有一个
        if #skepIDs > 0 then
            mazeGoods, length = skep_mgr.GetGoodsSidOfSkep(skepIDs[1])
        end
    end
    return mazeGoods, length
end

--[[获取所有的装备(宝石)]]
function GetAllEquipment()
    local equipmentArr = {}
    local totalCount = 0
    if playerEntity then
        for i, goodsEntity in pairs(playerEntity.packetPartData) do
            if goodsEntity.goodsID then
                if goodsEntity.itemType == prop_pb.GOODS_TYPE_EQUIP then
                    local skepID = goodsEntity:GetSkepID()
                    if skep_mgr.GetSkepType(skepID) == prop_pb.SKEP_TYPE_PACK or skep_mgr.GetSkepType(skepID) == prop_pb.SKEP_TYPE_PALEQUIP then
                        table.insert(equipmentArr, goodsEntity)
                        totalCount = totalCount + goodsEntity.numProp.goodsNum
                    end
                end
            end
        end
    end
    return equipmentArr, totalCount
end

--[[获取神装 返回所有神装、未装备神装]]
function GetAllGodEquipment()
    local allEquipmentArr = {}

    return {}, {}
end

local sigilTableCach = {}
function GetAllSigil(isCludeTrialHero)
    if isCludeTrialHero == nil then
        isCludeTrialHero = false-- isCludeTrialHero 是否包含试用英雄身上的印记 默认不包含
    end

    local player_mgr = require "player_mgr"
    local allSigilArr = {} -- 全印记
    local packSigilArr = {} -- 背包中的印记（数组结构）
    local packSigilOfSidArr = {} -- 背包中的印记（字典结构）
    local hadEquipedSigilOfSidArr = {} -- 背包中的印记（字典结构）
    if playerEntity then
        for _, goodsEntity in pairs(playerEntity.packetPartData) do
            if goodsEntity.goodsID and goodsEntity.itemType == prop_pb.GOODS_TYPE_SIGIL then
                -- 印记类型
                local skepID = goodsEntity:GetSkepID()
                local skepType = skep_mgr.GetSkepType(skepID)
                if (skepType == prop_pb.SKEP_TYPE_PACK or skepType == prop_pb.SKEP_TYPE_PALSIGIL) then
                    local isTrialHero = nil -- 是否是试炼英雄 是则剔除
                    if skepType == prop_pb.SKEP_TYPE_PACK then
                        table.insert(packSigilArr, goodsEntity)
                        packSigilOfSidArr[goodsEntity:GetGoodsSid()] = goodsEntity
                    elseif skepType == prop_pb.SKEP_TYPE_PALSIGIL then
                        -- 已装备上得印记
                        local hero_trial_mgr = require "hero_trial_mgr"
                        local heroSID = player_mgr.GetHeroOfSigilSkep(skepID)
                        isTrialHero = hero_trial_mgr.isTrialHeroBySid(heroSID)
                        -- 绕过客户端判断试用英雄 test
                        local lingshi_data = require "lingshi_data"
                        local value = lingshi_data.GetSkipClientMsgLimit()
                        if value then
                            isTrialHero = nil
                        end
                        if isCludeTrialHero or not isTrialHero then
                            hadEquipedSigilOfSidArr[goodsEntity:GetGoodsSid()] = goodsEntity
                        end
                    end
                    if isCludeTrialHero or not isTrialHero then
                        table.insert(allSigilArr, goodsEntity)
                    end
                end
            end
        end
    end
    return allSigilArr, packSigilArr, packSigilOfSidArr, hadEquipedSigilOfSidArr
end

function SetValidSigil()

end

--[[根据位置获取当前装备的印记]]
function GetEquippedSigilByPos(pos)
    local data = nil
    local skepIDs = skep_mgr.GetSkepIDs(prop_pb.SKEP_TYPE_PALSIGIL)
    if skepIDs then
        for k, skepID in pairs(skepIDs) do
            local sidGroup = skep_mgr.GetGoodsSidOfSkep(skepID)
            if sidGroup then
                for m, sid in pairs(sidGroup) do
                    local entity = GetPacketPartDataBySid(sid)
                    if entity and entity:GetSkepPlace() == pos then
                        data = entity
                    end
                end
            end
        end
    end
    return data
end

-- 获取英雄指定槽位的印记
function GetHeroSigilByPos(heroSID, pos)
    local heroEntity = GetPartDataCacheBySid(heroSID)
    if heroEntity then
        local skepID = heroEntity:GetSigilSkepID()
        local sidGroup = skep_mgr.GetGoodsSidOfSkep(skepID)
        if sidGroup then
            for k, sid in pairs(sidGroup) do
                local entity = GetPacketPartDataBySid(sid)
                if entity and entity:GetSkepPlace() == pos then
                    return entity
                end
            end
        end
    end
    return nil
end

--[[获取所有的装备，装备id和强化等级一样的数量叠加显示，穿在英雄身上的不叠加]]
function GetAllEquip_New()
    local equipmentArr = {}
    if not playerEntity then
        return equipmentArr
    end
    for i, goodsEntity in pairs(playerEntity.packetPartData) do
        if goodsEntity.goodsID then
            local skepID = goodsEntity:GetSkepID()
            local heroSid = GetBranchHeroOfEquipSkep(skepID)
            local heroEntity = nil
            if heroSid then
                heroEntity = GetPartDataBranchCacheBySid(heroSid)-- GetMazePalPartDataBySid(heroSid) or GetPeakPalPartDataBySid(heroSid)
            end
            if not heroEntity and goodsEntity.itemType == prop_pb.GOODS_TYPE_EQUIP and (skep_mgr.GetSkepType(skepID) == prop_pb.SKEP_TYPE_PACK or skep_mgr.GetSkepType(skepID) == prop_pb.SKEP_TYPE_PALEQUIP) then
                if goodsEntity.numProp.goodsNum ~= 0 then
                    local key = string.format("%d_%d_%d", skepID, goodsEntity.goodsID, goodsEntity.numProp.equipStrLv)
                    if not equipmentArr[key] then
                        equipmentArr[key] = {}
                    end
                    table.insert(equipmentArr[key], goodsEntity)
                end
            end
        end
    end
    return equipmentArr
end

--[[获取背包中不包括英雄身上的装备]]
function GetPacketEquipment()
    local equipmentArr = {}

    return equipmentArr
end

--[[背包中不包括英雄身上的装备是否至少有一件]]
function IsPacketEquipmentAtLeastOne()

    return false
end

--[[获取所有已经装备在英雄身上的装备(宝石)]]
function GetHeroEquipment()
    local equipmentArr = {}
    if playerEntity then
        for i, goodsEntity in pairs(playerEntity.packetPartData) do
            if goodsEntity.goodsID and goodsEntity.equipProp and goodsEntity.itemType == prop_pb.GOODS_TYPE_EQUIP then
                local skepID = goodsEntity:GetSkepID()
                if skep_mgr.GetSkepType(skepID) == prop_pb.SKEP_TYPE_PALEQUIP then
                    if goodsEntity.numProp.goodsNum ~= 0 then
                        table.insert(equipmentArr, goodsEntity)
                    end
                end
            end
        end
    end
    return equipmentArr
end

local GetSkepType = skep_mgr.GetSkepType
local SKEP_TYPE_PACK = prop_pb.SKEP_TYPE_PACK
local GOODS_TYPE_EQUIP = prop_pb.GOODS_TYPE_EQUIP
local SKEP_TYPE_PALEQUIP = prop_pb.SKEP_TYPE_PALEQUIP
local insert = table.insert
--[[获取某个部件位置上的所有装备(不包括已经穿上的)]]
function GetHeroPartEquip(partID)
    local equipmentArr = {}
    return equipmentArr
end

--[[某个部件位置上的装备是否至少有一件]]
function IsHeroPartEquipAtLeastOne(partID)
    return false
end

--[[
	获取某个部件位置上的所有装备(包括已经穿上的)  
	partID:部位
	exceptMaze:排除星际迷航雇佣兵装备
    goodsSid:列表排除这一件
]]
function GetHeroPartAllEquip(partID, exceptMaze, goodsSid)
    local equipmentArr = {}
    return equipmentArr
end

--[[获取所有的技能]]
function GetAllSkills()
    local allSkills = {}
    if playerEntity then
        for sid, goodsEntity in pairs(playerEntity.packetPartData) do
            local hSid = {}
            if goodsEntity.goodsID then
                if goodsEntity.itemType == prop_pb.GOODS_TYPE_SKILL then
                    -- 一个技能包括2种skep，一种是背包skep，一种是英雄技能skep
                    --local skepID = goodsEntity:GetSkepID()
                    local heroSids = goodsEntity:GetHeroSkillSkeps()
                    for i, v in pairs(heroSids) do
                        if v and v ~= 0 then
                            table.insert(hSid, v)
                        end
                    end
                    table.insert(allSkills, { goodsSid = sid, heroSid = hSid })
                end
            end

        end
    end
    return allSkills
end

--获取背包中的技能
function GetPackageSkill()
    local pskill = {}
    if playerEntity then
        for k, v in pairs(GetAllSkills()) do
            if #v.heroSid == 0 then
                table.insert(pskill, v)
            end
        end
    end
    return pskill
end

--[[通过英雄技能Id获取英雄技能SID, 不包含天生技能]]
function GetSkillSIDByID(skillId)
    if playerEntity then
        for sid, goodsEntity in pairs(playerEntity.packetPartData) do
            if goodsEntity.goodsID then
                if goodsEntity.itemType == prop_pb.GOODS_TYPE_SKILL and skillId == goodsEntity.goodsID then
                    ----print("通过英雄技能Id获取英雄技能SID",skillId,goodsEntity:GetGoodsID(),goodsEntity:GetGoodsSid())
                    return sid
                end
            end
        end
    end
end

--[[获取英雄部件]]
function GetPalPartData()
    local gw_hero_data = require"gw_hero_data"
    local heroPart = {}
    local ownHeroList = gw_hero_data.GetOwnedHeroIDList()
    for i,v in ipairs(ownHeroList) do
        local heroData = gw_hero_data.GetOldHeroEntityByHeroId(v)
        if heroData then
            heroPart[v] = heroData
        end
    end
    return heroPart
end

--[[获取非试用英雄部件（用于赛博酒吧，星际探索获取可派遣英雄列表,觉醒研究所,传奇锦标赛,轮回空间，星战-次元之战,时空擂台,王者争霸赛,超时空巅峰赛）]]
function GetNonTrialPalPartData()
    local partData = {}
    if playerEntity then
        local hero_trial_mgr = require "hero_trial_mgr"
        for sid, entity in pairs(playerEntity.palPartData) do
            if not hero_trial_mgr.isTrialHeroBySid(sid) then
                partData[sid] = entity
            end
        end
    end
    return partData
end

--[[获取英雄数量]]
function GetPalPartCount()
    local index = 0
    if playerEntity then
        for _, v in pairs(playerEntity.palPartData) do
            index = index + 1
        end
    end
    return index
end

function GetMazePalPartData()
    return playerEntity and playerEntity.mazePalPartData or {}
end

function GetMazePalPartDataBySid(sid)
    return playerEntity and playerEntity.mazePalPartData[sid] or nil
end
function GetPeakPalPartData()
    return playerEntity and playerEntity.peakPalPartData or {}
end
function GetPeakPalPartDataBySid(sid)
    return playerEntity and playerEntity.peakPalPartData[sid] or nil
end

function IsNotOrdinaryPal(sid)
    -- 从迷宫中打开的统一这样处理
    -- local ui_window_mgr = require "ui_window_mgr"
    -- return ui_window_mgr:IsModuleShown("ui_maze") --or ui_window_mgr:IsModuleShown("ui_peak_hero")
    --return not GetPalPartDataBySid(sid) 
end

--[[通过英雄序列号获取英雄部件,包括英雄背包,迷宫英雄背包,时光之巅英雄背包]]
function GetPartDataCacheBySid(sid)
    return GetPalPartDataBySid(sid) or GetMazePalPartDataBySid(sid) or GetPeakPalPartDataBySid(sid)
end

--[[通过英雄序列号获取英雄部件,包括迷宫英雄背包,时光之巅英雄背包]]
function GetPartDataBranchCacheBySid(sid)
    return GetMazePalPartDataBySid(sid) or GetPeakPalPartDataBySid(sid)
end

--[[通过英雄序列号获取英雄部件]]
function GetPalPartDataBySid(sid)
    local gw_hero_data = require "gw_hero_data"
    local heroEntity = gw_hero_data.GetOldHeroEntityBySid(sid)
    if heroEntity then
        return heroEntity
    end
    --
    --if playerEntity then
    --    return playerEntity.palPartData[sid]
    --end
end

--[[通过英雄HeroId获取英雄部件]]
function GetPalPartDataByHeroId(heroId)
    local heroList = {}
    local gw_hero_data = require "gw_hero_data"
    local heroEntity = gw_hero_data.GetOldHeroEntityByHeroId(heroId)
    if heroEntity then
        table.insert(heroList,heroEntity)
    end
    --if playerEntity then
    --    for sid, hEntity in pairs(playerEntity.palPartData) do
    --        if hEntity:GetHeroID() == heroId then
    --            table.insert(heroList, hEntity)
    --        end
    --    end
    --end
    return heroList
end

--[[通过itemid获取item数据]]
function GetPalPartDataByItemId(goodid)
    --local itemList = {}
    --if playerEntity then
    --	for sid, hEntity in pairs(playerEntity.palPartData) do
    --		if hEntity.goodsID == ItemId then
    --			table.insert(heroList,hEntity)
    --		end
    --	end
    --end

    for sid, goodsEntity in pairs(playerEntity.packetPartData) do
        if goodsEntity.goodsID == goodid then
            return sid
        end
    end

end

--[[通过英雄Herolist获取最好的英雄部件]]
function GetBestPalPartData(Herolist)
    if Herolist then
        local insertIdx = nil
        local tempPower = 0
        for i, v in pairs(Herolist) do
            if v:GetHeroBattleProps().power > tempPower then
                tempPower = v:GetHeroBattleProps().power
                insertIdx = i
            end
        end
        if insertIdx then
            return Herolist[insertIdx]
        else
            return nil
        end
    else
        return nil
    end
end

--[[通过英雄Herolist获取非试用最好的英雄部件]]
function GetBestNonTrialPalPartData(Herolist)
    if Herolist then
        local insertIdx = nil
        local tempPower = 0
        local hero_trial_mgr = require "hero_trial_mgr"
        for i, v in pairs(Herolist) do
            if v:GetHeroBattleProps().power > tempPower and not hero_trial_mgr.isTrialHeroBySid(v.heroSid) then
                tempPower = v:GetHeroBattleProps().power
                insertIdx = i
            end
        end
        if insertIdx then
            return Herolist[insertIdx]
        else
            return nil
        end
    else
        return nil
    end
end
ENUM_heroState = {
    nonHero = 1, --没有试用，没有正式
    onlyTrial = 2, --只有试用
    hasHero = 3, --有正式
}
--[[查找是否有非试用的英雄部件，通过英雄Herolist]]
function CheckNonTrialPalPartData(Herolist)
    local state = nil
    -- print("qsy_yxsy:[player_mgr]CheckNonTrialPalPartData111111>>>> ")
    if type(Herolist) == "table" then
        local hero_trial_mgr = require "hero_trial_mgr"
        for i, v in pairs(Herolist) do
            state = ENUM_heroState.onlyTrial--只有试用
            -- print("qsy_yxsy:[player_mgr]CheckNonTrialPalPartData>>>>Herolist = ",v.heroSid)
            if not hero_trial_mgr.isTrialHeroBySid(v.heroSid) then
                -- print("qsy_yxsy:[player_mgr]CheckNonTrialPalPartData>>>> true ")
                state = ENUM_heroState.hasHero
                return state --Herolist[sid]
            end
        end
    else
        state = ENUM_heroState.nonHero--没有试用，没有正式
    end
    return state
end

--[[通过英雄HeroId获取最好的英雄部件]]
function GetBestPalPartDataByHeroId(heroId)
    return GetBestPalPartData(GetPalPartDataByHeroId(heroId))
end

--[[查找是否有非试用的英雄部件，通过英雄HeroId]]
function CheckNonTrialPalPartDataByHeroId(heroId)
    return CheckNonTrialPalPartData(GetPalPartDataByHeroId(heroId))
end

--[[通过英雄HeroId获取 非试用 的最好的英雄部件]]
function GetBestNonTrialPalPartDataByHeroId(heroId)
    return GetBestNonTrialPalPartData(GetPalPartDataByHeroId(heroId))
end

---通过英雄heroList获取starLv最高的英雄部件
---@param heroList table
---@param includeTrial boolean 是否包括试炼英雄，true表示包括，false和nil表示不包括
function GetMaxStarLvPalPartData(heroList, includeTrial)
    if heroList then
        local hero_trial_mgr = require "hero_trial_mgr"
        local insertIdx = nil
        local maxStarLv = 0
        for i, v in pairs(heroList) do
            local starLv = v:GetStarLv() or 0
            if starLv > maxStarLv then
                if includeTrial then
                    maxStarLv = starLv
                    insertIdx = i
                elseif not hero_trial_mgr.isTrialHeroBySid(v.heroSid) then
                    maxStarLv = starLv
                    insertIdx = i
                end
            end
        end
        if insertIdx then
            return heroList[insertIdx]
        else
            return nil
        end
    else
        return nil
    end
end

--[[通过英雄HeroId获取starLv最高的英雄部件]]
function GetMaxStarLvPalPartDataByHeroId(heroId, includeTrial)
    return GetMaxStarLvPalPartData(GetPalPartDataByHeroId(heroId), includeTrial)
end

--[[通过英雄HeroId获取最好的英雄部件,传入table]]
function GetBestPalPartsByHeroIds(heroIds)
    local tab = GetPalPartByHeroIds(heroIds)
    local keyValues = {}
    for k, v in pairs(tab) do
        keyValues[k] = GetBestPalPartData(v)
    end
    return keyValues
end

--[[通过英雄HeroId获取英雄部件,传入table]]
function GetPalPartByHeroIds(heroIds)
    local heroList = {}
    local heroKey = {}
    for k, v in pairs(heroIds) do
        heroKey[v] = k
        heroList[v] = {}
    end
    if playerEntity then
        for sid, hEntity in pairs(playerEntity.palPartData) do
            local key = hEntity:GetHeroID()
            if heroKey[key] then
                table.insert(heroList[key], hEntity)
            end
        end
    end
    return heroList
end

--[[获取挂机部件]]
function GetIdlePartData()
    -- local partData = {}
    -- if playerEntity then
    --     partData = playerEntity.idlePartData
    -- end
    -- return partData
end

--[[获取许愿池部件]]
function GetWishPoolPartData(key)
    -- local partData = {}
    -- if playerEntity then
    --     partData = playerEntity.wishPoolPartData[key]
    -- end
    -- return partData
end

--[[获取酒馆部件]]
function GetTavernPartData()
    -- local tavernData = {}
    -- if playerEntity then
    --     tavernData = playerEntity.tavernPartData
    -- end
    -- return tavernData
end

--[[获取角色头像信息部件]]
function GetRoleFacePartData()
    local partData = {}
    if playerEntity then
        partData = playerEntity.roleFacePartData
    end
    return partData
end

--[[通过物品序号获取]]
function GetGoodsNumberBySid(sid)
    return GetPacketPartDataBySid(sid):GetGoodsNum()
end

function GetGoodsSidById(goodsId)
    local goodsEntities = GetPacketGoods()
    if goodsEntities then
        for i, goods in pairs(goodsEntities) do
            if goods:GetGoodsID() == goodsId then
                return goods:GetGoodsSid()
            end
        end
    end
    return 0
end

function GetGoodsEntity(goodsId)
    local goodsEntities = GetPacketGoods()
    if goodsEntities then
        for i, goods in pairs(goodsEntities) do
            if goods:GetGoodsID() == goodsId then
                return goods
            end
        end
    end
    return nil
end

--根据item类型获取道具数量
function GetGoodsCountByType(itemType)
    local count = 0
    local goodsEntities = GetPacketGoods()
    if goodsEntities then
        for i, goods in pairs(goodsEntities) do
            if goods.itemType == itemType then
                count = count + goods:GetGoodsNum()
            end
        end
    end
    return count
end

--根据item类型获取道具数量
function GetGoodsListByType(itemType)
    local tmp = {}
    local goodsEntities = GetPacketGoods()
    if goodsEntities then
        for i, goods in pairs(goodsEntities) do
            if goods.itemType == itemType then
                table.insert(tmp, goods)
            end
        end
    end
    return tmp
end

--[[获取英雄置换部件]]
function GetHeroTransformPartData()
    local partData = {}
    if playerEntity then
        partData = playerEntity.heroTransformData
    end
    return partData
end

--[[获取一个装备skepID所对应的英雄]]
function GetHeroOfEquipSkep(skepID)
    local heroSid = nil
    if skepID == nil then
        return nil
    end
    if playerEntity then
        for sid, hEntity in pairs(playerEntity.palPartData) do
            if hEntity:GetEquipSkepID() == skepID then
                heroSid = sid
                break
            end
        end

        --[[for sid, hEntity in pairs(playerEntity.mazePalPartData) do
            if hEntity:GetEquipSkepID() == skepID then
                heroSid = sid
                break
            end
        end]]
    end
    return heroSid
end

--[[获取一个印记skepID所对应的英雄]]
function GetHeroOfSigilSkep(skepID)
    local heroSid = nil
    if skepID == nil then
        return nil
    end
    if playerEntity then
        for sid, hEntity in pairs(playerEntity.palPartData) do
            if hEntity:GetSigilSkepID() == skepID then
                heroSid = sid
                break
            end
        end
    end
    return heroSid
end
--[[获取一个装备skepID所对应的英雄,除去背包部分]]
function GetBranchHeroOfEquipSkep(skepID)
    return GetMazeHeroOfEquipSkep(skepID) or GetPeakHeroOfEquipSkep(skepID)
end

--[[获取一个装备skepID所对应的英雄]]
function GetMazeHeroOfEquipSkep(skepID)
    local heroSid = nil
    if skepID == nil then
        return nil
    end
    if playerEntity then
        for sid, hEntity in pairs(playerEntity.mazePalPartData) do
            if hEntity:GetEquipSkepID() == skepID then
                heroSid = sid
                break
            end
        end
    end
    return heroSid
end

--[[获取一个装备skepID所对应的英雄]]
function GetPeakHeroOfEquipSkep(skepID)
    local heroSid = nil
    if skepID == nil then
        return nil
    end
    if playerEntity then
        for sid, hEntity in pairs(playerEntity.peakPalPartData) do
            if hEntity:GetEquipSkepID() == skepID then
                heroSid = sid
                break
            end
        end
    end
    return heroSid
end


--[[设置角色名字]]
function SetRoleName(name)
    if playerEntity then
        if playerEntity.playerProp and playerEntity.playerProp.roleName ~= "" and playerEntity.playerProp.roleName ~= name then
            ReportRenameEvent(name)
        end
        playerEntity.playerProp.roleName = name
        PlayerPrefs.SetString("RoleName", name)
    end
end
--[[设置创角时间]]
function SetRoleCreateTime(time)
    if playerEntity then
        playerEntity.playerProp.createTime = time
        ------print("SetRoleCreateTime>>>>>>>>>>>>>>>>>>")
        event.Trigger(event.REC_ROLE_CREATE_TIME, time)
    end
end
--[[设置开服时间]]
function SetRoleOpenSvrTime(time)
    if playerEntity then
        playerEntity.playerProp.openSvrTime = time
    end
end
--[[设置角色绑定信息]]
function SetPlayerBindPhone(value)
    if playerEntity then
        if playerEntity.maskPhone ~= "" then
            playerEntity.maskPhone = value
            PlayerPrefs.SetString(GetPlayerRoleID() .. "SetPlayerBindPhone", value)
        end
    end
end

--[[设置角色国家国旗]]
function SetPlayerNationalFlag(value, time)
    if playerEntity then
        playerEntity.nationalFlag = value and value or playerEntity.nationalFlag
        playerEntity.flagnextsetime = time and time or playerEntity.flagnextsetime
        event.Trigger(event.UPDATE_NATIONAL_FLAG)
    end
end

--[[设置国家国旗红点]]
function SetPlayerNationalFlagReddot(value)
    nationalFlagReddot = value
end

function GetPlayerNationalFlagReddot()
    return nationalFlagReddot
end

--[[上报角色名称修改数据]]
function ReportRenameEvent(newName)
    --q1sdk.TrackUpdateName(newName)
    q1sdk.UserSetOnceSingle("latest_role_name", newName)
    local actor_face_data = require "actor_face_data"
    local hasModifyName = actor_face_data.HasModifiedName()
    local diamondCost = 0
    if hasModifyName == true then
        local isEnough = true
        local cfg = game_scheme:InitBattleProp_0(35)
        diamondCost = cfg.szParam.data[0]
    end
    local param = "{\"diamond_cost\":" .. diamondCost .. ",\"old_role_name\":" .. playerEntity.playerProp.roleName .. ",\"new_role_name\":" .. newName .. "}"
    event.Trigger(event.GAME_EVENT_REPORT, "rename", param)
end

-- isInitData： 是第一次初始化数据，还是更新数据
--[[更新角色数值属性]]
function UpdateNumProp(key, value, isInitData)
    if playerEntity then
        if key == prop_pb.PERSON_PROP_PDBID then
            -- 角色id
            playerEntity.roleID = value
            if isInitData then
                local setting_server_data = require "setting_server_data"
                setting_server_data.ReportSelectServerReport("user_login")
            end
            PlayerPrefs.SetString("role_id_str", tostring(value))
            local last_role_id = PlayerPrefs.GetFloat("last_role_id", 0)
            if not last_role_id or last_role_id <= 0 then
                PlayerPrefs.SetFloat("last_role_id", value)
            end
            --SetContext内部做版本兼容判断
            q1sdk.SetContext("actorId", playerEntity.roleID)
        elseif key == prop_pb.PERSON_PROP_UDBID then
            -- 账号id
            playerEntity.userID = value
        elseif key == prop_pb.PERSON_PROP_EXP then
            -- 经验
            playerEntity.playerProp.exp = value

        elseif key == prop_pb.PERSON_PROP_LV then
            -- 等级
            local bUpgrade = false
            if playerEntity.playerProp.lv ~= 0 and (value > playerEntity.playerProp.lv) then
                -- 在挂机界面或者大厅才显示升级光效
                -- local laymain_mgr = require "laymain_mgr"
                -- local role_level_up = require "role_level_up"
                -- local ui_window_mgr = require "ui_window_mgr"
                -- ui_window_mgr:UnloadModule("role_level_up")
                -- if laymain_mgr.ScensIsDispose() == false then
                --     --role_level_up.ShowWithDate(playerEntity.playerProp.lv,value)
                -- else
                --     --role_level_up.SetDelayShow(playerEntity.playerProp.lv,value)
                -- end
                bUpgrade = true

                --2022.10.26 曾琳琳 游戏在登录时本身就是1级是不用上报升级事件，只有再成功升2级的时候才会出现升级的事件
                if value > 1 then
                    Adjust.TrackLevelup(playerEntity.playerProp.lv, value)
                    Firebase.TrackLevelup(playerEntity.playerProp.lv, value)
                    Facebook.TrackLevel(playerEntity.playerProp.lv, value)
                end

                -- u8 渠道
                q1sdk.U8SubmitExtraData(4, value)
            end

            --升级打点 冰川打点，数数打点都放到TrackLevelUp里面了
            local setting_server_data = require "setting_server_data"
            local r_worldID = setting_server_data.GetLoginWorldID() or 0
            local r_roleID = playerEntity.roleID or 0
            local r_roleName = playerEntity.playerProp.roleName or ""
            local r_userID = tostring(playerEntity.userID) or ""
            q1sdk.TrackLevelUp(r_worldID, r_roleID, r_roleName, value, r_userID)

            local param = ""
            if value > 50 then
                param = "over50"
            else
                param = value
            end
            q1sdk.AdjustAndFirebaseReport("player_level", { value = tostring(param) })
            --------print("TrackLevelUp>>>>>>>>>>",r_worldID,r_roleID,r_roleName,value,r_userID)

            playerEntity.playerProp.lv = value
            event.Trigger(event.ACTOR_LEVEL_UPGRADE, bUpgrade)
            event.Trigger(event.CHANGE_HOOK_CONDITION)

            -- Todo 修改
            -- local ui_pop_mgr = require "ui_pop_mgr"
            -- local isOpen = ui_pop_mgr.CheckIsOpen(54, false)
            -- local mill_data = require "mill_data"
            
            -- if ui_pop_mgr.CheckIsOpen(155, false) then
            --     local isShow = ((mill_data.GetRefreshTime() and mill_data.GetRefreshTime() <= 0) or (mill_data.GetFinishedOrder() and mill_data.GetFinishedOrder()== true))
            --     event.Trigger(event.UPDATE_SOCIATY_REDTIP, 3, isShow)--联盟磨坊红点
            --     end
            --     local boss_data = require "boss_data"
            --     local noDiamond = boss_data.GetDiamondNum()
            --     local hasRemainTime = (boss_data.GetRemainTime() and boss_data.GetRemainTime() <= 0)
            --     if isOpen and ui_pop_mgr.CheckIsOpen(154, false) then
            --     local isShowBossRedDot = noDiamond or hasRemainTime
            --     event.Trigger(event.UPDATE_SOCIATY_REDTIP, 5, isShowBossRedDot)--联盟Boss红点
            -- end
            -- if isOpen and ui_pop_mgr.CheckIsOpen(859, false) then
            --     local secretplace_mgr = require "secretplace_mgr"
            --     local isShowBossRedDot = secretplace_mgr.IsEntranceRed()--有可领取奖励/可扫荡机会
            --     event.Trigger(event.UPDATE_SOCIATY_REDTIP, 5, isShowBossRedDot)--杀戮场红点
            -- end
            UpdateSociatyTechnologyRed()
        elseif key == prop_pb.PERSON_PROP_COIN then
            -- 金币
            playerEntity.playerProp.coin = value
            event.Trigger(event.UPDATE_PLAYER_RESOURCE)
            local item_data = require "item_data"
            UpdateGoodsItem(item_data.Item_Enum.Gold, value)
            -- SLG 新增货币类型
        elseif key == prop_pb.PERSON_PROP_FOOD then
            -- 粮食
            playerEntity.playerProp.food = value
            event.Trigger(event.UPDATE_PLAYER_RESOURCE)
            local item_data = require "item_data"
            UpdateGoodsItem(item_data.Item_Enum.Food, value)
        elseif key == prop_pb.PERSON_PROP_STEEL then
            -- 铁矿
            playerEntity.playerProp.iron = value
            event.Trigger(event.UPDATE_PLAYER_RESOURCE)
            local item_data = require "item_data"
            UpdateGoodsItem(item_data.Item_Enum.Iron, value)
        elseif key == prop_pb.PERSON_PROP_DIAMOND then
            -- 钻石
            playerEntity.playerProp.diamond = value
            event.Trigger(event.UPDATE_PLAYER_RESOURCE)
            event.Trigger(event.UPDATE_PLAYER_DIAMOND)
            local item_data = require "item_data"
            UpdateGoodsItem(item_data.Item_Enum.Diamond, value)
        elseif key == prop_pb.PERSON_PROP_BIND_DIAMOND then
            -- 绑定钻石
            playerEntity.playerProp.bindDiamond = value
            event.Trigger(event.UPDATE_PLAYER_RESOURCE)
            event.Trigger(event.UPDATE_PLAYER_DIAMOND)
        elseif key == prop_pb.PERSON_PROP_BUY_HERO_LIST_NUM then
            --购买英雄列表次数
            playerEntity.playerProp.buyNum = value
            event.Trigger(event.UPDATE_BUY_HERO_LIMIT_NUM, value)
        elseif key == prop_pb.PERSON_PROP_FACEID then
            --头像
            playerEntity.playerProp.faceID = value
            local actor_face_data = require "actor_face_data"
            local oldID = actor_face_data.GetRoleFaceID()
            actor_face_data.RoleFaceUpdate(value)
            event.Trigger(event.ACTOR_FACE_UPDATE, oldID, value)
        elseif key == prop_pb.PERSON_PROP_FACEPROPID then
            --头像属性
            playerEntity.playerProp.facePropID = value
            --------print("头像属性",value)
            if const.USE_NEW_HERO_PROP_COUNT then
                event.Trigger(event.RECALCULATE_ALL_HERO_POWER, GlobleDirtyConfig.RoleFaceProperty)
            else
                -- local calpro_mgr = require "calpro_mgr"
                -- if playerEntity then
                --     for sid, hEntity in pairs(playerEntity.palPartData) do
                --         calpro_mgr.CalculateHeroProp(hEntity.heroSid, hEntity.numProp.stepLV, hEntity.numProp.starLv, true)--改变头像属性时要改变战斗属性值
                --         --------print("改变头像属性时要改变战斗属性值",hEntity.heroSid, hEntity.numProp.stepLV, hEntity.numProp.starLv)
                --     end
                -- end
            end

            local actor_face_data = require "actor_face_data"
            local oldPropID = actor_face_data.GetRoleFacePropID()
            actor_face_data.RoleFacePropUpdate(value)
            event.Trigger(event.ACTOR_FACE_PROP_UPDATE, oldPropID, value)
        elseif key == prop_pb.PERSON_PROP_VIP_LV then
            --vip等级
            showVip = false
            if (not isInitData) and (value > playerEntity.playerProp.vipLevel) then
                for i=playerEntity.playerProp.vipLevel+1,value do
                    q1sdk.TrackVipLevelUp(i)
                    q1sdk.AdjustAndFirebaseReport("VIP_1", { value = tostring(i) })
                end
            end
            local key1 = GetPlayerRoleID() .. "_VIPLevel"
            local key2 = GetPlayerRoleID() .. "_VIPLevel_" .. value
            local count = PlayerPrefs.GetInt(key2)--当前Vip等级升级面板打开次数
            playerEntity.playerProp.oldVipLevel = PlayerPrefs.GetInt(key1, -1)--本地存储的旧的VIP等级
            playerEntity.playerProp.vipLevel = value
            if value > 0 then
                PlayerPrefs.SetInt(key1, value)
                if playerEntity.playerProp.oldVipLevel == -1 then
                    playerEntity.playerProp.oldVipLevel = 0
                end
            end
            if isFirst then
                if value > 0 then
                    --没有本地数据的老号
                    playerEntity.playerProp.oldVipLevel = PlayerPrefs.GetInt(key1, -1)--本地存储的旧的VIP等级
                else
                    --没有本地数据的新号
                end
                isFirst = false
            end
            if count <= 0 and playerEntity.playerProp.vipLevel > 0 and playerEntity.playerProp.oldVipLevel < value then
                --playerEntity.playerProp.oldVipLevel >= 0 and

                if not isFirst then
                    showVip = true
                    local ui_window_mgr = require "ui_window_mgr"
                    ui_window_mgr:UnloadModule("ui_role_vip_up_new")
                end
                --1、灵石调整VIP等级 2、充值提升VIP等级 3、领取成就奖励提升VIP等级
                --local ui_role_vip_up = require "ui_role_vip_up"
                --ui_role_vip_up.ShowWithDate(playerEntity.playerProp.oldVipLevel,value)				
                --				if playerEntity.playerProp.oldVipLevel == 0 and value > 0 then
                --					playerEntity.playerProp.oldVipLevel = PlayerPrefs.GetInt(key1,-1)--本地存储的旧的VIP等级
                --				end
            else
                if playerEntity.playerProp.oldVipLevel <= 0 and value > 0 then
                    playerEntity.playerProp.oldVipLevel = PlayerPrefs.GetInt(key1, -1)--本地存储的旧的VIP等级
                end
            end
            if not isInitData then
                local ui_role_vip_up_new = require "ui_role_vip_up_new"
                ui_role_vip_up_new.ShowWithDate(playerEntity.playerProp.oldVipLevel, playerEntity.playerProp.vipLevel)
            end
            --新VIP升级界面
            local gw_vip_data = require "gw_vip_data"
            gw_vip_data.SetVipLevel(playerEntity.playerProp.vipLevel)
            --event.Trigger(event.GW_VIP_LEVEL_UP, playerEntity.playerProp.oldVipLevel, playerEntity.playerProp.vipLevel)
            --event.Trigger(event.GW_VIP_LEVEL_UP)
        elseif key == prop_pb.PERSON_PROP_VIP_EXP then
            --VIP经验
            playerEntity.playerProp.vipExp = value
            --event.Trigger(event.UPDATE_VIP_EXP)
            local gw_vip_data = require "gw_vip_data"
            gw_vip_data.SetVipCurExp(playerEntity.playerProp.vipExp)
            --event.Trigger(event.GW_VIP_EXP_CHANGE)
        elseif key == prop_pb.PERSON_PROP_VIP_EXPIRED then
            --vip到期时间
            playerEntity.playerProp.vipEndTime = value
            local gw_vip_data = require "gw_vip_data"
            gw_vip_data.SetVipEndTime(playerEntity.playerProp.vipEndTime)
        elseif key == prop_pb.PERSON_PROP_FREE_VIP_EXP then
            playerEntity.playerProp.freeVipExp = value
            --event.Trigger(event.UPDATE_VIP_EXP)
        elseif key == prop_pb.PERSON_UNIVERSE_STAR then
            if value > playerEntity.playerProp.universeStar then
                local player_mgr = require "player_mgr"
                local roleName = tostring(player_mgr.GetRoleName()) or ""
                local json = require "dkjson"
                local json_str = json.encode({
                    pass_point = value - playerEntity.playerProp.universeStar,
                    point_now = tostring(value),
                    point_type = "1",
                    role_name = roleName
                })
                event.Trigger(event.GAME_EVENT_REPORT, "battlepass_point", json_str)
            end

            playerEntity.playerProp.universeStar = value
            event.Trigger(event.ACTIVITY_REFREASH)
        elseif key == prop_pb.PERSON_GALAXY_STAR then
            if value > playerEntity.playerProp.galaxyStar then
                local player_mgr = require "player_mgr"
                local roleName = tostring(player_mgr.GetRoleName()) or ""
                local json = require "dkjson"
                local json_str = json.encode({
                    pass_point = value - playerEntity.playerProp.galaxyStar,
                    point_now = tostring(value),
                    point_type = "2",
                    role_name = roleName
                })
                event.Trigger(event.GAME_EVENT_REPORT, "battlepass_point", json_str)
            end

            playerEntity.playerProp.galaxyStar = value
            event.Trigger(event.ACTIVITY_REFREASH)
        elseif key == prop_pb.PERSON_PROP_FRAMEID then
            --头像框ID
            playerEntity.playerProp.frameID = value

            local actor_face_data = require "actor_face_data"
            local oldID = actor_face_data.GetRoleFrameID()
            actor_face_data.RoleFrameUpdate(value)
            event.Trigger(event.ACTOR_FRAME_UPDATE, oldID, value)
        elseif key == prop_pb.PERSON_PROP_RECHARGE_RMB then
            --充值的RMB	（没有换算，实际就是人民币）			
            SetNewRechargeNum(value, isInitData)
            -- log.Warning("充值的RMB,value",value)
        elseif key == prop_pb.PERSON_PROP_RECHARGE_USD then
            --充值的USD						
            SetNewRechargeNum(value, isInitData)
            -- log.Warning("充值的USD,value",value)
        elseif key == prop_pb.PERSON_PROP_RECHARGE_INR then
            --充值的印度币（没有换算，实际就是印度币）	
            SetNewRechargeNum(value, isInitData)
            -- log.Warning("充值的印度币,value",value)
        elseif key == prop_pb.PERSON_PROP_RECHARGE_KRW then
            --充值的韩币（没有换算，实际就是韩币）	
            SetNewRechargeNum(value, isInitData)
            -- log.Warning("充值的韩币,value",value)
        elseif key == prop_pb.PERSON_PROP_RECHARGE_CURRENCY then
            --充值的通用货币（原本港台币，服务器直接换算成美元）	
            SetNewRechargeNum(value, isInitData)
            -- log.Warning("充值的通用货币,value",value)
        elseif key == prop_pb.PERSON_PROP_POWER then
            --战斗力
            if playerEntity.playerProp.power ~= 0 then
                local data = {
                    lastPower = playerEntity.playerProp.power,
                    curPower = value
                }
                event.Trigger(event.ACTOR_NEW_POWER_UPDATE, data)
            end
            playerEntity.playerProp.power = value
        elseif key == prop_pb.PERSON_PROP_INCREASE_DISPATCH_QUEUE then
            --隐秘机动队派遣队列数+n
            local ui_tavern_mgr = require "ui_tavern_mgr"
            ui_tavern_mgr.SetIncreaseDispatch(value)
        elseif key == prop_pb.PERSON_PROP_INCREASE_SUPPLY_BOX_OUTPUT then
            --隐秘任务产出神秘补给箱数量+n
            local ui_tavern_mgr = require "ui_tavern_mgr"
            ui_tavern_mgr.SetIncreaseSupplyBox(value)
        elseif key == prop_pb.PERSON_PROP_FROMWORLDID then
            --	PERSON_PROP_FROMWORLDID = 17;   //  来自哪个worldid(创角worldid)
            playerEntity.playerProp.createRoleServerID = value
            PlayerPrefs.SetInt("createRoleServerID",value)
        else
            event.Trigger(event.New_UPDATE_PLAYER_PROP_INFO, key, value)
        end
        --------print("更新角色数值属性",key,value)
        if not isInitData then
            event.Trigger(event.UPDATE_PLAYER_PROP_INFO, key, value)
        end
    end
end

function UpdateGoodsItem(itemId, itemValue)
    util.DelayOneCall("UpdateGoodsProp", function()
        event.Trigger(event.UPDATE_GOOD_PROP, nil, itemId, itemValue)
    end, 0.2)
    util.YieldFrame(function()
        event.Trigger(event.UPDATE_GOODS_NUM_CHANGE, itemId)
    end)
end

local isFirstSetNewRechargeNum = true
--累充金额发生变化，统一设置（单位美分）
function SetNewRechargeNum(value, isInitData)
    --log.Warning("真实充值金额,value", value, isInitData)
    if value > 0 or isFirstSetNewRechargeNum then
        local net_vip_module = require "net_vip_module"
        net_vip_module.SetNewRechargeNum(value, isInitData)
        isFirstSetNewRechargeNum = false
    end
end

--秘宝发生变化，重新计算
function TreasureRareRecalculateHeroForFrame()
    -- if true then
    --     return
    -- end
    -- if playerEntity then
    --     if const.USE_NEW_HERO_PROP_COUNT then
    --         event.Trigger(event.RECALCULATE_ALL_HERO_POWER, GlobleDirtyConfig.treasure)
    --     else
    --         local calpro_mgr = require "calpro_mgr"
    --         for _, hEntity in pairs(playerEntity.palPartData) do
    --             calpro_mgr.CalculateHeroProp(hEntity.heroSid, hEntity.numProp.stepLV, hEntity.numProp.starLv, true)
    --         end
    --     end
    -- end
end

--角色属性发生变化，重新计算
function RecalculateHeroForFrame()
    -- if true then
    --     return
    -- end
    -- if playerEntity then
    --     if const.USE_NEW_HERO_PROP_COUNT then
    --         event.Trigger(event.RECALCULATE_ALL_HERO_POWER, GlobleDirtyConfig.RoleFramProperty)
    --     else
    --         local calpro_mgr = require "calpro_mgr"
    --         for _, hEntity in pairs(playerEntity.palPartData) do
    --             calpro_mgr.CalculateHeroProp(hEntity.heroSid, hEntity.numProp.stepLV, hEntity.numProp.starLv, true)--改变头像属性时要改变战斗属性值
    --         end
    --     end
    -- end
end

function RecalculateHeroForTitle()
    -- if true then
    --     return
    -- end
    -- if playerEntity then
    --     if const.USE_NEW_HERO_PROP_COUNT then
    --         event.Trigger(event.RECALCULATE_ALL_HERO_POWER, GlobleDirtyConfig.RoleTitleProperty)
    --     else
    --         local calpro_mgr = require "calpro_mgr"
    --         for sid, hEntity in pairs(playerEntity.palPartData) do
    --             calpro_mgr.CalculateHeroProp(hEntity.heroSid, hEntity.numProp.stepLV, hEntity.numProp.starLv, true)--改变头像属性时要改变战斗属性值
    --         end
    --     end
    -- end
end


--获取某个技能的最大等级
function GetHeroSkillMaxLevel(heroSkillID, currentLevel)
    local csv = nil
    local count = game_scheme:HeroSkill_nums()
    local maxLv = currentLevel
    for i = 0, count - 1 do
        csv = game_scheme:HeroSkill(i)
        if csv then
            if csv.heroSkillID == heroSkillID then
                if csv.Lv > currentLevel then
                    maxLv = csv.Lv
                end
            end
        end
    end
    return maxLv
end

function UpdateSociatyTechnologyRed()
    -- local ui_pop_mgr = require "ui_pop_mgr"
    -- local isOpen = ui_pop_mgr.CheckIsOpen(54, false)
    -- if isOpen and ui_pop_mgr.CheckIsOpen(223, false) then
    --     local cfg1 = game_scheme:InitBattleProp_0(504)
    --     local cfg2 = game_scheme:InitBattleProp_0(505)
    --     local cfg3 = game_scheme:InitBattleProp_0(507)
    --     local condition_lv1 = cfg1 and cfg1.szParam.data[0] or 108
    --     local condition_lv2 = cfg1 and cfg1.szParam.data[1] or 2110
    --     local condition_unioncoin1 = cfg2 and cfg2.szParam.data[0] or 2000
    --     local condition_unioncoin2 = cfg2 and cfg2.szParam.data[1] or 10000
    --     local condition_coin1 = cfg3 and cfg3.szParam.data[0] or 200000
    --     local condition_coin2 = cfg3 and cfg3.szParam.data[1] or 2000000

    --     local unionCoin = GetPacketPartDataBySid(skep_mgr.GetConstGoodsSidByID(skep_mgr.const_id.unionCoin))
    --     local unionCoinNum = unionCoin == nil and 0 or unionCoin:GetGoodsNum()--联盟币
    --     local coin = GetPlayerCoin()
    --     coin = coin == nil and 0 or coin--金币
    --     local isRed = false
    --     local laymain_data = require("laymain_data")
    --     local passLevel = laymain_data.GetPassLevel()
    --     local tech_data_Mgr = require "tech_data_Mgr"
    --     if passLevel >= condition_lv1 and passLevel < condition_lv2 and unionCoinNum >= condition_unioncoin1 and coin >= condition_coin1 then
    --         isRed = tech_data_Mgr.IsProfessionMaxLv()
    --     elseif passLevel >= condition_lv2 and unionCoinNum >= condition_unioncoin2 and coin >= condition_coin2 then
    --         isRed = tech_data_Mgr.IsProfessionMaxLv()
    --     end
    --     local tech_data_Mgr = require "tech_data_Mgr"
    --     tech_data_Mgr.UpdateSociatyInstituteRed(isRed)
    --     local instRed = tech_data_Mgr.GetSociatyInstituteRed()
    --     event.Trigger(event.UPDATE_SOCIATY_REDTIP, 4, isRed or instRed)
    -- end
end

--[[更新背包部件]]
function UpdatePacketPartData(goodsData)
    if not playerEntity then
        return
    end
    --util.PeekWatch("Login", "UpdatePacketPartData更新背包部件：创建物品实体")
    -- 创建物品实体	
    local sid = nil
    local goods_entity = require "goods_entity"
    for j, value in ipairs(goodsData.prop) do
        if value.propid == prop_pb.GOODS_PROP_SID then
            sid = value.propvalue
            break
        end
    end
    if not sid then
        return
    end
    local entity = playerEntity.packetPartData[sid]
    if entity == nil then
        entity = goods_entity:CreateInstance()
        playerEntity.packetPartData[sid] = entity
        --util.PeekWatch("Login", "UpdatePacketPartData更新背包部件：CreateEntity")
        entity:Create(sid, goodsData.prop)
        --------print("创建物品实体",entity.numProp.skepID)

        -- 特殊物品创建通知
        --util.PeekWatch("Login", "UpdatePacketPartData更新背包部件：特殊物品创建通知start")
        local constSid = skep_mgr.GetConstGoodsSidByID(entity.goodsID)
        if constSid then
            event.Trigger(event.CREATE_SPECIALGOODS_ENTITY, entity.goodsID, constSid)
        end
        --util.PeekWatch("Login", "UpdatePacketPartData更新背包部件：特殊物品创建通知over")
    end
    --通知神器管理类数据更新
  
    --util.PeekWatch("Login", "UpdatePacketPartData更新背包部件：创建物品实体完成")
    --util.PeekWatch("Login", "UpdatePacketPartData更新背包部件：刷新战斗属性start")
    UpdateHeroBattleProp(entity.numProp.skepID, sid)--增加英雄身上的物品时需要改变战斗属性值
    --util.PeekWatch("Login", "UpdatePacketPartData更新背包部件：刷新战斗属性over")
    skep_mgr.SetConstGoods_2(sid, entity.goodsID)
    if entity.goodsID == skep_mgr.const_id.unionCoin then
        --util.PeekWatch("Login", "UpdatePacketPartData更新背包部件：刷新联盟科技红点start")
        UpdateSociatyTechnologyRed()
        --util.PeekWatch("Login", "UpdatePacketPartData更新背包部件：刷新联盟科技红点over")
    elseif entity.goodsID == skep_mgr.const_id.fessionAlloy then
        UpdateSociatyTechnologyRed()
    end
end

--[[更新灵魂链接中的英雄]]
function SetSoulLinkingHeroSourceLevel(data)
    -- if not playerEntity then
    --     return
    -- end
    -- for _, v in pairs(playerEntity.palPartData) do
    --     v.numProp.sourceLV = nil
    -- end
    -- for _, slot in pairs(data) do
    --     if slot.heroSid and slot.heroSid ~= 0 then
    --         if playerEntity.palPartData[slot.heroSid] then
    --             playerEntity.palPartData[slot.heroSid].numProp.sourceLV = slot.heroLV
    --         end
    --     end
    -- end
end

--[[更新英雄祭司]]
function UpdatePalPriestData(data)
    -- if not playerEntity then
    --     return
    -- end
    -- for _, v in pairs(playerEntity.palPartData) do
    --     v.isFlamen = false
    -- end
    -- for _, flamenSid in pairs(data) do
    --     if playerEntity.palPartData[flamenSid] then
    --         playerEntity.palPartData[flamenSid].isFlamen = true
    --     end
    -- end
end

--[[更新英雄部件]]
function UpdatePalPartData(data)
    -- if not playerEntity then
    --     return
    -- end

    -- -- 创建英雄实体
    -- local hero_entity = require "hero_entity"
    -- local sid = nil
    -- for j, value in ipairs(data.prop) do
    --     if value.propid == prop_pb.PAL_PROP_SID then
    --         sid = value.propvalue
    --         break
    --     end
    -- end
    -- if not sid then
    --     return
    -- end
    -- local entity = nil
    -- if const.ONOFF_TEST_HEROTRIAL_DATA then
    --     data.src = 3
    -- end
    -- -- 多个英雄容器
    -- if data.src == prop_pb.PAL_SRC_MAZE then
    --     entity = playerEntity.mazePalPartData[sid]
    --     if entity == nil then
    --         entity = hero_entity:CreateInstance()
    --         playerEntity.mazePalPartData[sid] = entity
    --         entity:Create(sid, data.prop, true)
    --     end
    -- elseif data.src == prop_pb.PAL_SRC_PEAK then
    --     entity = playerEntity.peakPalPartData[sid]
    --     if entity == nil then
    --         entity = hero_entity:CreateInstance()
    --         playerEntity.peakPalPartData[sid] = entity
    --         entity:Create(sid, data.prop, true)
    --     end
    --     -- elseif data.src == prop_pb.PAL_SRC_TRIALATY then
    --     -- 	--英雄试用TODO
    --     -- 	entity = playerEntity.trialPalPartData[sid]
    --     -- 	if entity == nil then
    --     -- 		entity = hero_entity:CreateInstance()
    --     -- 		playerEntity.trialPalPartData[sid] = entity
    --     -- 		entity:Create(sid, data.prop, true)
    --     -- 	end
    -- else
    --     entity = playerEntity.palPartData[sid]
    --     if entity == nil then
    --         entity = hero_entity:CreateInstance()
    --         playerEntity.palPartData[sid] = entity
    --         entity:Create(sid, data.prop)
    --         -- local hero_mgr = require("hero_mgr")
    --         -- if not hero_mgr.HaveOwnedHero(entity.heroID) then
    --         --     hero_mgr.MarkOwnedHero(entity.heroID)
    --         -- end
    --     end
    --     local cfg_card = game_scheme:Hero_0(entity.heroID)
    --     if cfg_card and cfg_card.rarityType == 4 then
    --         --英雄稀有度类型（1B 2A 3S 4S+）
    --         if not HeroIDs[entity.heroID] then
    --             HeroIDs[entity.heroID] = {}
    --         end
    --         if not HeroIDs[entity.heroID].starLv or HeroIDs[entity.heroID].starLv < entity.numProp.starLv then
    --             HeroIDs[entity.heroID].starLv = entity.numProp.starLv
    --             HeroIDs[entity.heroID].lv = entity.numProp.lv
    --         end
    --     end
    -- end

    -- if entity then
    --     if const.USE_NEW_HERO_PROP_COUNT then
    --         local hero_prop_mgr = require "hero_prop_mgr"
    --         hero_prop_mgr.SetHeroDrity(entity.heroSid, hero_prop_mgr.HeroPropModule.base)
    --         hero_prop_mgr.SetHeroDrity(entity.heroSid, hero_prop_mgr.HeroPropModule.customLevel)
    --         hero_prop_mgr.SetHeroDrity(entity.heroSid, hero_prop_mgr.HeroPropModule.equipment)
    --         hero_prop_mgr.SetHeroDrity(entity.heroSid, hero_prop_mgr.HeroPropModule.skill)
    --         hero_prop_mgr.SetHeroDrity(entity.heroSid, hero_prop_mgr.HeroPropModule.godExclusiveEquipment)
    --         hero_prop_mgr.SetHeroDrity(entity.heroSid, hero_prop_mgr.HeroPropModule.enhanceEquipment)
    --         hero_prop_mgr.SetHeroDrity(entity.heroSid, hero_prop_mgr.HeroPropModule.decoration)
    --         hero_prop_mgr.DelayCallRecalculate(entity, true)
    --     else
    --         local calpro_mgr = require "calpro_mgr"
    --         calpro_mgr.CalculateHeroProp(entity.heroSid, entity.numProp.stepLV, entity.numProp.starLv, true)--创建英雄实体时要改变战斗属性值
    --     end
    -- end
end

function GameReport()
    local json = require "dkjson"
    local roleName = tostring(GetRoleName()) or ""
    local heroids = {}
    local herolevels = {}
    local herostars = {}
    for i, v in pairs(HeroIDs) do
        table.insert(heroids, i)
        table.insert(herolevels, v.lv)
        table.insert(herostars, v.starLv)
    end
    local json_str = json.encode({
        hero_id_new = table.concat(heroids, '#'), --S+英雄id（英雄id1#英雄id2#...）
        hero_star_new = table.concat(herostars, '#'), --S+英雄星级
        hero_level_new = table.concat(herolevels, '#'), --S+英雄等级
        role_name = roleName, --角色名
    })
    ----    print("登录S+英雄",json_str)
    event.Trigger(event.GAME_EVENT_REPORT, "NewHeroActivity_Hero_starLv", json_str)
end

--[[更新挂机部件]]
function UpdateIdlePartData(partData)
    -- if playerEntity then
    --     playerEntity.idlePartData = partData
    -- end
end

--[[更新许愿池部件]]
function UpdateWishPartData(key, partData)
    -- if playerEntity then
    --     playerEntity.wishPoolPartData[key] = partData
    -- end
end

--[[更新酒馆部件]]
function UpdateTavernPartData(partData)
    -- if playerEntity then
    --     playerEntity.tavernPartData = partData
    --     if partData.iLevel then
    --         local tavern_mgr = require "tavern_mgr"
    --         tavern_mgr.SetTavernLevel(partData.iLevel)
    --     end
    --     event.Trigger(event.TAVERN_ENTER_TASKlIST)
    -- end
end

--[[更新角色头像信息部件]]
function UpdateRoleFacePartData(partData)
    if playerEntity then
        local tempData = {}
        tempData = partData
        playerEntity.roleFacePartData = tempData
        local actor_face_data = require "actor_face_data"
        actor_face_data.OnActorEnterPartData(tempData)
        event.Trigger(event.UPDATE_ROLE_FACE_PART_DATA)
    end
end

--[[更新英雄置换部件]]
function UpdateHeroTransformPartData(partData)
    if playerEntity then
        playerEntity.heroTransformData = partData
    end
end

--[[删除某个英雄数据]]
function DestroyHeroPart(sid)
    if playerEntity == nil then
        return
    end

    local heroEntity = playerEntity.palPartData[sid]
    if heroEntity then
        skep_mgr.DelSkepID(heroEntity.equipSkepID)
        skep_mgr.DelSkepID(heroEntity.skillSkepID)
        playerEntity.palPartData[sid] = nil
        heroEntity:Release()
    end

    heroEntity = playerEntity.mazePalPartData[sid]
    if heroEntity then
        skep_mgr.DelSkepID(heroEntity.equipSkepID)
        skep_mgr.DelSkepID(heroEntity.skillSkepID)
        playerEntity.mazePalPartData[sid] = nil
        heroEntity:Release()
    end
    heroEntity = playerEntity.peakPalPartData[sid]
    if heroEntity then
        skep_mgr.DelSkepID(heroEntity.equipSkepID)
        skep_mgr.DelSkepID(heroEntity.skillSkepID)
        playerEntity.peakPalPartData[sid] = nil
        heroEntity:Release()
    end
end

--[[删除某个物品数据]]
function DestroyPacketPart(sid)
    if playerEntity == nil then
        return
    end

    local goodsEntity = playerEntity.packetPartData[sid]
    if goodsEntity then
        --2020-3-31 laijiaming:先删除物品再更新属性才能保证英雄属性计算正确
        local skepID = goodsEntity.numProp.skepID
        --------print("删除英雄身上的物品时要改变战斗属性值",goodsEntity.numProp.skepID)
        local needShow = false
        if goodsEntity.goodsID == skep_mgr.const_id.unionCoin then
            needShow = true
        end
        playerEntity.packetPartData[sid] = nil
        goodsEntity:Release()
        UpdateHeroBattleProp(skepID, sid)--删除英雄身上的物品时要改变战斗属性值
        if needShow then
            UpdateSociatyTechnologyRed()
        end

    end


end

--[[获取联盟科技部件]]
function GetTechPartData()
    local partData = {}
    if playerEntity then
        partData = playerEntity.techPartData
    end
    return partData
end

--[[获取联盟科技重置次数]]
function GetTechResetTimes(careerType)
    local result = 2
    if playerEntity then
        result = playerEntity.techResetTimes[careerType]
    end
    return result
end

--[[获取联盟科技2重置次数]]
function GetTechResetTimes2(careerType)
    local result = 2
    if playerEntity then
        result = playerEntity.techResetTimes2[careerType]
    end
    return result
end

--[[更新全部联盟科技部件]]
function UpdateTechPartData(parData)
    -- --[[
    --     ------print("UpdateTechPartData>>>>>>>>>>>>>>>")
    --     ------print("重置次数：",parData.nResetTimes)
    --     for i, professionTechData in ipairs(parData.techInfoArr) do
    --         ------print("职业： ",professionTechData.iProfession)
    --         for i=1,#professionTechData.arrTechData do
    --             ------print("id，level：",professionTechData.arrTechData[i].id,professionTechData.arrTechData[i].level)
    --         end
    --     end	
    -- ]]

    -- if playerEntity then
    --     --科技数据
    --     playerEntity.techPartData = {}
    --     for i, professionTechData in ipairs(parData.techInfoArr) do
    --         playerEntity.techPartData[professionTechData.iProfession] = professionTechData.arrTechData

    --         --重置次数
    --         playerEntity.techResetTimes[professionTechData.iProfession] = professionTechData.resetTimes
    --         playerEntity.techResetTimes2[professionTechData.iProfession] = professionTechData.resetTimes2
    --     end
    --     UpdataHeroPropForTech()
    --     UpdateSociatyTechnologyRed()
    --     local mill_data = require "mill_data"
    --     mill_data.SetContriLvExp(parData.millInfo.millLv, parData.millInfo.millExp)
    -- end
end

--[[联盟科技数据更新-根据某职业]]
function UpdateProfessionTechData(data)
    if playerEntity then
        playerEntity.techPartData[data.iProfession] = data.arrTechData
    end

    UpdataHeroPropForTech(data.iProfession)
end

--[[联盟科技数据更新-根据某科技]]
function UpdateSingleTechData(profession, singleTechData)
    local professionTechData = playerEntity.techPartData[profession]
    for i = 1, #professionTechData do
        if professionTechData[i].id == singleTechData.id then
            professionTechData[i] = singleTechData
            break
        end
    end

    UpdataHeroPropForTech(profession)
end

--[[联盟科技数据更新时要更新英雄属性]]
function UpdataHeroPropForTech(profession)
    -- if true then
    --     return
    -- end
    -- if playerEntity then

    --     if const.USE_NEW_HERO_PROP_COUNT then
    --         event.Trigger(event.RECALCULATE_ALL_HERO_POWER, GlobleDirtyConfig.TachProperty)
    --     else
    --         local calpro_mgr = require "calpro_mgr"
    --         for sid, hEntity in pairs(playerEntity.palPartData) do
    --             if profession ~= nil then
    --                 local cfg_hero = game_scheme:Hero_0(hEntity.heroID)
    --                 local heroType = cfg_hero and cfg_hero.profession
    --                 if profession == heroType then
    --                     --------print("UpdataHeroPropForTech>>>>>>>>>>>>>>>>>>")
    --                     calpro_mgr.CalculateHeroProp(hEntity.heroSid, hEntity.numProp.stepLV, hEntity.numProp.starLv, true)
    --                 end
    --             else
    --                 calpro_mgr.CalculateHeroProp(hEntity.heroSid, hEntity.numProp.stepLV, hEntity.numProp.starLv, true)
    --             end

    --             for sid, hEntity in pairs(playerEntity.mazePalPartData) do
    --                 if profession ~= nil then
    --                     local cfg_hero = game_scheme:Hero_0(hEntity.heroID)
    --                     local heroType = cfg_hero and cfg_hero.profession
    --                     if profession == heroType then
    --                         calpro_mgr.CalculateHeroProp(hEntity.heroSid, hEntity.numProp.stepLV, hEntity.numProp.starLv, true)
    --                     end
    --                 else
    --                     calpro_mgr.CalculateHeroProp(hEntity.heroSid, hEntity.numProp.stepLV, hEntity.numProp.starLv, true)
    --                 end
    --             end
    --             for sid, hEntity in pairs(playerEntity.peakPalPartData) do
    --                 if profession ~= nil then
    --                     local cfg_hero = game_scheme:Hero_0(hEntity.heroID)
    --                     local heroType = cfg_hero and cfg_hero.profession
    --                     if profession == heroType then
    --                         calpro_mgr.CalculateHeroProp(hEntity.heroSid, hEntity.numProp.stepLV, hEntity.numProp.starLv, true)
    --                     end
    --                 else
    --                     calpro_mgr.CalculateHeroProp(hEntity.heroSid, hEntity.numProp.stepLV, hEntity.numProp.starLv, true)
    --                 end
    --             end
    --         end
    --     end
    -- end
end

--[[星际科技数据更新时要更新英雄属性]]
function UpdataHeroPropForStarScienceByType(type)
    -- if true then
    --     return
    -- end
    -- if playerEntity then
    --     if const.USE_NEW_HERO_PROP_COUNT then
    --         event.Trigger(event.RECALCULATE_ALL_HERO_POWER, GlobleDirtyConfig.TachProperty)
    --     else
    --         local calpro_mgr = require "calpro_mgr"
    --         for sid, hEntity in pairs(playerEntity.palPartData) do
    --             if type ~= nil then
    --                 local cfg_hero = game_scheme:Hero_0(hEntity.heroID)
    --                 local heroType = cfg_hero and cfg_hero.type
    --                 if type == heroType then
    --                     --------print("UpdataHeroPropForTech>>>>>>>>>>>>>>>>>>")
    --                     calpro_mgr.CalculateHeroProp(hEntity.heroSid, hEntity.numProp.stepLV, hEntity.numProp.starLv, true)
    --                 end
    --             else
    --                 calpro_mgr.CalculateHeroProp(hEntity.heroSid, hEntity.numProp.stepLV, hEntity.numProp.starLv, true)
    --             end

    --             for sid, hEntity in pairs(playerEntity.mazePalPartData) do
    --                 if type ~= nil then
    --                     local cfg_hero = game_scheme:Hero_0(hEntity.heroID)
    --                     local heroType = cfg_hero and cfg_hero.type
    --                     if type == heroType then
    --                         calpro_mgr.CalculateHeroProp(hEntity.heroSid, hEntity.numProp.stepLV, hEntity.numProp.starLv, true)
    --                     end
    --                 else
    --                     calpro_mgr.CalculateHeroProp(hEntity.heroSid, hEntity.numProp.stepLV, hEntity.numProp.starLv, true)
    --                 end
    --             end
    --             for sid, hEntity in pairs(playerEntity.peakPalPartData) do
    --                 if type ~= nil then
    --                     local cfg_hero = game_scheme:Hero_0(hEntity.heroID)
    --                     local heroType = cfg_hero and cfg_hero.type
    --                     if type == heroType then
    --                         calpro_mgr.CalculateHeroProp(hEntity.heroSid, hEntity.numProp.stepLV, hEntity.numProp.starLv, true)
    --                     end
    --                 else
    --                     calpro_mgr.CalculateHeroProp(hEntity.heroSid, hEntity.numProp.stepLV, hEntity.numProp.starLv, true)
    --                 end
    --             end
    --         end
    --     end
    -- end
end

--[[星际科技数据更新时要更新英雄属性]]
function UpdataHeroPropForStarScienceByHeroID(heroID)
    -- if true then
    --     return
    -- end
    -- if playerEntity then
    --     if const.USE_NEW_HERO_PROP_COUNT then
    --         event.Trigger(event.RECALCULATE_ALL_HERO_POWER, GlobleDirtyConfig.TachProperty)
    --     else
    --         local calpro_mgr = require "calpro_mgr"
    --         for sid, hEntity in pairs(playerEntity.palPartData) do
    --             if heroID ~= nil then
    --                 local cfg_hero = game_scheme:Hero_0(hEntity.heroID)
    --                 local heroType = cfg_hero and cfg_hero.heroID
    --                 if heroID == heroType then
    --                     --------print("UpdataHeroPropForTech>>>>>>>>>>>>>>>>>>")
    --                     calpro_mgr.CalculateHeroProp(hEntity.heroSid, hEntity.numProp.stepLV, hEntity.numProp.starLv, true)
    --                 end
    --             else
    --                 calpro_mgr.CalculateHeroProp(hEntity.heroSid, hEntity.numProp.stepLV, hEntity.numProp.starLv, true)
    --             end

    --             for sid, hEntity in pairs(playerEntity.mazePalPartData) do
    --                 if heroID ~= nil then
    --                     local cfg_hero = game_scheme:Hero_0(hEntity.heroID)
    --                     local heroType = cfg_hero and cfg_hero.heroID
    --                     if heroID == heroType then
    --                         calpro_mgr.CalculateHeroProp(hEntity.heroSid, hEntity.numProp.stepLV, hEntity.numProp.starLv, true)
    --                     end
    --                 else
    --                     calpro_mgr.CalculateHeroProp(hEntity.heroSid, hEntity.numProp.stepLV, hEntity.numProp.starLv, true)
    --                 end
    --             end
    --             for sid, hEntity in pairs(playerEntity.peakPalPartData) do
    --                 if heroID ~= nil then
    --                     local cfg_hero = game_scheme:Hero_0(hEntity.heroID)
    --                     local heroType = cfg_hero and cfg_hero.heroID
    --                     if heroID == heroType then
    --                         calpro_mgr.CalculateHeroProp(hEntity.heroSid, hEntity.numProp.stepLV, hEntity.numProp.starLv, true)
    --                     end
    --                 else
    --                     calpro_mgr.CalculateHeroProp(hEntity.heroSid, hEntity.numProp.stepLV, hEntity.numProp.starLv, true)
    --                 end
    --             end
    --         end
    --     end
    -- end
end

--[[更新联盟科技重置次数]]
function UpdateTechResetTimes(professionTechData)
    if playerEntity then
        playerEntity.techResetTimes[professionTechData.iProfession] = professionTechData.resetTimes
    end
end


--[[更新联盟科技2重置次数]]
function UpdateTechResetTimes2(professionTechData)
    if playerEntity then
        playerEntity.techResetTimes2[professionTechData.iProfession] = professionTechData.resetTimes2
    end
end

--[[重置图鉴点亮数据]]
function UpdatePalHandBookData(data)
    --------print("UpdatePalHandBookData>>>>>>>")
    if playerEntity then
        playerEntity.palHandBookData = {}
        for i, s_data in ipairs(data) do
            ------print(i,s_data.heroID,s_data.heroStarLv)
            playerEntity.palHandBookData[s_data.heroID] = true
            -- if s_data.heroID then
            --     local hero_mgr = require("hero_mgr")
            --     if not hero_mgr.HaveOwnedHero(s_data.heroID) then
            --         hero_mgr.MarkOwnedHero(s_data.heroID)
            --     end
            -- end
        end
    end
    --dump(playerEntity.palHandBookData)
end

--[[更新单条图鉴数据]]
function UpdatePalHandBookDataSingle(data)
    --------print("UpdatePalHandBookDataSingle>>>>>>>>")
    if playerEntity and playerEntity.palHandBookData then
        --------print("UpdatePalHandBookDataSingle2>>>>>>>>",data.heroID,data.heroStarLv)
        playerEntity.palHandBookData[data.heroID] = true
    end
end

--[[获取图鉴数据]]
function GetPalHandBookData()
    local palHandBookData = {}
    if playerEntity then
        palHandBookData = playerEntity.palHandBookData
    end
    return palHandBookData
end

function SetBiographyData(data)
    for i, v in pairs(data) do
        ------print("========角色剧情数据下发=========", v.heroID,v.nLvStage,v.type)
    end
    if playerEntity then
        playerEntity.BiographyData = data
        local ui_hero_biography_data = require "ui_hero_biography_data"
        ui_hero_biography_data.SetData(playerEntity.BiographyData)
    end
end

function GetBiographyData()
    local biographyData = {}
    if playerEntity then
        biographyData = playerEntity.BiographyData
    end
    return biographyData
end
--[[角色战力(等级最高的六个英雄战力之和） 老代码 先注释 2024 10 - 18 han]]
--[[function GetPlayerPower()
    local num = 6
    local heroPowerArr = {}
    local insertIdx = 0
    for l, v in pairs(GetPalPartData()) do
        insertIdx = 0
        for i = 1, #heroPowerArr do
            if v:GetHeroBattleProps().power > heroPowerArr[i] then
                insertIdx = i + 1
            end
        end

        if insertIdx == 0 then
            if #heroPowerArr < num then
                table.insert(heroPowerArr, 1, v:GetHeroBattleProps().power)
            end
        else
            table.insert(heroPowerArr, insertIdx, v:GetHeroBattleProps().power)
            if #heroPowerArr > num then
                table.remove(heroPowerArr, 1)
            end
        end
    end

    local totalPower = 0
    for l, v in pairs(heroPowerArr) do
        totalPower = totalPower + v
    end
    return math.floor(totalPower)
end]]
--[[角色战力(等级最高的六个英雄战力之和）]]
function GetPlayerPower()
    if playerEntity then
        return playerEntity.playerProp.power
    end
    return 0
end

--获取创角worldID
function GetCreateRoleWorldID()
    if playerEntity then
        return playerEntity.playerProp.createRoleServerID
    end
    return 0
end

----[[获取等级最高的六个英雄]]
function GetSixTopHero(type)
    -- local num = 6
    local heroArr = {}
    -- local insertIdx = 0
    -- local allHero = GetPalPartData()
    -- for l,v in pairs (allHero) do 
    -- 	if type == nil or game_scheme:Hero_0(v.heroID).type == type then
    -- 		insertIdx = 0
    -- 		for i=1,#heroArr do 
    -- 			if v:GetHeroBattleProps().power > heroArr[i]:GetHeroBattleProps().power then
    -- 				insertIdx = i+1
    -- 			end
    -- 		end

    -- 		if insertIdx==0 then
    -- 			if #heroArr < num then
    -- 				table.insert( heroArr,1, v )
    -- 			end
    -- 		else
    -- 			table.insert( heroArr,insertIdx, v )
    -- 			if #heroArr>num then
    -- 				table.remove( heroArr, 1 )
    -- 			end
    -- 		end	
    -- 	end
    -- end

    local hero_prop_mgr = require "hero_prop_mgr"
    local _, topsixArr = hero_prop_mgr.GetTopSixHeroPower()
    for i = 1, 6 do
        if topsixArr[i] then
            local _hero = GetPalPartDataBySid(topsixArr[i])
            table.insert(heroArr, _hero)
        end
    end
    return heroArr
end

function OnSceneDestroy()
    playerEntity = nil
    isFirst = true
    showDiamondTips = true
    showDiamondItemTips = true
    showDroneTips = true
    showDismissCongressTips = true
    showVip = nil
    nationalFlagReddot = false
    skep_mgr.ClearSkepData()
    HeroIDs = {}
    util.ClearCoverCall("UpdatePlayerTotalBattlePower")
    nationalFlagReddot = false
end
event.Register(event.SCENE_DESTROY, OnSceneDestroy)
event.Register(event.ACCOUNT_CHANGE_WORLD_RSP, OnSceneDestroy)

-- function ResetMaze()
--     if not playerEntity then
--         return
--     end

--     -- 清除
--     for k, heroEntity in pairs(playerEntity.mazePalPartData) do
--         skep_mgr.DelSkepID(heroEntity.equipSkepID)
--         skep_mgr.DelSkepID(heroEntity.skillSkepID)
--         playerEntity.mazePalPartData[k] = nil
--         heroEntity:Release()
--     end
-- end
-- event.Register(event.RESET_MAZE_MAP, ResetMaze)
function ResetPeak()
    if not playerEntity then
        return
    end

    -- 清除
    for k, heroEntity in pairs(playerEntity.peakPalPartData) do
        skep_mgr.DelSkepID(heroEntity.equipSkepID)
        skep_mgr.DelSkepID(heroEntity.skillSkepID)
        playerEntity.peakPalPartData[k] = nil
        heroEntity:Release()
    end
end
event.Register(event.RESET_PEAK_MAP, ResetPeak)

-- 计算玩家总战斗力 ---------------------------------------------------------------------------------------------
function Internal_UpdatePlayerTotalBattlePower(eventnames)
    -- if true then
    --     return
    -- end
    -- -- local log = require "log"
    -- -- log.Error("UpdatePlayerTotalBattlePower")
    -- local TREASURERARE_ADVANCE_RSP = "" --秘宝强化进阶
    -- local TREASURERARE_REWARD_RSP = "" --秘宝获取

    -- local ILLUSION_TOWER_PROP_UPGRADE = ""
    -- local ACTOR_FRAME_ATTR_UPDATE = ""
    -- local ACTOR_TITLE_ATTR_UPDATE = ""
    -- local BATTLE_SHIP_TECHNOLOGY_UPGRADE = ""
    -- for i, eventname in ipairs(eventnames) do
    --     -- body
    --     if eventname ~= event.UPDATE_HERO_BATTLE_PROP then
    --         print("eventname:", eventname)
    --     end
    --     if eventname == event.TREASURERARE_ADVANCE_RSP then
    --         TREASURERARE_ADVANCE_RSP = eventname
    --     end
    --     if eventname == event.TREASURERARE_REWARD_RSP then
    --         TREASURERARE_REWARD_RSP = eventname
    --     end
    --     if eventname == event.ILLUSION_TOWER_PROP_UPGRADE then
    --         ILLUSION_TOWER_PROP_UPGRADE = eventname
    --     end
    --     if eventname == event.ACTOR_FRAME_ATTR_UPDATE then
    --         ACTOR_FRAME_ATTR_UPDATE = eventname
    --     end
    --     if eventname == event.ACTOR_TITLE_ATTR_UPDATE then
    --         ACTOR_TITLE_ATTR_UPDATE = eventname
    --     end
    --     if eventname == event.BATTLE_SHIP_TECHNOLOGY_UPGRADE then
    --         BATTLE_SHIP_TECHNOLOGY_UPGRADE = eventname
    --     end
    -- end

    -- --可能有全局属性变化，英雄个体没变，需要重新计算
    -- if ILLUSION_TOWER_PROP_UPGRADE == event.ILLUSION_TOWER_PROP_UPGRADE or
    --         ACTOR_FRAME_ATTR_UPDATE == event.ACTOR_FRAME_ATTR_UPDATE or
    --         ACTOR_TITLE_ATTR_UPDATE == event.ACTOR_TITLE_ATTR_UPDATE or
    --         BATTLE_SHIP_TECHNOLOGY_UPGRADE == event.BATTLE_SHIP_TECHNOLOGY_UPGRADE or
    --         TREASURERARE_ADVANCE_RSP == event.TREASURERARE_ADVANCE_RSP or
    --         TREASURERARE_REWARD_RSP == event.TREASURERARE_REWARD_RSP then

    --     local topSixHer = GetSixTopHero()
    --     if TREASURERARE_ADVANCE_RSP == event.TREASURERARE_ADVANCE_RSP or TREASURERARE_REWARD_RSP == event.TREASURERARE_REWARD_RSP then
    --         topSixHer = playerEntity.palPartData
    --     end
    --     print("战斗力计算 英雄列表长度：", #topSixHer)
    --     if const.USE_NEW_HERO_PROP_COUNT then
    --         event.Trigger(event.RECALCULATE_TOPSIX_HERO_POWER)
    --     else
    --         local calpro_mgr = require "calpro_mgr"
    --         for key, _hero in pairs(topSixHer) do
    --             calpro_mgr.CalculateHeroProp(_hero.heroSid, _hero.numProp.stepLV, _hero.numProp.starLv, true)
    --         end
    --     end
    -- end

    -- local hero_prop_mgr = require "hero_prop_mgr"
    -- local power, _ = hero_prop_mgr.GetTopSixHeroPower()
    -- -- local power = calpro_mgr.CalculateHeroListPowerWithHalo(topSixHer, nil)
    -- --战斗力排名前6总战斗力
    -- SetPlayerBattlePower(power)

    -- --所有英雄总战斗力
    -- -- local allHero = GetPalPartData()
    -- -- power = calpro_mgr.CalculateHeroListPowerWithHalo(allHero, nil)
    -- local allpower, _ = hero_prop_mgr.GetAllHeroPower()
    -- SetPlayerAllHeroBattlePower(allpower)
end

local eventList = {}
function UpdatePlayerTotalBattlePower(eventname)
    table.insert(eventList, eventname)
    --100ms时升级英雄仍有两次计算，为尽可能减少非必要计算，暂设为最快 200ms 计算一次
    util.CoverOneCall("UpdatePlayerTotalBattlePower", 0.2, function()
        Internal_UpdatePlayerTotalBattlePower(eventList)
        eventList = {}
    end)
end

---获取Facebook社群引导领奖状态,true:可以领奖   false:不可以领奖
function GetFacebookGuideRewardState()
    local open_test_mgr = require "open_test_mgr"
    if open_test_mgr.IsOpenFBGuideReward() then
        if not playerEntity then
            return true
        end
        return playerEntity.facebookGuideReward == 0
    else
        return false
    end

end

-- =================  手动战力计算模式相关 =================================

--计算目标战力
---@param needCalSid 需要计算的英雄SID若nil则全部计算
function CalculateTargetPower(needCalSid)
    -- if true then
    --     return
    -- end
    -- local calpro_mgr = require "calpro_mgr"
    -- if needCalSid then
    --     for index, value in ipairs(needCalSid) do
    --         local hEntity = playerEntity.palPartData[value]
    --         if hEntity then
    --             calpro_mgr.CalculateHeroProp(hEntity.heroSid, hEntity.numProp.stepLV, hEntity.numProp.starLv, true)
    --         end
    --     end
    -- else
    --     for sid, hEntity in pairs(playerEntity.palPartData) do
    --         calpro_mgr.CalculateHeroProp(hEntity.heroSid, hEntity.numProp.stepLV, hEntity.numProp.starLv, true)
    --     end
    -- end
end

-- =================  玩家初始化模块时 事件派发 =================================
function AddInitEvent(eventId, listener)
    if not initEventLists[eventId] then
        initEventLists[eventId] = {}
    end
    table.insert(initEventLists[eventId], listener)
end
function DispatchInitEvent(eventId, ...)
    if initEventLists[eventId] then
        for _, listener in ipairs(initEventLists[eventId]) do
            listener(...)
        end
    end
end
function DisposeInitEvent(eventId, listener)
    if initEventLists[eventId] then
        local events = initEventLists[eventId]
        local removeIndex = 0
        for i, v in ipairs(events) do
            if v == listener then
                removeIndex = i
            end
        end
        table.remove(events, removeIndex)
    end
end

--重登之类的
function Clear(e)
    ManualUpdateHeroPower = false
end
--钻石提示框相关
function SetDiamondTips(value)
    showDiamondTips = value
end

function SetDiamondItemTips(value)
    showDiamondItemTips = value
end

function SetDismissCongressTips(value)
    showDismissCongressTips = value
end

function SetDroneTips(value)
    showDroneTips = value
end

function GetDiamondTips()
    return showDiamondTips
end

function GetDiamondItemTips()
    return showDiamondItemTips
end

function GetDismissCongressTips()
    return showDismissCongressTips
end

function GetDroneTips()
    return showDroneTips
end

function GetActivityBubble()
    return showActivityBubble
end

function SetActivityBubble(value)
    showActivityBubble = value
end

function SetFireCnt(value)
    if playerEntity and playerEntity.playerProp then
        playerEntity.playerProp.fireCnt = value
    end
end

function GetFireCnt()
    if playerEntity and playerEntity.playerProp then
        return playerEntity.playerProp.fireCnt
    end
    return 0
end

-- event.Register(event.HERO_UPDATE, UpdatePlayerTotalBattlePower)
event.Register(event.UPDATE_HERO_BATTLE_PROP, UpdatePlayerTotalBattlePower)
event.Register(event.ILLUSION_TOWER_PROP_UPGRADE, UpdatePlayerTotalBattlePower)
event.Register(event.ACTOR_FRAME_ATTR_UPDATE, UpdatePlayerTotalBattlePower)
event.Register(event.ACTOR_TITLE_ATTR_UPDATE, UpdatePlayerTotalBattlePower)
event.Register(event.BATTLE_SHIP_TECHNOLOGY_UPGRADE, UpdatePlayerTotalBattlePower)
event.Register(event.TREASURERARE_REWARD_RSP, UpdatePlayerTotalBattlePower)
event.Register(event.TREASURERARE_ADVANCE_RSP, UpdatePlayerTotalBattlePower)

---------------------------------------------------------------------------------------------------------------
---@see 用户清楚数据事件 (登出、切换区服、切换账号),(暂时先这样处理  到后面去优化这个事件逻辑  进行防止重复调用)
---@param resetType number 清除数据类型
local function UserDataReset(resetType)
    event.Trigger(event.USER_DATA_RESET, resetType)
end
--账户切换区服时
function AccountChangeWorld()
    UserDataReset(userDataResetType.SwitchServer)
end
--场景销毁时
function SceneDestroy()
    UserDataReset(userDataResetType.SceneDestroy)
end
--- 账户退出回调
function OnAccountLogout()
    --之前的逻辑
    DestroyPlayerEntity()
    UserDataReset(userDataResetType.Logout)
end
--返回登录界面 或者 user session 失效 销毁数据
event.Register(event.ACCOUNT_LOGOUT, OnAccountLogout)
event.Register(event.SCENE_DESTROY, SceneDestroy)
event.Register(event.ACCOUNT_CHANGE_WORLD_RSP, AccountChangeWorld)