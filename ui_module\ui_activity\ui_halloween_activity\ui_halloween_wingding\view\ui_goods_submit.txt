local print = print
local require = require
local pairs = pairs
local ipairs = ipairs
local string = string
local table = table
local tostring = tostring
local tonumber = tonumber
local type = type
local math = math
local UIUtil = CS.Common_Util.UIUtil

local reward_mgr = require "reward_mgr"
local goods_item_new = require "goods_item_new"
local enum_define = require "enum_define"
local ui_base = require "ui_base"
local lang = require "lang"
local util = require "util"
local class = require "class"
local binding = require "ui_goods_submit_binding"

--region View Life
module("ui_goods_submit")
local ui_path = binding.UIPath
local window = nil
local UIView = {}

UIView.widget_table = binding.WidgetTable

function UIView:SetVCTypeUI()
    return self.__base.SetVCTypeUI(self, enum_define.enum_ui_vc_type.vc)
end

function UIView:Init()
    self:SetVCTypeUI()
    self.__base.Init(self)

    self.VData = {}

end

function UIView:OnShow()
    self.__base.OnShow(self)
end

function UIView:OnHide()
    self.__base.OnHide(self)
end

function UIView:Close(data)
    if self.VData then
        for i, v in pairs(self.VData) do
            if type(v) == "table" and v.Dispose then
                v:Dispose()
            end
        end
    end
    self.VData = nil
    if self.submitGoodsItem then
        self.submitGoodsItem:Dispose()
        self.submitGoodsItem = nil
    end

    if self.goodItemArr then
        for i, v in ipairs(self.goodItemArr) do
            v:Dispose()
        end
        self.goodItemArr = nil
    end

    self.__base.Close(self)
    window = nil
end
--endregion

--region View Logic

function UIView:ShowGoodsSubmitPanel(data)
    self.txt_title.text = data.titleStr
    self.txt_tips.text = data.tipStr
    self.txt_okText.text = data.btnTextStr
    self.txt_curNum.text = string.format(lang.Get(1009306), data.haveItemCount)

    self.ss_ok:Switch(data.haveItemCount < data.submitMinCount and 1 or 0)

    --提交物品展示
    self.submitGoodsItem = self.submitGoodsItem or goods_item_new.CGoodsItem():Init(self.rtf_submitGoodsParent.transform, nil, 0.77)
    self.submitGoodsItem:SetGoods(nil, data.itemID, nil, true)
    self.submitGoodsItem:SetCountEnable(true, data.maxCount * data.submitMinCount)

    --region 上来就显示可以提交的最大次数
    self.sld_numSlider.minValue = 1
    self.sld_numSlider.maxValue = data.maxCount
    self.sld_numSlider.value = data.maxCount
    self.inp_numInput.text = data.maxCount
    --特殊处理
    if data.maxCount == 1 then
        self.sld_numSlider.minValue = 0
        self.sld_numSlider.value = data.maxCount
    end
    --endregion

    --region 奖励预览
    if not self.goodItemArr then
        self.goodItemArr = {}
    end
    self.rewardData = reward_mgr.GetRewardGoodsList(data.preReward)
    local maxIndex = 0

    for i, v in ipairs(self.rewardData) do
        local goodItem = self.goodItemArr[i] or goods_item_new.CGoodsItem():Init(self.rtf_rewardParent.transform, nil, 0.82)
        goodItem:SetGoods(nil, v.id, v.num * data.maxCount, true)
        goodItem:SetCountEnable(true)
        self.goodItemArr[i] = goodItem
        maxIndex = i
    end

    for i, v in ipairs(self.goodItemArr) do
        if i > maxIndex then
            v:Dispose()
            self.goodItemArr[i] = nil
        end
    end
    --endregion
end

---@public function 刷新提交次数
function UIView:RefreshSubmitNum(count, totalNum)
    self.submitGoodsItem:SetCountEnable(true, totalNum)
    self.inp_numInput.text = count
    
    if self.rewardData then
        for i, v in ipairs(self.rewardData) do
            self.goodItemArr[i] = self.goodItemArr[i] or goods_item_new.CGoodsItem():Init(self.rtf_rewardParent.transform, nil, 0.82)
            self.goodItemArr[i]:SetGoods(nil, v.id, v.num * count, true)
            self.goodItemArr[i]:SetCountEnable(true)
        end
    end
end

function UIView:AddSliderValue(value)
    self.sld_numSlider.value = self.sld_numSlider.value + value
end

---@public function 设置加按钮置灰
function UIView:SetAddButtonGray(isGray)
    self.gray_add:SetEnable(isGray)
end

---@public function 设置减按钮置灰
function UIView:SetMinusButtonGray(isGray)
    self.gray_minus:SetEnable(isGray)
end

--endregion

--region View Static 
local CUIView = class(ui_base, nil, UIView)

function Show(data)
    if window == nil then
        window = CUIView()
        window._NAME = _NAME
        window.isBlurBg = true

        if data and type(data) == "table" then
            local tempPath = data.uiPath or ui_path
            local tempParent = data.uiParent or nil
            window:LoadUIResource(tempPath, nil, tempParent, nil, true, false)
        else
            window:LoadUIResource(ui_path, nil, nil, nil, true, false)
        end
    end
    window:Show()
    return window
end

function Close(data)
    if window ~= nil then
        window:Close(data)
        window = nil
    end
end

function Hide()
    if window ~= nil then
        window:Hide()
    end
end
--endregion
