-- arena_common_const.txt ------------------------------------------
-- author:  hym
-- date:    2025/08/28
-- ver:     1.0
-- desc:    竞技场通用常量
--------------------------------------------------------------
local require 	= require
local ui_window_mgr = require "ui_window_mgr"
local festival_activity_cfg = require "festival_activity_cfg"
local role_pb = require "role_pb"
local click_liking_define = require "click_liking_define"
local data_personalInfo = require "data_personalInfo"
local common_pb               = require "common_new_pb"
module("arena_common_const")

SingleRankCount = 20        --单页竞技场排行榜数量

ArenaType = {
--[[    Weekend = 1,        --巅峰竞技场
    Legend = 2,         --3v3竞技场
    New = 3,            --新手竞技场]]
    Storm = 4,          --风暴竞技场
}

loginReqArenaType = {
    ArenaType.Storm,
}
--战力排行榜类型
PowerRankType = 306

--点赞服务器类型
LikeServerType = {
    [ArenaType.Storm] = role_pb.enLikeSysType_StormArena,
}

--点赞配置类型
LikeConfigType = {
    [ArenaType.Storm] = click_liking_define.Enum_LikeType.StormArena,
} 

--点赞客户端类型
LikeClientType = {
    [ArenaType.Storm] = data_personalInfo.PropEnum.StormArena,
}

--获取每日奖励配置
GetDailyRewardConfig = {
    [ArenaType.Storm] = function(arenaCfg, activityIdList)
        return activityIdList.data[arenaCfg.ArenaSubType]
    end,
}

--获取竞技场晋升奖励
GetPromoteRewardConfig = {
    [ArenaType.Storm] = function(data)
        local game_scheme = require "game_scheme"
        local initCfg = game_scheme:InitBattleProp_0(8515)
        if not initCfg or not initCfg.szParam then
            return
        end
        return initCfg.szParam.data[0]
    end,
}

--上次进入竞技场排名
LAST_ARENA_RANK = "LAST_ARENA_RANK"

--是否选择跳过战斗
IS_SKIP_BATTLE = "IS_SKIP_BATTLE"

--最近一期竞技场开启时间
LAST_ARENA_OPEN_TIME = "LAST_ARENA_OPEN_TIME"

--竞技场攻击阵容保存标记
ArenaAttackLineup = {
    [ArenaType.Storm] = "StormArenaAttackLineup",
}

--竞技场对饮headingCode枚举
ArenaHeadingCode = {
    [ArenaType.Storm] = festival_activity_cfg.ActivityCodeType.StormArena,
}

--竞技场战斗类型
ArenaBattleType = {
    [ArenaType.Storm] = common_pb.StormArena,
}

BattleSetPrepareBattle = {
    [ArenaType.Storm] = function()
        --[[local festival_activity_mgr = require "festival_activity_mgr"
        festival_activity_mgr.CloseActivityCenterUI()
        --ui_window_mgr:UnloadModule("ui_arena_main_common")
        ui_window_mgr:UnloadModule("ui_arena_challenge")]]
    end
}

--关闭竞技场界面
CloseArenaUI = {
    [ArenaType.Storm] = function()
        local festival_activity_mgr = require "festival_activity_mgr"
        festival_activity_mgr.CloseActivityCenterUI()
        ui_window_mgr:UnloadModule("ui_arena_challenge")
    end,
} 