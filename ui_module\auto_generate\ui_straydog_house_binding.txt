local require = require
local typeof = typeof

local Button = CS.UnityEngine.UI.Button
local Transform = CS.UnityEngine.Transform
local Text = CS.UnityEngine.UI.Text
local SpriteSwitcher = CS.War.UI.SpriteSwitcher
local RectTransform = CS.UnityEngine.RectTransform
local RawImage = CS.UnityEngine.UI.RawImage
local SortingGroup = CS.UnityEngine.Rendering.SortingGroup

module("ui_straydog_house_binding")

UIPath = "ui/prefabs/gw/gw_straydog/uistraydoghouse.prefab"

WidgetTable ={
	btn_closeBtn = { path = "closeBtn", type = Button, event_name = "OnBtnCloseBtnClickedProxy", backEvent = true},
	tf_HouseIcon_1 = { path = "tf_HouseIcon_1", type = Transform, },
	tf_HouseIcon_2 = { path = "tf_HouseIcon_2", type = Transform, },
	txt_Title = { path = "txt_Title", type = Text, },
	btn_Tip = { path = "txt_Title/btn_Tip", type = Button, event_name = "OnBtnTipClickedProxy"},
	txt_Level = { path = "bg_level/txt_Level", type = Text, },
	tf_Award = { path = "tf_Award", type = Transform, },
	
	rtf_RoleParent = { path = "tf_Award/rtf_RoleParent", type = RectTransform, },
	rImg_RoleImg = { path = "tf_Award/rtf_RoleParent/rImg_RoleImg", type = RawImage, },
	ss_DogIcon = { path = "tf_Award/ss_DogIcon", type = SpriteSwitcher, },
	btn_GetAward = { path = "tf_Award/btn_GetAward", type = Button, event_name = "OnBtnGetAwardClickedProxy"},
	txt_RewardNum = { path = "tf_Award/btn_GetAward/Image/txt_RewardNum", type = Text, },
	tf_RewardItem = { path = "tf_Award/bg/tf_RewardItem", type = Transform, },
	rtf_RechargeParent = { path = "tf_Award/bg/rtf_RechargeParent", type = RectTransform, },
	rtf_RewardParent = { path = "tf_Award/bg/rtf_RechargeParent/Viewport/rtf_RewardParent", type = RectTransform, },
	tf_NoAward = { path = "tf_NoAward", type = Transform, },
	txt_GetAward = { path = "tf_NoAward/AwardGray/txt_GetAward", type = Text, },
	txt_Outside = { path = "tf_NoAward/txt_Outside", type = Text, },
	sg_Upgrade = { path = "sg&btn_Upgrade", type = SortingGroup, },
	btn_Upgrade = { path = "sg&btn_Upgrade", type = Button, event_name = "OnBtnUpgradeClickedProxy"},
	tf_UpgradeRed = { path = "sg&btn_Upgrade/tf_UpgradeRed", type = Transform, },
	rImg_Effect = { path = "sg&btn_Upgrade/effect", type = RawImage, },
	tf_UpgradeEffectParent = { path = "tf_UpgradeEffectParent", type = Transform, },

	sg_txt_Upgrade = { path = "sg&btn_Upgrade/txt_Upgrade", type = SortingGroup, },
}
