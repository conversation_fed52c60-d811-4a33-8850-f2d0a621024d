---
--- Created by fgy.
--- Changed by <PERSON><PERSON>
--- DateTime: 2024/8/30 20:15
--- Desc: 名称展示

local require = require
local newclass = newclass
local string = string
local UIUtil = UIUtil
local SimpleTextOutline = CS.TextMeshUtil.SimpleTMOutline
local MeshRenderer = CS.UnityEngine.MeshRenderer
local SpriteFitSize = CS.TextMeshUtil.SpriteFitSize
local SpriteRenderer = CS.UnityEngine.SpriteRenderer

local gw_sand_external_mgr = require "gw_sand_external_mgr"
local gw_common_util = require "gw_common_util"
local gw_march_common_util = require "gw_march_common_util"
local event = require "event"
local lang = require "lang"
local sand_hud_base = require "sand_hud_base"
local gw_const = require "gw_const"
local helper_personalInfo = require("helper_personalInfo")
local EntityType = gw_const.ESEntityType
local MarchType = gw_const.ESMarchType

module("gw_comp_name_hud")
-- - - - - - - - - - - - - - - - - - - - - 分割线 - - - - - - - - - - - - - - - - - - - -

---@class GWCompNameHud : sand_hud_base
local GWCompNameHud = newclass("gw_comp_name_hud", sand_hud_base)

-- - - - - - - - - - - - - - - - - - - - - 分割线 - - - - - - - - - - - - - - - - - - - -
GWCompNameHud.widget_table = {
    txtName = { path = "imgBg/txtName", type = "TextMesh" },
    txtName_outline = { path = "imgBg/txtName", type = SimpleTextOutline },
    imgBg_size = { path = "imgBg", type = SpriteFitSize },
    imgBg = { path = "imgBg", type = SpriteRenderer },

    PresidentialIcon = { path = "imgBg/PresidentialIcon", type = "GameObject" },
    TextMeshRenderer = { path = "imgBg/txtName", type = MeshRenderer },
    imgNational = { path = "imgBg/nationalFlag", type = SpriteRenderer },
}

function GWCompNameHud:InitHudComp(hudData, data)
    sand_hud_base.InitHudData(self, hudData)
    self:InstantiateModelAsync(data.resPath, data.parent);
end

---实例化成功 （现必须基成设置名字和组件Id）
---@see override
function GWCompNameHud:InstantiateSuccess(_obj)
    sand_hud_base.InstantiateSuccess(self, _obj)
    self:UpdateName()
    self:OnUpdateVisible()
    self:OnUpdateIcon()
    self:UpdateNamePlate()
end

---@public 注册监听相关
---@see override
function GWCompNameHud:RegisterListener()
    sand_hud_base.RegisterListener(self)
    self.onLanguageChange = function()
        self:UpdateName()
    end
    self:RegisterEvent(event.LANGUAGE_SETTING_CHANGED, self.onLanguageChange)
end

function GWCompNameHud:OnUpdateData()
    sand_hud_base.OnUpdateData(self)
    self:UpdateName()
    self:OnUpdateVisible()
    self:OnUpdateIcon()
    self:UpdateNamePlate()
end

function GWCompNameHud:OnUpdateIcon()
    local GWConst = require "gw_const"
    local util = require "util"
    if not util.IsObjNull(self.PresidentialIcon) then
        local roleId = self.serData:GetPositionId()
        UIUtil.SetActive(self.PresidentialIcon, roleId == GWConst.enCongress_PositionType.enCongress_PositionType_Pres)
    end
end

---更新铭牌
function GWCompNameHud:UpdateNamePlate()
    if not self:IsValid() then
        return
    end

    local id = self.serData:GetNamePlateId()
    local cfg = helper_personalInfo.GetNamePlateConfig(id)
    ---@type GWAssetMgr
    local gw_asset_mgr = require "gw_asset_mgr"
    gw_asset_mgr:LoadGoodsIcon(cfg.rivalType, function(sprite)
        if not self:IsValid() then
            return
        end
        self.imgBg.sprite = sprite
    end)
end

function GWCompNameHud:UpdateNationalFlag()
    if not self:IsValid() then
        return
    end
    if self.serData.type ~= 1 then
        self.imgNational.gameObject:SetActive(false)
        return
    end
    local nationalFlagMgr = require "national_flag_mgr"
    if nationalFlagMgr:CheckNationalFlagIsOpen() then
        -- 设置国旗
        local countryID = self.serData:GetNationalFlagID()
        if countryID == 0 or countryID == nil or string.IsNullOrEmpty(countryID) then
            countryID = 217
        end
        require("card_sprite_asset").CreateNationalFlagAsset():GetSprite(countryID, function(sprite)
            self.imgNational.sprite = sprite
            self.imgNational.gameObject:SetActive(true)
        end)
    else
        self.imgNational.gameObject:SetActive(false)
    end
end

---实例化成功 （现必须基成设置名字和组件Id）
function GWCompNameHud:UpdateName(lv)
    if not self:IsValid() then
        return
    end
    
    --通用实体处理
    if self.serData.type == EntityType.CommonEntity then
        local gw_sand_common_entity_gather = require "gw_sand_common_entity_gather"
        self:SetTextContent(gw_sand_common_entity_gather.GetEntityName(self.serData))
        return 
    end
    
    if self.serData:IsSelfBigGun() then
        self:SetTextContent(gw_sand_external_mgr.GetBigGunName(self.serData))
        return
    end
    
    if not self.serData.MarchEntityInScene and (self.serData.type == EntityType.RadarTreasure or self.serData.type == EntityType.SandBoxTreasure) then
        --如果是雷达宝箱或者沙盒宝箱，显示联盟名字
        if self.serData.cfg and self.serData.cfg.attributionType == 4 then
            -- 攻占城市
            self:SetTextContent(string.format2(lang.Get(667166), self.serData:GetUnionShortAndRoleName()))
        else
            if self.serData.cfg.type == 3 then
                self:SetTextContent(string.format2(lang.Get(1006584), self.serData:GetUnionShortName()))
            else
                self:SetTextContent(string.format2(lang.Get(652090), self.serData:GetUnionShortAndRoleName()))
            end
            
        end
    else
        self:SetTextContent(self.serData:GetUnionShortAndRoleNameAndServerID())
        self:UpdateNationalFlag(self.serData)
    end
end

function GWCompNameHud:OnUpdateVisible()
    if self.serData.MarchEntityInScene and (self.serData.type == MarchType.CarriageMarch
            or self.serData.type == MarchType.AllianceTrain) then
        self:OnShow(gw_march_common_util.GetMarchMoveState(self.serData))
    end
end

function GWCompNameHud:SetTextContent(content)
    --如果是战区对决，且是巨炮
    if self.serData:IsSelfBigGun() then
        --无人占领白色cityName，同服占领蓝色 ，不同服占领粉色 [#区服ID]cityName
        self.txtName.color =gw_sand_external_mgr.GetOwnerColorBigGun(self.serData)
    else
         self.txtName.color = self.serData:GetColorType()
    end
    

    self.txtName.text = content
    self.txtName_outline:ManualUpdateOutline()
    self.imgBg_size:UpdateBackground()
end

return GWCompNameHud
