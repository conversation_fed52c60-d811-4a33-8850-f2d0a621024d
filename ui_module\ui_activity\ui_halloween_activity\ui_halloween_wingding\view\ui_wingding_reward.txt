local print = print
local require = require
local pairs = pairs     
local ipairs = ipairs
local string = string
local table = table
local tostring = tostring
local tonumber = tonumber
local type = type
local UIUtil = CS.Common_Util.UIUtil

local goods_item_new = require "goods_item_new"
local reward_mgr = require "reward_mgr"
local enum_define = require "enum_define"
local ui_base = require "ui_base"
local lang = require "lang"
local util = require "util"
local class = require "class"
local binding = require "ui_wingding_reward_binding"

--region View Life
module("ui_wingding_reward")
local ui_path = binding.UIPath
local window = nil
local UIView = {}

UIView.widget_table = binding.WidgetTable

function UIView:SetVCTypeUI()
    return self.__base.SetVCTypeUI(self, enum_define.enum_ui_vc_type.vc)
end

function UIView:Init()
    self:SetVCTypeUI()
    self.__base.Init(self)

    self.VData = {}
    self:InitScrollTable()
end

function UIView:OnShow()
    self.__base.OnShow(self)
end

function UIView:OnHide()
    self.__base.OnHide(self)
end

function UIView:Close(data)   
    if self.VData then
        for i, v in pairs(self.VData) do
            if type(v) == "table" and v.Dispose then
                v:Dispose()
            end
        end
    end
    self.VData = nil
    
    if self.srt_Content then
        self.srt_Content:ItemsDispose()
    end
    
    self.__base.Close(self)
    window = nil
end
--endregion

--region View Logic
function UIView:InitScrollTable()
    self.srt_Content.onItemRender = function(...)
        self:RewardRender(...)
    end
    self.srt_Content.onItemDispose = function(scroll_rect_item, index)
        if scroll_rect_item and scroll_rect_item.data then
            if scroll_rect_item.data.goodItemArr then
                for _, v in pairs(scroll_rect_item.data.goodItemArr) do
                    v:Dispose()
                end
                scroll_rect_item.data.goodItemArr = nil
            end
        end
    end
end

function UIView:RewardRender(scroll_rect_item, index, dataItem)
    scroll_rect_item.data = scroll_rect_item.data or { index, dataItem }
    scroll_rect_item.data[1] = index
    scroll_rect_item.data[2] = dataItem
    
    local levelText = scroll_rect_item:Get("levelText")
    local giftTable = {
        [1] = scroll_rect_item:Get("gift1"),
        [2] = scroll_rect_item:Get("gift2"),
        [3] = scroll_rect_item:Get("gift3"),
        [4] = scroll_rect_item:Get("gift4"),
        [5] = scroll_rect_item:Get("gift5")
    }
    local goodsParent = scroll_rect_item:Get("goodsParent")
    levelText.text = string.format("Lv.%s",index)

    for i, v in ipairs(giftTable) do
        self:SetActive(v,index == i)
    end
    
    if not scroll_rect_item.data.goodItemArr then
        scroll_rect_item.data.goodItemArr = {}
    end
    local rewardData = reward_mgr.GetRewardGoodsList(dataItem)
    local maxIndex = 0

    for i, v in ipairs(rewardData) do
        local goodItem = scroll_rect_item.data.goodItemArr[i] or goods_item_new.CGoodsItem():Init(goodsParent.transform, nil, 0.62)
        goodItem:SetGoods(nil, v.id, v.num, true)
        goodItem:SetCountEnable(true)
        scroll_rect_item.data.goodItemArr[i] = goodItem
        maxIndex = i
    end

    for i, v in ipairs(scroll_rect_item.data.goodItemArr) do
        if i > maxIndex then
            v:Dispose()
            scroll_rect_item.data.goodItemArr[i] = nil
        end
    end
end

---@public function 刷新奖励列表
function UIView:RefreshRewardList(data)
    if not data then
        return
    end
    local len = #data
    self.srt_Content:SetData(data, len)
    self.srt_Content:Refresh(0, -1)
end

--endregion

--region View Static 
local CUIView = class(ui_base, nil, UIView)

function Show(data)
    if window == nil then
        window = CUIView()
        window._NAME = _NAME
		window.isBlurBg = true

        if data and type(data) == "table" then
            local tempPath = data.uiPath or ui_path
            local tempParent = data.uiParent or nil
			window:LoadUIResource(tempPath, nil, tempParent, nil, true, false)
        else
			window:LoadUIResource(ui_path, nil, nil, nil, true, false)
        end
    end
    window:Show()
    return window
end

function Close(data)
    if window ~= nil then
        window:Close(data)
        window = nil
    end
end

function Hide()
    if window ~= nil then
        window:Hide()
    end
end
--endregion
