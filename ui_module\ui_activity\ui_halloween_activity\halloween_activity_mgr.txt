local require = require

local table = table
local pairs = pairs
local ipairs = ipairs
local type = type
local string = string
local math = math

local festival_activity_mgr = require "festival_activity_mgr"
local game_scheme = require "game_scheme"
local log = require "log"
local gw_task_data = require "gw_task_data"
local data_mgr = require "data_mgr"
local event = require "event"
local red_system = require "red_system"
local red_const = require "red_const"
local event_task_define = require "event_task_define"
local gw_event_activity_define = require "gw_event_activity_define"
local gw_task_const = require "gw_task_const"

local halloween_const = require "halloween_const"

local halloween_activity_mgr = {}

---所有数据存储
local _d = data_mgr:CreateData("halloween_activity_mgr")
---非服务器数据存储
local mc = _d.mde.const

local sub_mgr_list = {
    "halloween_sign_in_mgr",
    "halloween_slot_machine_mgr",
}


function halloween_activity_mgr.Init()
    event.Register(event.USER_DATA_RESET, Clear)
    
    for _, v in ipairs(sub_mgr_list) do
        require(v).Init()
    end
end

function halloween_activity_mgr.GetJumpCenterItemID()
    local festivalActivityID = halloween_const.act_id.jump_center
    local festivalActivityCfg = game_scheme:festivalActivity_0(festivalActivityID)
    return festivalActivityCfg.ctnID1[1][0]
end

---@public function 设置活动ID
function halloween_activity_mgr.SetActivityID(activityID)
    mc.activityID = activityID
end

---@public function 获取活动ID
function halloween_activity_mgr.GetActivityID()
    return mc.activityID
end


---@public function 获取红点数量
function halloween_activity_mgr.GetRed()
    
    return 0
end

function Clear()
    _d.mde:clear()
    mc = _d.mde.const
end


return halloween_activity_mgr