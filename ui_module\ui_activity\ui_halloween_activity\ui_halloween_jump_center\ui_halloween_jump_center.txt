local print = print
local require = require
local pairs = pairs     
local ipairs = ipairs
local string = string
local table = table
local tostring = tostring
local tonumber = tonumber
local type = type
local UIUtil = CS.Common_Util.UIUtil

local enum_define = require "enum_define"
local ui_base = require "ui_base"
local lang = require "lang"
local util = require "util"
local class = require "class"
local binding = require "ui_halloween_jump_center_binding"

local gw_asset_mgr = require "gw_asset_mgr"
local slot_machine_ani_helper = require "slot_machine_ani_helper"

--region View Life
module("ui_halloween_jump_center")
local ui_path = binding.UIPath
local window = nil
local UIView = {}

UIView.widget_table = binding.WidgetTable

function UIView:SetVCTypeUI()
    return self.__base.SetVCTypeUI(self, enum_define.enum_ui_vc_type.vc)
end

function UIView:Init()
    self:SetVCTypeUI()
    self.__base.Init(self)

    self.VData = {}
end

function UIView:OnShow()
    self.__base.OnShow(self)
end

function UIView:OnHide()
    self.__base.OnHide(self)
end

function UIView:Close(data)   
    if self.VData then
        for i, v in pairs(self.VData) do
            if type(v) == "table" and v.Dispose then
                v:Dispose()
            end
        end
    end
    self.VData = nil    
    self.__base.Close(self)
    window = nil
end
--endregion

--region View Logic

-- 刷新活动倒计时
function UIView:RenderActivityCountDownTime(timeStr)
    --self["txt_ActivityCountDownTime"].text = timeStr
    self["txt_ActivityCountDownTime"].text = lang.Get(656002) .. timeStr
end

function UIView:RenderCoinItem(id)
    gw_asset_mgr:LoadGoodsIcon(id, function(sprite)
        if not self:IsValid() then
            return
        end
        self["rtf_goal_item"].sprite = sprite
    end)
    
end

function UIView:RenderList(dataList)
    local jump_item = require "jump_item"
    slot_machine_ani_helper.ClearChildren(self.rtf_scrollContent)
    local go_template = self.item_JumpCenterItem
    for i, v in ipairs(dataList) do
        local go = CS.UnityEngine.GameObject.Instantiate(go_template)
        go:SetActive(true)
        go.transform:SetParent(self.rtf_scrollContent.transform, false)
        local rtf_item = go:GetComponent("RectTransform")
        jump_item.Render(rtf_item, i, v)
    end
end


--endregion

--region View Static 
local CUIView = class(ui_base, nil, UIView)

function Show(data)
    if window == nil then
        window = CUIView()
        window._NAME = _NAME

        if data and type(data) == "table" then
            local tempPath = data.uiPath or ui_path
            local tempParent = data.uiParent or nil
			window:LoadUIResource(tempPath, nil, tempParent, nil, nil, true)
        else
			window:LoadUIResource(ui_path, nil, nil, nil,nil, true)
        end
    end
    window:Show()
    return window
end

function Close(data)
    if window ~= nil then
        window:Close(data)
        window = nil
    end
end

function Hide()
    if window ~= nil then
        window:Hide()
    end
end
--endregion
