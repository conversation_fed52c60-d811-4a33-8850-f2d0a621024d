local print = print
local require = require
local pairs = pairs     
local ipairs = ipairs
local string = string
local table = table
local newClass = newclass
local type = type
local os = os
local time_util = require "time_util"

local controller_base = require "controller_base"
local ui_window_mgr = require "ui_window_mgr"
local lang = require "lang"

local halloween_slot_machine_mgr = require "halloween_slot_machine_mgr"
local halloween_activity_mgr = require "halloween_activity_mgr"
local skep_mgr = require "skep_mgr"
local halloween_jump_center_helper = require "halloween_jump_center_helper"
local game_scheme = require("game_scheme")

--region Controller Life
module("ui_halloween_jump_center_controller")
local controller = nil
local UIController = newClass("ui_halloween_jump_center_controller", controller_base)

function UIController:Init(view_name, controller_name, data)
    self.CData = {}
    self.__base.Init(self, view_name, controller_name)    
end

local activeTimer = nil

function UIController:OnShow()
    self.__base.OnShow(self)


    -- 活动倒计时
    if activeTimer then
        self:RemoveTimer(activeTimer)
        activeTimer = nil
    end
    local activeEndTime = self:GetActivityTime()
    local activeTimerTemp = self:CreateTimer(1, function()
        local curTime = os.server_time()
        local time = time_util.FormatTime5(activeEndTime - curTime)
        self:TriggerUIEvent("RenderActivityCountDownTime", time)
    end)
    activeTimer = activeTimerTemp
    
    -- 代币
    local coin_id = halloween_activity_mgr.GetJumpCenterItemID()
    local coin_num = skep_mgr.GetGoodsNum(coin_id)
    self:TriggerUIEvent("RenderCoinItem", coin_id, skep_mgr.GetGoodsNum(coin_id))

    -- 列表
    local list_data = halloween_jump_center_helper.GetListData()
    self:TriggerUIEvent("RenderList", list_data)
end

function UIController:Close(data)   
    if self.CData then
        for i, v in pairs(self.CData) do
            if type(v) == "table" and v.Dispose then
                v:Dispose()
            end
        end
    end

    self.CData = nil
    if not data or not data.isRecycleUI then
        self.__base.Close(self)
        controller = nil
    end
end

function UIController:AutoSubscribeEvents() 
end

function UIController:AutoUnsubscribeEvents() 
end
--endregion

--region Controller Logic
function  UIController:OnBtnTipsBtnClickedProxy()
end

--endregion

--region Controller Static 
function Show()
    if controller == nil then
        controller = UIController.new()
    end
    return controller
end
--endregion
