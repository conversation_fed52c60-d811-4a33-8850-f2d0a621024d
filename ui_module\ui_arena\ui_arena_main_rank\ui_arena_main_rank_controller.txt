local print = print
local require = require
local pairs = pairs     
local ipairs = ipairs
local string = string
local table = table
local newClass = newclass
local type = type

local gw_task_mgr = require "gw_task_mgr"
local event = require "event"
local gw_event_activity_define = require "gw_event_activity_define"
local event_personalInfo = require "event_personalInfo"
local arena_common_const = require "arena_common_const"
local event_arena_common_define = require "event_arena_common_define"
local table_util = require "table_util"
local arena_common_mgr = require "arena_common_mgr"
local controller_base = require "controller_base"
local ui_window_mgr = require "ui_window_mgr"
local lang = require "lang"

--region Controller Life
module("ui_arena_main_rank_controller")
local controller = nil
local UIController = newClass("ui_arena_main_rank_controller", controller_base)

function UIController:Init(view_name, controller_name, data)
    self.CData = {}
    self.__base.Init(self, view_name, controller_name)
    self.isGetRank = false
    self.arenaType = data.arenaType
    self.activityId = data.activityId
    self.rankData = {}
    self.rankList = {}
    
    --请求玩家所在排行榜分页
    self.arenaID = arena_common_mgr.GetCurArenaID(self.arenaType)
    self.pageIndex = arena_common_mgr.GetPlayerRankPage(self.arenaID)
    --当前分页中位数
    self.midRandIndex = (self.pageIndex - 1) * arena_common_const.SingleRankCount * 1.5
    self.rankIndex = arena_common_mgr.GetPlayerCurRank(self.arenaID)
    self:InitShow()
    self:InitBaseShow()
end

function UIController:OnShow()
    self.__base.OnShow(self)
end

function UIController:Close(data)   
    if self.CData then
        for i, v in pairs(self.CData) do
            if type(v) == "table" and v.Dispose then
                v:Dispose()
            end
        end
    end
    self.rankList = {}
    self.likeList = {}
    self.taskIdList = nil
    
    self.CData = nil
    if not data or not data.isRecycleUI then
        self.__base.Close(self)
        controller = nil
    end
end

function UIController:AutoSubscribeEvents()
    self.updateRandData = function(_, msg)
        --刷新排行榜显示
        if self.arenaType ~= msg.arenaType then
            return
        end
        self:UpdateRandData(msg)
        self:UpdateTopRankShow(msg)
        self:RefreshShow(msg)
    end
    self:RegisterEvent(event_arena_common_define.TMSG_NEW_ARENA_GET_RANK_INFO_RSP, self.updateRandData)

    --region 点赞
    --刷新点赞数量
    self.UpdateLiking = function(_,msg)
        self:RefreshLikeNum(msg.spLikeNum)
        self:RefreshLikeRedDot()
    end
    self:RegisterEvent(event_personalInfo.REFRESH_ARENA_LIKE,self.UpdateLiking)

    --刷新点赞红点
    self.UpdateTopicData = function()
        self:RefreshLikeRedDot()
        event.Trigger(gw_event_activity_define.GW_ACTIVITY_RED_NEED_UPDATE,self.activityId) --刷新活动红点
    end
    self:RegisterEvent(event_personalInfo.REFRESH_ARENA_TOPIC_LIKENUM,self.UpdateTopicData)

    --点赞回复
    self.OnClickLikeCallBack = function(eventName,data)
        self:RefreshLikeNum(data.nLikeRoleNums, self.curPlayID) --刷新点赞数据
        self:ShowFlowTxt() --显示点赞成功飘字
        self:ShowAni() --播放获得奖励动画
    end
    self:RegisterEvent(event_personalInfo.ROLE_PRAISE_UPDATE_MSG,self.OnClickLikeCallBack)
    --endregion
    
    --请求防守阵容
    self.OnGetDefenceTeam = function(_, msg)
        local arenaID = arena_common_mgr.GetCurArenaID(self.arenaType)
        if not arenaID or arenaID ~= msg.nArenaID then
            return
        end
        local player_mgr = require "player_mgr"
        local roleID = player_mgr.GetPlayerRoleID()
        if msg.nTargetRoleID and msg.nTargetRoleID ~= roleID then
            --不处理非自己的防守阵容数据
            return
        end
        arena_common_mgr.OpenDefendLineUpUI(self.arenaType, msg)
        
    end
    self:RegisterEvent(event_arena_common_define.TMSG_NEW_ARENA_GET_DEFENCE_LINEUP_RSP, self.OnGetDefenceTeam)
    
    --切换竞技场成功
    self.changeArenaSuccess = function(_, msg)
        arena_common_mgr.ChangeArenaSuccess(self.arenaType, msg.nNewArenaID)
    end
    self:RegisterEvent(event_arena_common_define.TMSG_NEW_ARENA_CHANGE_ARENA_RSP, self.changeArenaSuccess)
    
end

function UIController:AutoUnsubscribeEvents() 
end
--endregion

--region Controller Logic
function  UIController:OnBtnTipBtnClickedProxy()
    arena_common_mgr.OpenArenaHelpUI(self.arenaType)
end

function  UIController:OnBtnShowDailyRewardClickedProxy()
    --打开每日奖励
    local data = {
        activityId = self.activityId,
        taskIdList = arena_common_mgr.GetDailyTaskList(self.activityId, self.arenaType),
    }
    self:TriggerUIEvent("ShowDailyReward", data)
end
function  UIController:OnBtnPromotionClickedProxy()
    --打开晋升界面
    arena_common_mgr.OpenPromoteArenaUI(self.arenaType)
end

function  UIController:OnBtnShowArenaRewardClickedProxy()
    --1009115	获得风暴竞技场第{%s1}名的玩家所在的联盟所有成员，都会获得一份对应的联盟奖励
    arena_common_mgr.OpenArenaRewardUI(self.activityId, self.arenaType, 1009115)
end
function  UIController:OnBtnShowRecordListClickedProxy()
    --打开战斗记录
    arena_common_mgr.OpenBattleRecordUI(self.arenaType)
end
function  UIController:OnBtnSetDefenseClickedProxy()
    --设置防守阵容
    local player_mgr = require "player_mgr"
    local roleID = player_mgr.GetPlayerRoleID()
    arena_common_mgr.RequestDefendLineUp(self.arenaType, roleID)
end
function  UIController:OnBtnChallengeClickedProxy()
    --打开挑战界面
    local game_scheme = require "game_scheme"
    local needChallengeNum = game_scheme:InitBattleProp_0(8513)
    if not needChallengeNum or not needChallengeNum.szParam then
        return
    end
    local data = {
        needChallengeNum = needChallengeNum.szParam.data[0],
        arenaType = self.arenaType,
    }
    arena_common_mgr.OpenArenaChallengeUI(data)
end

--初始化界面数据
function UIController:InitShow()
    local serData = arena_common_mgr.GetRankData(self.arenaType)
    self:UpdateRandData(serData)
    self:UpdateTopRankShow(serData)
    self:RefreshShow(serData)
    self:RefreshDailyBoxShow()
end

--初始化基础显示
function UIController:InitBaseShow()
    local arenaId = arena_common_mgr.GetCurArenaID(self.arenaType)
    local arenaCfg = arena_common_mgr.GetArenaCfg(arenaId)
    if not arenaCfg or not arenaCfg.ArenaHelpTipsID then
        return
    end
    local data = {
        serverIdsStr = arena_common_mgr.GetArenaMatchServerStr(arenaId),
        endTime = arena_common_mgr.GetArenaEndTime(arenaId) or 0,
        ArenaName = arenaCfg.ArenaName,
        arenaTypeLang = arenaCfg.ArenaName,
    }
    self:TriggerUIEvent("InitBaseShow",data)
end

--刷新界面显示
function UIController:RefreshShow(serData)
    if not serData or not serData.tSelfRankInfo then
        return
    end
    local arenaId = arena_common_mgr.GetCurArenaID(self.arenaType)
    local arenaCfg = arena_common_mgr.GetArenaCfg(arenaId)
    if not arenaCfg or not arenaCfg.ChallengeTime then
        return
    end
    local data = {
        myRankInfo = arena_common_mgr.ParseRankData(serData.tSelfRankInfo, self.arenaType, true),
        challengeNum = arenaCfg.ChallengeTime - serData.nChallengedNum,
        canPromote = serData.nUpgradeArenaID,
    }
    self:TriggerUIEvent("RefreshShow",data)
end

--刷新每日宝箱显示
function UIController:RefreshDailyBoxShow()
    if not self.taskIdList then
        self.taskIdList = arena_common_mgr.GetDailyTaskList(self.activityId, self.arenaType)
    end
    if not self.taskIdList then
        return
    end
    local index = #self.taskIdList
    for k, v in ipairs(self.taskIdList) do
        if gw_task_mgr.GetTaskIsReceive(v) then
            index = k
        end
    end
    self:TriggerUIEvent("RefreshDailyBoxShow", index - 1)
end

--刷新前三名显示
function UIController:UpdateTopRankShow(data)
    if not data or not data.lsTopNRoleArenaRankInfo then
        return
    end
    local likingClick = function(playerData)
        self.curPlayID = playerData.roleId
        arena_common_mgr.RequestLike(self.curPlayID, self.arenaType)
    end
    self.rankTopList = arena_common_mgr.ParseRankData(data.lsTopNRoleArenaRankInfo, self.arenaType)
    self:TriggerUIEvent("SetTopPlayerInfo",self.rankTopList, likingClick)
    arena_common_mgr.RequestLikeData(self.rankTopList, self.arenaType)
end

--刷新排行榜数据
function UIController:UpdateRandData(data)
    if not data or not data.lsRoleArenaRankInfo then
        return
    end
    local rankList = arena_common_mgr.ParseRankData(data.lsRoleArenaRankInfo, self.arenaType)
    if not rankList then
        return
    end
    local serListIndex = rankList[1] and rankList.rank or 0
    local curListIndex = self.rankList[1] and self.rankList[1].rank or 0
    local tmpList = serListIndex > curListIndex and rankList or self.rankList
    local curList = serListIndex > curListIndex and self.rankList or rankList
    local updateNum = serListIndex < curListIndex and arena_common_const.SingleRankCount or nil
    for _, v in ipairs(tmpList) do
        if v.recordId > 0 then
            table.insert(curList,v)
        end
    end
    self.rankList = curList
    self:CustomRecordList(self.rankList)
    self:TriggerUIEvent("UpdateRand",self.rankList, updateNum)
end

function UIController:CustomRecordList(data_table)
    if not data_table then
        data_table = {}
    end
    function GetItem(t,k)
        --这里去实现自己对应的数据获取方式和来源      
        --就是dataItem吧
        local info ={}
        return data_table[k]
    end
    --注意 只有你想要动态获取item的值时才需要设置，如果list很短，其实没必要，直接全部传过去
    table_util.SetDynamicGetItem(data_table,GetItem)
    return data_table
end

function UIController:OnScrollRectItemRender(index, dataItem)
    if dataItem.rank ~= self.midRandIndex - 1 or dataItem.rank ~= self.midRandIndex + 1 then
        return
    end
    if dataItem.rank == self.midRandIndex - 1 then
        --上滑
        self.pageIndex = self.pageIndex - 1
    elseif dataItem.rank == self.midRandIndex + 1 then
        --下滑
        self.pageIndex = self.pageIndex + 1
    end
    self.midRandIndex = (self.pageIndex - 1) * arena_common_const.SingleRankCount * 1.5
    arena_common_mgr.ReqArenaRankData(nil, self.arenaType, self.pageIndex)
end

--点赞无通用接口
--region 点赞
--点赞成功飘字
function UIController:ShowFlowTxt()
    local click_liking_mgr = require "click_liking_mgr"
    local likeConfigType = arena_common_const.LikeConfigType[self.arenaType]
    local itemInfo = click_liking_mgr.GetRewardInfo(likeConfigType)
    if itemInfo then
        local str = string.format("%s%s",itemInfo.num,lang.Get(itemInfo.nameKey))
        local flow_text = require "flow_text"
        flow_text.Add(string.format2(lang.Get(1007308),str))
    end
end

--刷新点赞数据
function UIController:RefreshLikeNum(data, roleID)
    self.likeList = {}
    if not self.rankTopList then
        return
    end
    for k, v in ipairs(self.rankTopList) do
        local info = v
        info.rank = k
        info.likeNum = 0
        self.likeList[info.roleID] = info
    end
    if data and type(data) == "table" then
        for i,v in ipairs(data) do
            if self.likeList[v.dbid] then
                self.likeList[v.dbid].likeNum = v.likeNum
                self:TriggerUIEvent("RefreshSingleLikingNum", self.likeList[v.dbid].rank,self.likeList[v.dbid])
            end
        end
    elseif data and type(data) == "number" and roleID then
        if self.likeList[roleID] then
            self.likeList[roleID].likeNum = data
            self:TriggerUIEvent("RefreshSingleLikingNum", self.likeList[roleID].rank,self.likeList[roleID])
        end
    end
end

--刷新点赞红点
function UIController:RefreshLikeRedDot()
    local data_personalInfo = require "data_personalInfo"
    local click_liking_mgr = require "click_liking_mgr"
    local likeConfigType = arena_common_const.LikeConfigType[self.arenaType]
    local likeClientType = arena_common_const.LikeClientType[self.arenaType]
    local curPlayCanLikeNum = data_personalInfo.GetPersonalInfoValue(likeClientType)
    local LikeMaxCount = click_liking_mgr.GetArenaMaxLikeCount(likeConfigType)
    for i = 1,3 do
        self:TriggerUIEvent("RefreshSingleLikingRedDot", i, curPlayCanLikeNum and curPlayCanLikeNum > 0 and i == 1)
    end
end

--播放获得奖励动画
function UIController:ShowAni()
    if not self.likeList or not self.likeList[self.curPlayID] then
        return
    end
    local index = self.likeList[self.curPlayID].rank
    self:TriggerUIEvent("ShowLikingAnimation", index)
end

--endregion

--endregion

--region Controller Static 
function Show()
    if controller == nil then
        controller = UIController.new()
    end
    return controller
end
--endregion
