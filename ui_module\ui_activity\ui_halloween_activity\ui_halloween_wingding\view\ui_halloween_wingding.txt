local print = print
local require = require
local pairs = pairs     
local ipairs = ipairs
local string = string
local table = table
local tostring = tostring
local tonumber = tonumber
local type = type
local UIUtil = CS.Common_Util.UIUtil

local goods_item_new = require "goods_item_new"
local e_handler_mgr = require "e_handler_mgr"
local time_util = require "time_util"
local os = require "os"
local enum_define = require "enum_define"
local ui_base = require "ui_base"
local lang = require "lang"
local util = require "util"
local class = require "class"
local binding = require "ui_halloween_wingding_binding"

--region View Life
module("ui_halloween_wingding")
local ui_path = binding.UIPath
local window = nil
local UIView = {}

UIView.widget_table = binding.WidgetTable

function UIView:SetVCTypeUI()
    return self.__base.SetVCTypeUI(self, enum_define.enum_ui_vc_type.vc)
end

function UIView:Init()
    self:SetVCTypeUI()
    self.__base.Init(self)

    self.VData = {}
    self:InitGiftClickBtn()
end

function UIView:OnShow()
    self.__base.OnShow(self)
end

function UIView:OnHide()
    self.__base.OnHide(self)
end

function UIView:Close(data)   
    if self.VData then
        for i, v in pairs(self.VData) do
            if type(v) == "table" and v.Dispose then
                v:Dispose()
            end
        end
    end
    self.VData = nil

    if self.endTimer then
        util.RemoveDelayCall(self.endTimer)
        self.endTimer = nil
    end
    if self.unLockTimer then
        util.RemoveDelayCall(self.unLockTimer)
        self.unLockTimer = nil
    end
    if self.rankGoodsItem then
        self.rankGoodsItem:Dispose()
        self.rankGoodsItem = nil
    end
    
    self.__base.Close(self)
    window = nil
end
--endregion

--region View Logic

---@public function 设置活动结束时间
function UIView:SetActivityTimer(endTime)
    if endTime then
        self.endTimer = self.endTimer or util.IntervalCall(1, function()
            local tempTime = endTime - os.server_time()
            if not util.IsObjNull(self.txt_time) then
                self.txt_time.text = time_util.FormatTime5(tempTime)
            end
            if tempTime <= 0 then
                if not util.IsObjNull(self.txt_time) then
                    self.txt_time.text = lang.Get(102401) --"活动已结束"
                end
                self.endTimer = nil
                return true
            end
        end)
    end
end

---@public function 设置下一级解锁时间
function UIView:SetNextUnlockTimer(unLockTime)
    if unLockTime then
        self.unLockTimer = self.unLockTimer or util.IntervalCall(1, function()
            local tempTime = unLockTime - os.server_time()
            if not util.IsObjNull(self.txt_unLockTime) then
                self.txt_unLockTime.text =string.format(lang.Get(1009298), time_util.FormatTime5(tempTime))
            end
            if tempTime <= 0 then
                if not util.IsObjNull(self.txt_unLockTime) then
                    self:SetActive(self.txt_unLockTime, false)
                end
                self.endTimer = nil
                return true
            end
        end)
    end
end

---@public function 刷新聚会进度条
function UIView:RefreshTaskProgress(viewData)
    if not viewData then
        return
    end
    self.txt_level.text = string.format("Lv.%s",viewData.curLevel) 
    self.txt_sldNum.text = string.format("<color=#FFEC5C>%s</color>/%s", viewData.curProgress, viewData.maxProgress)
    self:SetActive(self.txt_unLockTime, viewData.isNextLvUnlock)
    
    local curSliderProgress = viewData.curProgress - viewData.preOffset
    local maxSliderProgress = viewData.maxProgress - viewData.preOffset
    
    if maxSliderProgress and maxSliderProgress > 0 then
        self.sld_leftSlider.value = curSliderProgress / maxSliderProgress
    end
end

---@public function 初始化礼物点击
function UIView:InitGiftClickBtn()
    for i = 1, 5 do
        self:AddOnClick(self["btn_pumpkin" .. i].onClick, function()
            e_handler_mgr.TriggerHandler(self.controller_name,"OnClickReceiveGift",i)
        end)
    end
end

---@public function 刷新宝箱区
function UIView:RefreshGiftArea(taskData)
    local isCanReceive = false
    for i, v in pairs(taskData) do
        self["ss_pumpkin" .. i]:Switch(v.status and 1 or 0)
        if not isCanReceive and not v.status and v.isGet then
            isCanReceive = true
        end
    end
    self:SetActive(self.rtf_arrow, isCanReceive)
end

---@public function 刷新排行奖励
function UIView:RefreshRankReward(rewardData)
    self.rankGoodsItem = self.rankGoodsItem or goods_item_new.CGoodsItem():Init(self.rtf_rankRewardParent.transform, nil, 0.53)
    self.rankGoodsItem:SetGoods(nil, rewardData.id, rewardData.num, nil, nil, nil)
end

--endregion

--region View Static 
local CUIView = class(ui_base, nil, UIView)

function Show(data)
    if window == nil then
        window = CUIView()
        window._NAME = _NAME
		window.isBlurBg = true

        if data and type(data) == "table" then
            local tempPath = data.uiPath or ui_path
            local tempParent = data.uiParent or nil
			window:LoadUIResource(tempPath, nil, tempParent, nil, true, false)
        else
			window:LoadUIResource(ui_path, nil, nil, nil, true, false)
        end
    end
    window:Show()
    return window
end

function Close(data)
    if window ~= nil then
        window:Close(data)
        window = nil
    end
end

function Hide()
    if window ~= nil then
        window:Hide()
    end
end
--endregion
