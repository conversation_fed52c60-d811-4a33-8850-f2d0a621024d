local require = require
local pairs = pairs     
local string = string
local table = table
local type = type
local LayoutRebuilder=CS.UnityEngine.UI.LayoutRebuilder

local enum_define = require "enum_define"
local ui_base = require "ui_base"
local lang = require "lang"
local util = require "util"
local class = require "class"
local binding = require "ui_diamond_gift_binding"
local card_sprite_asset =require "card_sprite_asset"

--region View Life
module("ui_diamond_gift")
local ui_path = binding.UIPath
local window = nil
local UIView = {}

UIView.widget_table = binding.WidgetTable

local goodsAsset =nil
local iconAsset =nil

function UIView:SetVCTypeUI()
    return self.__base.SetVCTypeUI(self, enum_define.enum_ui_vc_type.vc)
end

function UIView:Init()
    self:SetVCTypeUI()
    self.__base.Init(self)
    self.VData = {}
    self:InitScrollRectTable() 
end

function UIView:OnShow()
    self.__base.OnShow(self)
    goodsAsset=card_sprite_asset.CreateSpriteAsset()
    iconAsset=card_sprite_asset.CreateGWDiamondIcon()
end

function UIView:SetTopInfo()
     --设置标题
    self.txt_title.text=lang.Get(1002006)
end

function UIView:InitScrollRectTable() 

    
    self.srt_Content.onItemRender =onRenderItem
    self.srt_Content.onItemDispose=function(scroll_rect_item, index)
        if scroll_rect_item and scroll_rect_item.data  then
          --rect_table Dispose时 Item上相关的资源是否需要Dispose

        end       
    end
end

function UIView:UpdateScrollList(data,len) 
    if not data then 
        return
    end
    self.srt_Content:SetData(data,len or #data) 
    self.srt_Content:Refresh(-1, -1)
end



function onRenderItem(scroll_rect_item,index,dataItem)
	scroll_rect_item.data = scroll_rect_item.data or {index,dataItem}
    scroll_rect_item.data[1] = index
    scroll_rect_item.data[2] = dataItem

    local data = dataItem       --列表对象数据
    local pirce = scroll_rect_item:Get("pirce") --价格
    local icon =scroll_rect_item:Get("icon")    --主图标Icon
    local title= scroll_rect_item:Get("title")  --标题
    local NumDiamond =scroll_rect_item:Get("NumLimit") --购买获取的道具数量描述
    local textRoot = scroll_rect_item:Get("xiangouDI")  --获取道具面板信息显示
    local itemicon =scroll_rect_item:Get("itemicon") --第一个道具的Icon
    local hotFlag =scroll_rect_item:Get("hotFlag")--双倍标签
    
    --字体加载是异步的，可能在界面打开的时候，item预制体还没有切换成对应的字体，但是其他item已经创建出来了，会导致其他item没有切换字体成功，所以再执行一次设置字体
    local lang_util = require "lang_util"
    lang_util.SetFont(scroll_rect_item.gameObject)
    --设置价格
    local net_recharge_module = require "net_recharge_module"
    pirce.text =net_recharge_module.GetMoneyStrByGoodsID(data.goodsId)
    --设置主ICon 和 奖励Icon 和数量
    local rewardData= data.rewardList
    iconAsset:GetSprite(data.activityIcon,function(sp) 
        if not util.IsObjNull(icon) then 
            icon.sprite=sp
            icon:SetNativeSize()
        end
    end)
    goodsAsset:GetSprite(rewardData[1].icon,function(sp) 
        if not util.IsObjNull(itemicon) then 
            itemicon.sprite=sp
        end
    end)     
    NumDiamond.text=rewardData[1].Num
    --设置标题
    title.text = lang.Get(data.activityName)
    LayoutRebuilder.ForceRebuildLayoutImmediate(textRoot)

    --设置双倍标签显示
    hotFlag.transform:SetActive(data.showDouble)

     --绑定上点击等事件，主要是为了抛给controller层进行分离   
    scroll_rect_item.InvokeFunc =  function(funcname,obj)
        --注意  这里的事件是存在多种的clickItemEvent 
        if dataItem[funcname] then
            dataItem[funcname](index,dataItem)
        end
    end  
end


function UIView:OnHide()
    self.__base.OnHide(self)
end

function UIView:Close(data)   
    if self.VData then
        for i, v in pairs(self.VData) do
            if type(v) == "table" and v.Dispose then
                v:Dispose()
            end
        end
    end

    if goodsAsset then 
        goodsAsset:Dispose()
        goodsAsset=nil
    end

    if iconAsset then 
        iconAsset:Dispose()
        iconAsset=nil
    end

    if self.srt_Content then
        self.srt_Content:ItemsDispose()
    end
    self.VData = nil    
    self.__base.Close(self)
    window = nil
end
--endregion

--region View Logic

--endregion

--region View Static 
local CUIView = class(ui_base, nil, UIView)

function Show(data)
    if window == nil then
        window = CUIView()
        window._NAME = _NAME

        if data and type(data) == "table" then
            local tempPath = data.uiPath or ui_path
            local tempParent = data.uiParent or nil
			window:LoadUIResource(tempPath, nil, tempParent, nil, nil, nil, nil, nil, nil, true)
        else
			window:LoadUIResource(ui_path, nil, nil, nil, nil, nil, nil, nil, nil, true)
        end
    end
    window:Show()
    return window
end

function Close(data)
    if window ~= nil then
        window:Close(data)
        window = nil
    end
end

function Hide()
    if window ~= nil then
        window:Hide()
    end
end
--endregion
