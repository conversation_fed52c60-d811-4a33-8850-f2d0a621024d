-- langutil.txt --------------------------------------------------
-- author:  郑秀程
-- date:    2017.10.17
-- ver:     1.0
-- desc:    国际化工具方法
-------------------------------------------------------------------

local typeof = typeof
local print = print
local require = require
local setmetatable = setmetatable
local type = type
local string = string
local table = table
local tonumber = tonumber
local unpack = unpack
local coroutine = coroutine

local util = require "util"
local property_define = require "property_define"
local game_scheme = require "game_scheme"
local log = require "log"
local Lang = require "lang"
local asset_loader = require "asset_loader"

local Text = CS.UnityEngine.UI.Text
local GameObject = CS.UnityEngine.GameObject
local LocalizationText = CS.War.UI.LocalizationText
local LocalizationFontSize = CS.War.UI.LocalizationFontSize
local GameSchemeProxy = CS.War.Common.GameSchemeProxy
local TextMesh = CS.UnityEngine.TextMesh
local MeshRenderer = CS.UnityEngine.MeshRenderer
local TextMeshProUGUI        = CS.TMPro.TextMeshProUGUI
local LuaGetArrayIndex = util.LuaGetArrayIndex
local LuaGetComponentsInChildren = util.LuaGetComponentsInChildren
local LocalizationFontSizeType = typeof(LocalizationFontSize)
local LocalizationTextType = typeof(LocalizationText)
local TextMeshProUGUIType = typeof(TextMeshProUGUI)
local TextType = typeof(Text)
local TextMeshType = typeof(TextMesh)

module("lang_util")

local Chn2LangID = {}
local AutoChange = false
local assetLoaders = {}

--[[获取key对应的资源]]
function Get(nKey)
    return Lang.Get(nKey)
end

function Translate(chnStr)
    local text = string.trim(chnStr)
    return Lang.Get(Chn2LangID[text] or 0)
end

--[[设置Text对象的Text]]
function SetTextWithFormat(comText, nKey, ...)
    if nil == comText then
        return
    end

    if type(nKey) == 'number' then
        local strText = string.format(Lang.Get(nKey), ...) --"确定花费%s...."
        comText.text = strText
    else
        comText.text = nKey
    end
end

--[[设置Go对象的Text]]
function SetGoChild(go, nKey)
    if nil == go then
        return
    end

    local comText = go:GetComponentInChildren(typeof(TextMeshProUGUI))
    SetText(comText, nKey)
end

--[[设置Go对象的Text]]
function SetGo(go, nKey)
    if nil == go then
        return
    end

    local comText = go:GetComponent(typeof(TextMeshProUGUI))
    SetText(comText, nKey)

end

--[[设置组件的文本]]
function SetText(comText, nKey)
    if nil == comText then
        return
    end

    if type(nKey) == 'number' then
        comText.text = Lang.Get(nKey)
    else
        comText.text = nKey
    end
end

--[[翻译某个GameObject
@param go 要翻译的对象
]]
function TranslateGameObject(go)
    if nil == go then
        return
    end

    --if AutoChange then
    local textList = LuaGetComponentsInChildren(go, TextMeshProUGUIType, true)
    local curTextMeshProUGUI
    for i = 0, textList.Length - 1 do
        curTextMeshProUGUI = LuaGetArrayIndex(textList, i, TextMeshProUGUIType)
        local preText = string.trim(curTextMeshProUGUI.text)
        if string.len(preText) > 0 and (Chn2LangID[preText] or 0) ~= 0 then
            curTextMeshProUGUI.text = Lang.Get(Chn2LangID[preText])
        end
    end
    --end
    local localText
    local localTextList = LuaGetComponentsInChildren(go, LocalizationTextType, true)
    for i = 0, localTextList.Length - 1 do
        localText = LuaGetArrayIndex(localTextList, i, LocalizationTextType)
        if localText == nil then
            return
        end

        if localText.TextKey > 0 then
            local goTmp = localText.gameObject
            local comText = goTmp:GetComponent(TextMeshProUGUIType)
            if not comText then
                comText = goTmp:GetComponent(typeof(Text))
            end
            if nil ~= comText then
                comText.text = Lang.Get(localText.TextKey)
            end
        end

        --翻译完之后就丢弃
        -- GameObject.Destroy(localText)
    end
    return localTextList.Length
end

--[[    根据语言种类设置本文字号
]]
local langMap = {
    [1] = "zh", --中文
    [2] = "en", --英语
    [3] = "fa", --繁体
    [4] = "ind", --印尼语
    [5] = "pi", --菲律宾语
    [6] = "vi", --越南语
    [7] = "th", --泰语
    [8] = "po", --葡萄牙语
    [9] = "ko", --韩语
    [10] = "fr", --法语
    [11] = "de", --德语
    [12] = "ma", --马来西亚语
    [13] = "jp", --日语
}

function SetFontSize(go)
    if nil == go then
        return
    end

    local localTextList = LuaGetComponentsInChildren(go, LocalizationFontSizeType, true)
    if localTextList.Length == 0 then
        return
    end
    local localText
    for i = 0, localTextList.Length - 1 do
        localText = LuaGetArrayIndex(localTextList, i, LocalizationFontSizeType)
        if localText ~= nil then
            local script
            for i = 0, localText.scripts.Count - 1 do
                script = localText.scripts[i]
                if script then
                    if langMap[script:GetLangKey()] == Lang.USE_LANG then
                        localText.gameObject:GetComponent(typeof(Text)).fontSize = script.fontSize
                        break
                    end
                end
            end
        end
    end
end

local fontcache = {}

function SetFont(go)
    if nil == go then
        return
    end
    
    local TextList = LuaGetComponentsInChildren(go, TextType, true)
    local text
    for i = 0, TextList.Length - 1 do
         text = LuaGetArrayIndex(TextList, i, TextType)
        if text ~= nil and (text.font ~= nil) then
            local ui_setting_cfg = require "ui_setting_cfg"
            if util.IsObjNull(text.font) then
                log.Error(text.name)
            end
            local textType = ui_setting_cfg.LangTypeMap[text.font.name]
            if textType then
                local config = ui_setting_cfg.LangFontAssetName[Lang.USE_LANG][textType]
                if (config) then
                    if text.font.name ~= config.name then
                        local assetBundle = config.ab
                    
                        if fontcache[assetBundle] == nil then
                            --闭包引用
                            local tmpText = text
                            assetLoaders[assetBundle] = asset_loader(assetBundle, "lang_util")
                            assetLoaders[assetBundle]:load(function(obj)
                                local UIPrefab = obj.asset
                                if tmpText and (not util.IsObjNull(tmpText)) then
                                    tmpText.font = UIPrefab
                                    tmpText.lineSpacing = config.lineSpacing and config.lineSpacing or 1
                                    fontcache[assetBundle] = UIPrefab
                                end
                            end)
                        else
                            if text and (not util.IsObjNull(text)) then
                                text.font = fontcache[assetBundle]
                                text.lineSpacing = config.lineSpacing and config.lineSpacing or 1
                            end
                        end
                    else
                        
                    end
                end
            end
        end
    end
    SetTMPFont(go)
    
    local util = require "util"
    if util.get_len(typeMap) > 0 then
        return typeMap
    end
end

--设置tmp字体
function SetTMPFont(go)
    local typeMap = {}
    local TextMeshList = LuaGetComponentsInChildren(go, TextMeshProUGUIType, true)
    for i = 0, TextMeshList.Length - 1 do
        text = LuaGetArrayIndex(TextMeshList, i, TextMeshProUGUIType)
        if text ~= nil then
            local ui_setting_cfg = require "ui_setting_cfg"
            local textType = ui_setting_cfg.TMPLangTypeMap[text.font.name]
            if textType then
                typeMap[text] = textType
                local config = ui_setting_cfg.TMPLangFontAssetName[Lang.USE_LANG][textType]
                if (config) then
                    if text.font.name ~= config.name then
                        local assetBundle = config.ab
                        if fontcache[assetBundle] == nil then
                            --闭包引用
                            local tmpText = text
                            if not assetLoaders[assetBundle] then
                            end
                            assetLoaders[assetBundle] = asset_loader(assetBundle, "lang_util")
                            assetLoaders[assetBundle]:load(function(obj)
                                local UIPrefab = obj.asset
                                if tmpText and (not util.IsObjNull(tmpText)) then
                                    tmpText.font = UIPrefab
                                    tmpText.lineSpacing = config.lineSpacing and config.lineSpacing or 1
                                    fontcache[assetBundle] = UIPrefab
                                    local matRenderer = tmpText:GetComponent(typeof(MeshRenderer))
                                    if not util.IsObjNull(matRenderer) then
                                        matRenderer.sharedMaterial = UIPrefab.material
                                    end
                                end
                            end)
                        else
                            if text and (not util.IsObjNull(text)) then
                                text.font = fontcache[assetBundle]
                                text.lineSpacing = config.lineSpacing and config.lineSpacing or 1
                                local matRenderer = text:GetComponent(typeof(MeshRenderer))
                                if not util.IsObjNull(matRenderer)  then
                                    matRenderer.sharedMaterial = fontcache[assetBundle].material
                                end
                            end
                        end
                    end
                end
            end
        end
    end
end
function SetTextMeshFontReferToTypeMap(go, typeMap)
    if nil == go or nil == typeMap then
        return
    end

    -- local TextMeshList = go:GetComponentsInChildren(typeof(TextMesh), true) or { Length = 0 }
    local TextMeshList = LuaGetComponentsInChildren(go, TextMeshType, true)
    local text
    for i = 0, TextMeshList.Length - 1 do
        text = LuaGetArrayIndex(TextMeshList, i, TextMeshType)
        textType = typeMap[text]
        if textType then
            local ui_setting_cfg = require "ui_setting_cfg"
            local config = ui_setting_cfg.LangFontAssetName[Lang.USE_LANG][textType]
            if (config) then
                if text.font.name ~= config.name then
                    local assetBundle = config.ab
                    if fontcache[assetBundle] == nil then

                        assetLoaders[assetBundle] = asset_loader(assetBundle, "lang_util")
                        assetLoaders[assetBundle]:load(function(obj)
                            local UIPrefab = obj.asset
                            text.font = UIPrefab
                            fontcache[assetBundle] = UIPrefab
                            local matRenderer = text:GetComponent(typeof(MeshRenderer))
                            if matRenderer then                
                                matRenderer.sharedMaterial = UIPrefab.material
                            end
                        end)
                    else
                        text.font = fontcache[assetBundle]
                        local matRenderer = text:GetComponent(typeof(MeshRenderer))
                        if matRenderer then                
                            matRenderer.sharedMaterial = fontcache[assetBundle].material
                        end
                    end
                end
            end
        end
    end
end

local fontCacheSigneText={}
function SetTextMeshFont(textmesh,fontabname)

    if util.IsObjNull(textmesh) or fontabname=="" then
        return
    end
    local newfontmat = GameObject.Instantiate(textmesh.material)
    textmesh.material=newfontmat
    if fontCacheSigneText[fontabname] then
        local UIPrefab = fontCacheSigneText[fontabname].asset
        textmesh.font = UIPrefab
    else
        assetLoaders[fontabname] = asset_loader(fontabname, "singlefont")
        assetLoaders[fontabname]:load(function(obj)
            local UIPrefab = obj.asset
            textmesh.font = UIPrefab
            fontCacheSigneText[fontabname]=obj
        end)
    end
    
    
end

--[[根据国际化Key，解析字符串，例：str:%s#1234#%s,params:12001#12002.输出：生命值#1234#伤害]]
function FormatString(str, params)
    local strKeys = util.SplitString(params, "#")
    local keys = {}
    for i = 1, #strKeys do
        local key = tonumber(strKeys[i])
        if key and key ~= 0 then
            table.insert(keys, Get(key))
        end
    end

    return string.format(str, unpack(keys))
end

--根据属性类型来返回国际化值
local function GetValueByPropType(propType, param)
    if not param or not propType then
        return nil
    end

    local value = nil
    if propType.showMode == property_define.PROP_SHOWMODE_Original then
        value = param
    elseif propType.showMode == property_define.PROP_SHOWMODE_CommonTranslate then
        value = string.format(Lang.Get(propType.params[1]), param)
    elseif propType.showMode == property_define.PROP_SHOWMODE_ChooseOneType then
        value = Lang.Get(propType.params[tonumber(param)])
    elseif propType.showMode == property_define.PROP_SHOWMODE_Compare then
        if tonumber(param) < propType.params[1] then
            value = Lang.Get(propType.params[2])
        else
            value = param
        end
    elseif propType.showMode == property_define.PROP_SHOWMODE_Monster then
        local monster = game_scheme:Monster_0(tonumber(param))
        if monster then
            value = Lang.Get(monster.iNameKey)
        end
    end

    return value
end

--根据属性定义(property_define)来国际化解析属性,
--如16#2668; =>> 1019&12001&2668 (名字国际化id，图标id，文本值)
function FormatStringFromDefine(strPropKey)
    local propertiesData = {}
    local strKeys = util.SplitString(strPropKey, ";")
    if strKeys ~= nil then
        for i = 1, #strKeys do
            if #strKeys[i] > 0 then
                local arr = util.SplitString(strKeys[i], "#")
                --[1]是定义id，[2]是参数
                local defineItem = property_define.PropsTable[tonumber(arr[1])]
                if defineItem ~= nil then
                    local name = Get(defineItem.name) or "nil"
                    local iconID = defineItem.icon or "nil"
                    local value = GetValueByPropType(defineItem, arr[2]) or "nil"

                    table.insert(propertiesData, name .. "&" .. iconID .. "&" .. value)
                end
            end
        end
    end
    return propertiesData
end

--根据属性定义(property_define)来国际化解析属性,
--如16#2668; =>> 1019&12001&2668 (名字国际化id，图标id，文本值)
function FormatStringFromDefineEx(strPropKey)
    local propertiesData = {}
    local strKeys = util.SplitString(strPropKey, ";")
    if strKeys ~= nil then
        for i = 1, #strKeys do
            if #strKeys[i] > 0 then
                local arr = util.SplitString(strKeys[i], "#")
                --[1]是定义id，[2]是参数
                local defineItem = property_define.PropsTable[tonumber(arr[1])]
                if defineItem ~= nil then
                    local name = Get(defineItem.name1) or "nil"
                    local iconID = defineItem.icon or "nil"
                    local value = GetValueByPropType(defineItem, arr[2]) or "nil"

                    table.insert(propertiesData, name .. "&" .. iconID .. "&" .. value)
                end
            end
        end
    end
    return propertiesData
end

function GetLocalMoney(num, needPrefix, roundUp)
    local recharge_data = require "recharge_data"
    local cfg = recharge_data.GetExchangeCfg(num * 100)
    local exchangeFactor = tonumber(Lang.Get(9154))
    local str = string.format(Lang.Get(9179), (cfg and Lang.USE_LANG ~= Lang.ZH) and cfg.en / 100 or (num / exchangeFactor + (roundUp and 0.005 or 0))) -- TODO .en从lang那边取

    if needPrefix then
        return Lang.Get(9178) .. str
    end
    return str
end

function InitChn2LangID()
    local nums = game_scheme:Lang_nums()
    util.DelayCall(0, function(t)
        for i = 0, nums - 1 do
            local cfg = game_scheme:Lang(i)
            local text = string.trim(cfg.zh)

            -- 一个中文长度3
            -- 格式控制符以及【的忽略
            if string.len(text) > 0 and
            --[[string.len(text) <= 18]]
            not string.find(text, '%%[dsf]') and not string.find(text, '【') and Chn2LangID[text] == nil then
                Chn2LangID[text] = cfg.nKey
            end

            if cfg.nKey >= 100000 then break end

            if (i % 40) == 0 then
                coroutine.yield(0)
            end
        end
        --print('--done. line sum is', table.count(Chn2LangID), nums)
        AutoChange = true
    end)
end

setmetatable(_M, { __index = require "lang_res_key" })

GameSchemeProxy.OnTranslateGameObject = TranslateGameObject
GameSchemeProxy.OnSetFontSize = SetFontSize