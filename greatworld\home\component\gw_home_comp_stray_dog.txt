local GWG = GWG
local require = require
local newclass = newclass
local UIUtil = UIUtil
local table = require "table"
local util = require "util"
local math = math
local gw_home_config_mgr = require "gw_home_config_mgr"
local gw_home_comp_base_class = require "gw_home_comp_base_class"
local gw_home_astar_pathfinding = require "gw_home_astar_pathfinding"
local gw_home_lord_state_machine = require "gw_home_lord_state_machine"
local gw_home_grid_data = require "gw_home_grid_data"
local gw_ed = require "gw_ed"
local gw_gpu_animation_uitl = require "gw_gpu_animation_uitl"
local gw_home_comp_hud_stray_dog_bubble = require "gw_home_comp_hud_stray_dog_bubble"
local GWG = GWG
local print = print
local ipairs = ipairs
local event				= require "event"
local log = log
local tostring = tostring
-- Unity类型引用
local Vector3 = CS.UnityEngine.Vector3
local DG = CS.DG
local Tweening = CS.DG.Tweening
local Time = CS.UnityEngine.Time
local Quaternion = CS.UnityEngine.Quaternion
local Collider = CS.UnityEngine.Collider
local SphereCollider = CS.UnityEngine.SphereCollider
local type = type
local typeof = typeof

module("gw_home_comp_stray_dog")
-- 默认动画配置（会在初始化时根据狗屋等级更新）
local ClickAnimations = {"Skill01", "Skill02", "Skill03"}
local StandAnimation = "Skill04"

-- 动画帧数配置（用于计算播放时间）
local AnimationFrameConfig = {
    ["Skill01"] = 36,   -- Skill01动画帧数
    ["Skill02"] = 127,  -- Skill02动画帧数
    ["Skill03"] = 128,  -- Skill03动画帧数
}

-- 动画帧率
local AnimationFrameRate = 30

---@class GWHomeCompStrayDog : GWHomeCompBaseClass
local GWHomeCompStrayDog = newclass("gw_home_comp_stray_dog", gw_home_comp_base_class)

-- 构造器
function GWHomeCompStrayDog:ctor()
    gw_home_comp_base_class.ctor(self)
end

-- 初始化动画配置
function GWHomeCompStrayDog:InitAnimationConfig()
    if self.levelConfig then
        ClickAnimations = self.levelConfig.clickAnimations
        StandAnimation = self.levelConfig.standAnimation
        GWG.GWAdmin.SwitchUtility.HomeLog("流浪狗使用预设等级配置：等级" .. self.levelConfig.level)
        return
    end

    local buildingData = GWG.GWHomeMgr.buildingData.GetBuildingDataByBuildingType(GWG.GWConst.enBuildingType.enBuildingType_DogHouse)
    local dogHouseLevel = 1 -- 默认等级

    if buildingData and buildingData.nLevel then
        dogHouseLevel = buildingData.nLevel
    end

    -- 根据狗屋等级设置动画配置
    if dogHouseLevel == 1 then
        ClickAnimations = {"Skill01", "Skill02", "Skill03"}
        StandAnimation = "Skill04"
    elseif dogHouseLevel == 2 then
        ClickAnimations = {"Show", "Skill02", "Skill03"}
        StandAnimation = "Stand"
    else
        -- 默认使用1级配置
        ClickAnimations = {"Skill01", "Skill02", "Skill03"}
        StandAnimation = "Skill04"
    end
end

-- 初始化
function GWHomeCompStrayDog:Init()
    gw_home_comp_base_class.Init(self)

    -- 初始化动画配置（根据狗屋等级）
    self:InitAnimationConfig()

    -- 获取流浪狗配置
    local strayDogConfig = gw_home_config_mgr.GetStrayDogConfig()

    -- 流浪狗基本属性
    self.entityType = GWG.GWConst.EHomeEntityType.StrayDog
    self.compName = GWG.GWCompName.gw_home_comp_stray_dog

    -- 跟随相关参数（使用配置）
    self.followDistance = strayDogConfig.followDistance  -- 跟随距离（网格单位）
    self.followLordId = nil     -- 跟随的领主ID
    self.followLordComp = nil   -- 跟随的领主组件引用

    -- 巡逻相关参数（使用配置）
    self.patrolMinRadius = strayDogConfig.patrolMinRadius   -- 最小巡逻半径
    self.patrolMaxRadius = strayDogConfig.patrolMaxRadius   -- 最大巡逻半径
    self.restDuration = strayDogConfig.patrolMaxRestTime -- 休息时间（秒）
    self.interactDuration = strayDogConfig.interactTime  -- 交互时间（秒）
    self.moveSpeed = strayDogConfig.moveSpeed            -- 移动速度
    
    -- 当前状态数据
    self.currentPath = nil      -- 当前路径
    self.currentPathIndex = 1   -- 当前路径点索引
    self.targetGridX = nil      -- 目标网格X
    self.targetGridY = nil      -- 目标网格Y
    self.currentGridX = 52      -- 当前网格X（默认位置，会在OnLoaded中重新设置）
    self.currentGridY = 52      -- 当前网格Y（默认位置，会在OnLoaded中重新设置）
    
    -- 状态控制
    self.isFollowing = false    -- 是否正在跟随
    self.isInteracting = false
    self.isMovingToTarget = false
    self.manualTarget = nil     -- 玩家手动指定的目标点
    
    -- 计时器
    self.restTimer = nil
    self.interactTimer = nil
    self.patrolTimer = nil
    self.moveTimer = nil
    self.followCheckTimer = nil -- 跟随检查计时器
    
    -- 状态机
    self.stateMachine = nil
    
    -- GPU动画控制
    self.gpuAnimator = nil
    self.currentAnimation = ""
    
    -- 移动相关
    self.moveTween = nil

    -- 转向相关
    self.isRotating = false        -- 是否正在转向
    self.rotationTween = nil       -- 转向动画
    self.targetRotation = nil      -- 目标旋转角度
    self.rotationCallback = nil    -- 转向完成回调
    self.customMoveTimer = nil

    -- 领主移动方向/历史（用于优化跟随位置，减少左右摆动）
    self.lastLordGridX = nil
    self.lastLordGridY = nil
    self.lordMoveDirX = 0
    self.lordMoveDirY = 0
    
    -- 点击动画播放列表管理
    self.clickAnimationPlaylist = {}
    self.clickAnimationIndex = 1
    self:ShuffleClickAnimations()

    -- 气泡组件
    self.bubbleComponent = nil
end

-- 加载完成回调
function GWHomeCompStrayDog:OnLoaded()
    gw_home_comp_base_class.OnLoaded(self)

    -- 设置初始位置（在领主附近生成）
    self:SetInitialPosition()
    
    -- 获取GPU动画组件
    if self.transform then
        self.gpuAnimator = gw_gpu_animation_uitl.GetGpuAnimate(self.transform.gameObject)
        if self.gpuAnimator then
            -- 添加到GPU动画管理器
            gw_gpu_animation_uitl.AddAnimator("stray_dog", self.gpuAnimator)
        end
    end
    
    -- 设置初始动画
    self:PlayAnimation(StandAnimation)
    
    -- 创建状态机（复用领主的状态机）
    self.stateMachine = gw_home_lord_state_machine.CreateLordStateMachine(self)
    
    -- 尝试找到领主并开始跟随
    self:TryFindAndFollowLord()

    -- 创建气泡组件
    self:CreateBubbleComponent()
end

-- 创建气泡组件
function GWHomeCompStrayDog:CreateBubbleComponent()
    if not self.transform then
        return
    end

    -- 使用HUD工具类创建气泡组件，但不作为领主的子对象
    local gw_home_hud_util = require "gw_home_hud_util"
    local bubbleData = {
        parent = GWG.GWHomeNode.heroNode(), -- 放在heroNode下，与领主同级
        strayDogTransform = self.transform      -- 传递领主Transform用于跟随
    }
    local GWHomeHudConst = require "gw_home_hud_const"
    local MoveHudTypes = GWHomeHudConst.MoveHud
    local data = MoveHudTypes[ GWG.GWCompName.gw_home_comp_hud_stray_dog_bubble]
    data.parent = GWG.GWHomeNode.heroNode()
    data.strayDogTransform =  self.transform
    
    local id = nil
    id , self.bubbleComponent = gw_home_hud_util.InitMoveHudComponent(
        GWG.GWCompName.gw_home_comp_hud_stray_dog_bubble,
        GWG.GWHomeNode.heroNode() -- 父节点设置为heroNode
    )

end

-- 触发气泡显示（点击时调用）
function GWHomeCompStrayDog:TriggerBubbleOnClick()
    if self.bubbleComponent then
        self.bubbleComponent:TriggerInteractBubble()
    else
        GWG.GWAdmin.SwitchUtility.HomeLog("流浪狗气泡组件不存在，无法显示气泡")
    end
end

-- 刷新模型
function GWHomeCompStrayDog:Upgrade()
    -- 获取狗屋建筑数据
    local buildingData = GWG.GWHomeMgr.buildingData.GetBuildingDataByBuildingType(GWG.GWConst.enBuildingType.enBuildingType_DogHouse)
    if not buildingData then
        GWG.GWAdmin.SwitchUtility.HomeLog("流浪狗升级失败：无法获取狗屋建筑数据")
        return
    end

    local dogHouseLevel = buildingData.nLevel
    GWG.GWAdmin.SwitchUtility.HomeLog("流浪狗升级：狗屋等级为 " .. dogHouseLevel)

    -- 根据狗屋等级设置模型路径和动画配置
    local modelPath
    local newClickAnimations
    local newStandAnimation
    -- 目前策划跟美术没对齐 搞得配置动作都乱糟糟的，都没有规律，先硬编码吧
    if dogHouseLevel == 1 then
        modelPath = "art/characters/xiaogou/prefabs/xiaogou_simple.prefab"
        newClickAnimations = {"Skill01", "Skill02", "Skill03"}
        newStandAnimation = "Skill04"
    elseif dogHouseLevel == 2 then
        modelPath = "art/characters/xiaogou01/prefabs/xiaogou01_simple.prefab"
        newClickAnimations = {"Show", "Skill02", "Skill03"}
        newStandAnimation = "Stand"
    else
        -- 默认使用1级配置
        modelPath = "art/characters/xiaogou/prefabs/xiaogou_simple.prefab"
        newClickAnimations = {"Skill01", "Skill02", "Skill03"}
        newStandAnimation = "Skill04"
        GWG.GWAdmin.SwitchUtility.HomeLog("流浪狗升级：未知狗屋等级 " .. dogHouseLevel .. "，使用默认配置")
    end

    -- 更新全局动画配置
    ClickAnimations = newClickAnimations
    StandAnimation = newStandAnimation

    -- 重新打乱点击动画播放列表
    self:ShuffleClickAnimations()

    GWG.GWAdmin.SwitchUtility.HomeLog("流浪狗升级动画配置更新：站立动画：" .. StandAnimation .. "，点击动画：" .. table.concat(ClickAnimations, ","))

    -- 保存当前位置和状态
    local currentGridX = self.currentGridX
    local currentGridY = self.currentGridY
    local wasFollowing = self.isFollowing
    local wasMoving = self.isMovingToTarget

    -- 停止当前所有活动
    self:StopMovement()
    if self.followCheckTimer then
        util.RemoveDelayCall(self.followCheckTimer)
        self.followCheckTimer = nil
    end

    -- 清理旧的GPU动画组件
    if self.gpuAnimator then
        gw_gpu_animation_uitl.RemoveAnimator(self.gpuAnimator)
        self.gpuAnimator = nil
    end

    -- 销毁旧模型
    if self.entity then
        self.entity:Dispose()
        self.entity = nil
    end

    -- 获取配置
    local gw_home_config_mgr = require "gw_home_config_mgr"
    local strayDogConfig = gw_home_config_mgr.GetStrayDogConfig()

    -- 加载新模型
    self.entity = GWG.GWAssetMgr:Load(modelPath, function(asset)
        self:Bind(asset)

        -- 设置模型缩放和位置调整
        if asset and asset.transform then
            asset.transform.localScale = { x = strayDogConfig.modelScale, y = strayDogConfig.modelScale, z = strayDogConfig.modelScale }

            -- 添加碰撞体用于点击检测
            local collider = asset:GetComponent(typeof(Collider))
            if not collider then
                -- 如果没有碰撞体，添加一个球形碰撞体
                collider = asset:AddComponent(typeof(SphereCollider))
                if collider then
                    collider.radius = 0.3  -- 设置碰撞体半径，比领主小
                    collider.isTrigger = false  -- 不是触发器，用于点击检测
                end
            end

            -- 恢复位置
            self:SetGridPosition(currentGridX, currentGridY)

            -- 获取新的GPU动画组件
            if self.transform then
                self.gpuAnimator = gw_gpu_animation_uitl.GetGpuAnimate(self.transform.gameObject)
                if self.gpuAnimator then
                    -- 添加到GPU动画管理器
                    gw_gpu_animation_uitl.AddAnimator("stray_dog", self.gpuAnimator)
                end
            end

            -- 播放新的站立动画（使用升级后的动画配置）
            GWG.GWAdmin.SwitchUtility.HomeLog("流浪狗升级完成，播放新站立动画：" .. newStandAnimation)
            self:PlayAnimation(newStandAnimation)

            -- 恢复之前的状态
            if wasFollowing then
                self:TryFindAndFollowLord()
            elseif not wasMoving then
                self:StartRest()
            end

            GWG.GWAdmin.SwitchUtility.HomeLog("流浪狗模型升级完成：" .. modelPath)
        end

    end, GWG.GWHomeNode.heroNode(), "Home_StrayDog_Upgraded")
end

-- 设置初始位置（在领主附近生成）
function GWHomeCompStrayDog:SetInitialPosition()
    local gw_home_lord_util = require "gw_home_lord_util"
    local lordComp = gw_home_lord_util.GetLordComp()

    local spawnX, spawnY = 52, 52  -- 默认位置

    if lordComp then
        -- 在领主附近找一个可行走的位置
        local lordX, lordY = lordComp.currentGridX, lordComp.currentGridY
        local foundPos = false

        -- 尝试在领主周围3格范围内找位置
        for radius = 1, 3 do
            for dx = -radius, radius do
                for dy = -radius, radius do
                    if not foundPos and (math.abs(dx) == radius or math.abs(dy) == radius) then
                        local testX, testY = lordX + dx, lordY + dy
                        if gw_home_astar_pathfinding.IsWalkable(testX, testY) then
                            spawnX, spawnY = testX, testY
                            foundPos = true
                            break
                        end
                    end
                end
                if foundPos then break end
            end
            if foundPos then break end
        end

        GWG.GWAdmin.SwitchUtility.HomeLog("流浪狗生成位置: ("..spawnX..","..spawnY.."), 领主位置: ("..lordX..","..lordY..")")
    else
        GWG.GWAdmin.SwitchUtility.HomeLog("未找到领主，流浪狗使用默认位置: ("..spawnX..","..spawnY..")")
    end

    -- 设置位置
    self:SetGridPosition(spawnX, spawnY)
end

-- 设置网格位置
function GWHomeCompStrayDog:SetGridPosition(gridX, gridY)
    if not self.transform then
        return
    end

    self.currentGridX = gridX
    self.currentGridY = gridY

    -- 转换为世界坐标
    local worldX, worldY, worldZ = gw_home_grid_data.GetPosByGridXY(gridX, gridY, true)
    UIUtil.SetLocalPos(self.transform, worldX, worldY, worldZ)
end

-- 根据当前世界位置更新网格位置
function GWHomeCompStrayDog:UpdateGridPosition()
    if not self.transform then
        return
    end

    -- 获取当前世界位置
    local currentPos = self.transform.position

    -- 转换为网格坐标（使用正确的方法名）
    local gridX, gridY = gw_home_grid_data.GetGridXYdByXZ(currentPos.x, currentPos.z)

    -- 更新网格位置记录
    self.currentGridX = gridX
    self.currentGridY = gridY
end

-- 播放GPU动画
function GWHomeCompStrayDog:PlayAnimation(animationName, loop)
    if not self.gpuAnimator then --or self.currentAnimation == animationName
        return
    end

    self.currentAnimation = animationName

    if loop == nil then
        loop = true -- 默认循环播放
    end
    --log.Error(animationName)
    -- 播放GPU动画
    gw_gpu_animation_uitl.SetAnimatorState(self.gpuAnimator, animationName)

    -- 动画播放完后回到Stand（如果不循环播放）
    --if animationName == ClickAnimation and not loop then
    --    util.DelayCallOnce(1.0, function()
    --        if self.gpuAnimator then
    --            self:PlayAnimation("Stand")
    --        end
    --    end)
    --end
end

-- 尝试找到并跟随领主
function GWHomeCompStrayDog:TryFindAndFollowLord()
    local gw_home_lord_util = require "gw_home_lord_util"
    local lordComp = gw_home_lord_util.GetLordComp()
    
    if lordComp then
        self:StartFollowingLord(lordComp)
    else
        -- 没有领主，开始独立巡逻
        self:StartIndependentPatrol()
    end
end

-- 开始跟随领主
function GWHomeCompStrayDog:StartFollowingLord(lordComp)
    self.followLordComp = lordComp
    self.isFollowing = true
    
    -- 停止独立巡逻
    self:StopIndependentPatrol()
    
    -- 开始跟随检查循环
    self:StartFollowCheckLoop()
    
    GWG.GWAdmin.SwitchUtility.HomeLog("流浪狗开始跟随领主")
end

-- 停止跟随领主
function GWHomeCompStrayDog:StopFollowingLord()
    self.followLordComp = nil
    self.isFollowing = false
    
    -- 停止跟随检查
    if self.followCheckTimer then
        util.RemoveDelayCall(self.followCheckTimer)
        self.followCheckTimer = nil
    end
    
    -- 开始独立巡逻
    self:StartIndependentPatrol()
    
    GWG.GWAdmin.SwitchUtility.HomeLog("流浪狗停止跟随，开始独立巡逻")
end

-- 开始跟随检查循环
function GWHomeCompStrayDog:StartFollowCheckLoop()
    if not self.isFollowing or not self.followLordComp then
        return
    end
    
    -- 检查领主状态并决定跟随行为
    self:CheckAndFollowLord()
    
    -- 设置下次检查（使用配置的检查间隔）
    local strayDogConfig = gw_home_config_mgr.GetStrayDogConfig()
    self.followCheckTimer = util.DelayCallOnce(strayDogConfig.followCheckInterval, function()
        self:StartFollowCheckLoop()
    end)
end

-- 检查并跟随领主
function GWHomeCompStrayDog:CheckAndFollowLord()
    -- 如果正在播放Show动画，暂停跟随检查
    if self.isPlayingShowAnimation then
        GWG.GWAdmin.SwitchUtility.HomeLog("流浪狗正在播放Show动画，暂停跟随检查")
        return
    end

    if not self.followLordComp or not self.followLordComp.transform then
        -- 领主不存在，停止跟随
        self:StopFollowingLord()
        return
    end

    local lordGridX = self.followLordComp.currentGridX
    local lordGridY = self.followLordComp.currentGridY

    -- 计算与领主的距离
    local distance = math.sqrt((self.currentGridX - lordGridX)^2 + (self.currentGridY - lordGridY)^2)

    -- 记录并估算领主移动方向（用于选择更稳定的跟随点）
    if self.lastLordGridX and self.lastLordGridY then
        local dirX = lordGridX - self.lastLordGridX
        local dirY = lordGridY - self.lastLordGridY
        if dirX ~= 0 or dirY ~= 0 then
            -- 归一化到-1/0/1 的网格方向
            if dirX > 0 then self.lordMoveDirX = 1 elseif dirX < 0 then self.lordMoveDirX = -1 end
            if dirY > 0 then self.lordMoveDirY = 1 elseif dirY < 0 then self.lordMoveDirY = -1 end
        end
    end
    self.lastLordGridX = lordGridX
    self.lastLordGridY = lordGridY

    -- 如果流浪狗正在移动中，不要频繁改变目标
    if self.isMovingToTarget then
        -- 检查当前目标是否还合理
        if self.targetGridX and self.targetGridY then
            local targetDistance = math.sqrt((lordGridX - self.targetGridX)^2 + (lordGridY - self.targetGridY)^2)
            -- 如果当前目标距离领主太远（超过6格），才重新规划路径（从3改到6了，放宽阈值，减少频繁重规划导致的左右摆动）
            if targetDistance > 6 then
                self:StopMovement()
            else
                -- 当前目标还合理，继续移动
                return
            end
        end
    end

    if self.followLordComp.isMovingToTarget then
        -- 领主正在移动，距离超过1格就跟随
        if distance > 1.5 then
            self:FollowMovingLord()
        end
    elseif distance > 2 then
        -- 距离超过2格就移动到领主附近
        self:MoveToNearLord()
    else
        -- 距离合适，保持当前状态
        if self.isMovingToTarget then
            -- 如果正在移动且距离已经合适，停止移动
            self:StopMovement()
            self:PlayAnimation(StandAnimation)
        end
    end
end

-- 跟随移动中的领主
function GWHomeCompStrayDog:FollowMovingLord()
    -- 简化跟随逻辑：直接跟随到领主当前位置附近
    local lordGridX = self.followLordComp.currentGridX
    local lordGridY = self.followLordComp.currentGridY

    -- 在领主附近找一个合适的跟随位置
    local targetX, targetY = self:FindFollowPosition(lordGridX, lordGridY)

    if targetX and targetY then
        local distance = math.sqrt((self.currentGridX - targetX)^2 + (self.currentGridY - targetY)^2)
        if distance > 1 then -- 距离大于1格就移动，保持紧密跟随
            self:MoveToTarget(targetX, targetY)
        end
    end
end

-- 在领主附近找一个合适的跟随位置
function GWHomeCompStrayDog:FindFollowPosition(lordX, lordY)
    -- 根据领主最近移动方向，优先选择“身后/侧后”的稳定位置，减少左右摆动
    local dirX, dirY = self.lordMoveDirX or 0, self.lordMoveDirY or 0

    local positions
    if dirX ~= 0 or dirY ~= 0 then
        -- 领主在移动：优先取其反方向（身后），其次两侧后方，最后再取原先的邻接点
        local backX, backY = lordX - dirX, lordY - dirY
        local side1X, side1Y = lordX - dirY, lordY + dirX     -- 背向左侧
        local side2X, side2Y = lordX + dirY, lordY - dirX     -- 背向右侧
        positions = {
            {backX, backY},
            {side1X, side1Y},
            {side2X, side2Y},
            {lordX - 1, lordY},     -- 左
            {lordX + 1, lordY},     -- 右
            {lordX, lordY - 1},     -- 下
            {lordX, lordY + 1},     -- 上
            {lordX - 1, lordY - 1}, -- 左下
            {lordX + 1, lordY - 1}, -- 右下
            {lordX - 1, lordY + 1}, -- 左上
            {lordX + 1, lordY + 1}, -- 右上
        }
    else
        -- 领主未明显移动：使用原始优先级
        positions = {
            {lordX - 1, lordY},     -- 左
            {lordX + 1, lordY},     -- 右
            {lordX, lordY - 1},     -- 下
            {lordX, lordY + 1},     -- 上
            {lordX - 1, lordY - 1}, -- 左下
            {lordX + 1, lordY - 1}, -- 右下
            {lordX - 1, lordY + 1}, -- 左上
            {lordX + 1, lordY + 1}, -- 右上
        }
    end

    -- 找到第一个可行走且不与领主重叠的位置
    for _, pos in ipairs(positions) do
        local x, y = pos[1], pos[2]
        if not (x == lordX and y == lordY) and gw_home_astar_pathfinding.IsWalkable(x, y) then
            return x, y
        end
    end

    -- 如果附近都不可行走，尝试更远的位置
    return self:FindFollowPositionExtended(lordX, lordY)
end

-- 扩展搜索跟随位置（更远距离，避免重叠）
function GWHomeCompStrayDog:FindFollowPositionExtended(lordX, lordY)
    -- 搜索半径2-4的位置
    for radius = 2, 4 do
        for dx = -radius, radius do
            for dy = -radius, radius do
                -- 只检查圆周上的点，不检查内部
                if math.abs(dx) == radius or math.abs(dy) == radius then
                    local x, y = lordX + dx, lordY + dy

                    -- 确保不与领主重叠
                    if not (x == lordX and y == lordY) then
                        if gw_home_astar_pathfinding.IsWalkable(x, y) then
                            GWG.GWAdmin.SwitchUtility.HomeLog("流浪狗：在半径"..radius.." 找到跟随位置"..x..","..y)
                            return x, y
                        end
                    end
                end
            end
        end
    end

    GWG.GWAdmin.SwitchUtility.HomeLog("流浪狗：警告！找不到合适的跟随位置，保持当前位置")
    -- 如果实在找不到，返回当前位置，不要返回领主位置
    return self.currentGridX, self.currentGridY
end

-- 移动到领主附近
function GWHomeCompStrayDog:MoveToNearLord()
    local lordGridX = self.followLordComp.currentGridX
    local lordGridY = self.followLordComp.currentGridY

    -- 使用新的跟随位置查找方法
    local targetX, targetY = self:FindFollowPosition(lordGridX, lordGridY)

    if targetX and targetY then
        self:MoveToTarget(targetX, targetY)
    end
end

-- 开始独立巡逻
function GWHomeCompStrayDog:StartIndependentPatrol()
    if self.isFollowing then
        return -- 正在跟随时不进行独立巡逻
    end

    -- 如果正在播放Show动画，暂停独立巡逻
    if self.isPlayingShowAnimation then
        GWG.GWAdmin.SwitchUtility.HomeLog("流浪狗正在播放Show动画，暂停独立巡逻")
        return
    end

    self:StartPatrolCycle()
end

-- 停止独立巡逻
function GWHomeCompStrayDog:StopIndependentPatrol()
    if self.patrolTimer then
        util.RemoveDelayCall(self.patrolTimer)
        self.patrolTimer = nil
    end

    if self.restTimer then
        util.RemoveDelayCall(self.restTimer)
        self.restTimer = nil
    end
end

-- 开始巡逻循环（独立模式）
function GWHomeCompStrayDog:StartPatrolCycle()
    if self.isFollowing then
        return
    end

    if self.patrolTimer then
        util.RemoveDelayCall(self.patrolTimer)
    end

    -- 立即开始第一次巡逻
    self:StartPatrol()
end

-- 开始巡逻（独立模式）
function GWHomeCompStrayDog:StartPatrol()
    if self.isFollowing then
        return
    end

    -- 切换到巡逻状态
    if self.stateMachine then
        self.stateMachine:ChangeState(gw_home_lord_state_machine.LordState.Patrol)
    end

    -- 如果有手动目标，优先移动到手动目标
    if self.manualTarget then
        self:MoveToTarget(self.manualTarget.x, self.manualTarget.y)
        self.manualTarget = nil
        return
    end

    -- 生成随机巡逻目标点
    local targetX, targetY = gw_home_astar_pathfinding.GetRandomWalkablePoint(
        self.currentGridX, self.currentGridY,
        self.patrolMinRadius, self.patrolMaxRadius
    )

    if targetX and targetY then
        self:MoveToTarget(targetX, targetY)
    else
        -- 如果找不到合适的巡逻点，直接进入休息状态
        self:StartRest()
    end
end

-- 移动到目标点
function GWHomeCompStrayDog:MoveToTarget(targetX, targetY)
    -- 参数验证
    if not targetX or not targetY then
        GWG.GWAdmin.SwitchUtility.HomeLog("流浪狗移动目标无效:"..tostring(targetX)..","..tostring(targetY))
        return
    end

    -- 停止当前移动
    if self.isMovingToTarget then
        self:StopMovement()
    end

    self.targetGridX = targetX
    self.targetGridY = targetY
    self.isMovingToTarget = true

    -- 使用当前记录的网格位置作为起点
    local startX, startY = self.currentGridX, self.currentGridY

    -- 使用A*寻路
    local path = gw_home_astar_pathfinding.FindPath(
        startX, startY,
        targetX, targetY
    )

    if path then
        -- 简化路径
        local simplifiedPath = gw_home_astar_pathfinding.SimplifyPath(path)

        -- 平滑路径，将直角拐弯变成弧形（使用配置的平滑半径）
        local pathfindingConfig = gw_home_config_mgr.GetPathfindingConfig()
        self.currentPath = gw_home_astar_pathfinding.SmoothPath(simplifiedPath, pathfindingConfig.smoothRadius)

        -- 过滤重复点/零步长点，避免0时长tween导致卡住
        self.currentPath = self:CleanPath(self.currentPath)
        self.currentPathIndex = 1

        -- 显示路径特效（手动移动时显示）
        if self.manualTarget then
            GWG.GWAdmin.SwitchUtility.HomeLog("流浪狗显示路径特效，使用路径点数:"..#self.currentPath)
            self:ShowPathEffect(self.currentPath)
        end

        -- 直接开始平滑移动，让MoveToGridPositionSmooth处理转向
        self:PlayAnimation("Run")
        self:MoveAlongSmoothPath()
    else
        -- 寻路失败，进入休息状态
        self.isMovingToTarget = false
        if not self.isFollowing then
            self:StartRest()
        end
    end
end

-- 沿路径移动
function GWHomeCompStrayDog:MoveAlongPath()
    if not self.currentPath or self.currentPathIndex > #self.currentPath then
        -- 路径完成
        self:OnReachTarget()
        return
    end

    local nextPoint = self.currentPath[self.currentPathIndex]
    if not nextPoint then
        self:OnReachTarget()
        return
    end

    -- 平滑移动到下一个路径点
    self:MoveToGridPosition(nextPoint.x, nextPoint.y, function()
        -- 移动完成后继续下一个点
        self.currentPathIndex = self.currentPathIndex + 1
        self:MoveAlongPath()
    end)
end

-- 平滑转向到指定方向
function GWHomeCompStrayDog:SmoothRotateTo(targetX, targetY, callback)
    if not self.transform then
        if callback then callback() end
        return
    end

    -- 计算目标方向
    local currentPos = self.transform.position
    local direction = Vector3(targetX, currentPos.y, targetY) - currentPos

    -- 如果距离太近，不需要转向
    if direction.magnitude < 0.1 then
        if callback then callback() end
        return
    end

    -- 计算目标旋转
    local targetRotation = Quaternion.LookRotation(direction)
    self:SmoothRotateToQuaternion(targetRotation, callback)
end

-- 平滑转向到指定四元数
function GWHomeCompStrayDog:SmoothRotateToQuaternion(targetRotation, callback)
    if not self.transform then
        if callback then callback() end
        return
    end

    -- 停止之前的转向动画
    self:StopRotation()

    local currentRotation = self.transform.rotation

    -- 计算角度差
    local angleDiff = Quaternion.Angle(currentRotation, targetRotation)

    -- 如果角度差很小，直接设置
    if angleDiff < 5 then
        self.transform.rotation = targetRotation
        if callback then callback() end
        return
    end

    -- 根据角度差计算转向时间
    local rotationTime = math.min(0.8, math.max(0.15, angleDiff / 180 * 0.4))

    -- 设置转向状态
    self.isRotating = true
    self.targetRotation = targetRotation
    self.rotationCallback = callback

    -- 创建转向动画
    self.rotationTween = self.transform:DORotateQuaternion(targetRotation, rotationTime)
        :SetEase(DG.Tweening.Ease.OutCubic)
        :OnComplete(function()
            self:OnRotationComplete()
        end)
end

-- 转向完成回调
function GWHomeCompStrayDog:OnRotationComplete()
    self.isRotating = false
    self.rotationTween = nil
    self.targetRotation = nil

    if self.rotationCallback then
        local callback = self.rotationCallback
        self.rotationCallback = nil
        callback()
    end
end

-- 停止转向动画
function GWHomeCompStrayDog:StopRotation()
    if self.rotationTween then
        self.rotationTween:Kill()
        self.rotationTween = nil
    end

    self.isRotating = false
    self.targetRotation = nil
    self.rotationCallback = nil
end

-- 检查是否需要初始转向
function GWHomeCompStrayDog:CheckNeedInitialRotation()
    if not self.transform or not self.currentPath or #self.currentPath < 2 then
        return false
    end

    local firstPoint = self.currentPath[2]
    local currentPos = self.transform.position
    local worldX, worldY, worldZ = gw_home_grid_data.GetPosByGridXY(firstPoint.x, firstPoint.y, true)
    local direction = Vector3(worldX, currentPos.y, worldZ) - currentPos

    if direction.magnitude < 0.1 then
        return false
    end

    local targetRotation = Quaternion.LookRotation(direction)
    local currentRotation = self.transform.rotation
    local angleDiff = Quaternion.Angle(currentRotation, targetRotation)

    -- 只有大于90度才需要初始转向
    return angleDiff > 90
end

-- 平滑路径移动
function GWHomeCompStrayDog:MoveAlongSmoothPath()
    if not self.currentPath or self.currentPathIndex > #self.currentPath then
        -- 路径完成
        self:OnReachTarget()
        return
    end

    local nextPoint = self.currentPath[self.currentPathIndex]
    if not nextPoint then
        self:OnReachTarget()
        return
    end

    -- 平滑移动到下一个路径点
    self:MoveToGridPositionSmooth(nextPoint.x, nextPoint.y, function()
        -- 移动完成后继续下一个点
        self.currentPathIndex = self.currentPathIndex + 1
        self:MoveAlongSmoothPath()
    end)
end

-- 平滑移动到网格位置（恒定速度，带旋转）
function GWHomeCompStrayDog:MoveToGridPositionSmooth(gridX, gridY, onComplete)
    if not self.transform then
        return
    end

    -- 计算目标世界坐标
    local targetWorldX, targetWorldY, targetWorldZ = gw_home_grid_data.GetPosByGridXY(gridX, gridY, true)
    local targetPos = Vector3(targetWorldX, targetWorldY, targetWorldZ)
    local currentPos = self.transform.position

    -- 计算移动距离和时间（恒定速度）
    local distance = Vector3.Distance(currentPos, targetPos)
    local moveTime = distance / self.moveSpeed

    -- 计算移动方向，用于旋转
    local direction = targetPos - currentPos
    direction.y = 0 -- 只考虑水平方向

    -- 停止之前的移动
    if self.moveTween then
        self.moveTween:Kill()
        self.moveTween = nil
    end

    -- 若距离极小/时间为0，直接瞬移并回调，避免tween不触发完成回调导致卡住
    if moveTime <= 0.01 then
        self:SetGridPosition(gridX, gridY)
        if onComplete then onComplete() end
        return
    end

    local hasDOTween = DG and Tweening and self.transform.DOMove ~= nil

    -- 同时进行移动和旋转
    if direction.magnitude > 0.1 then
        local targetRotation = Quaternion.LookRotation(direction)
        local currentRotation = self.transform.rotation
        local angleDiff = Quaternion.Angle(currentRotation, targetRotation)

        if hasDOTween then
            -- 移动动画
            self.moveTween = self.transform:DOMove(targetPos, moveTime)
                :SetEase(DG.Tweening.Ease.Linear)
                :OnUpdate(function()
                    -- 更新网格位置
                    self:UpdateGridPosition()
                    -- 动态销毁已走过的路径特效
                    self:UpdatePathEffectDestroy()
                end)
                :OnComplete(function()
                    self.moveTween = nil
                    -- 确保最终位置准确
                    self:SetGridPosition(gridX, gridY)
                    if onComplete then
                        onComplete()
                    end
                end)

            -- 只有角度差大于10度才进行旋转动画
            if angleDiff > 10 then
                -- 旋转动画（时间稍短，让旋转更快完成）
                local rotationTime = math.min(moveTime, 0.25)
                self.transform:DORotateQuaternion(targetRotation, rotationTime)
                    :SetEase(DG.Tweening.Ease.OutCubic)
            end
        else
            -- Fallback：无DOTween时使用自定义平滑移动
            self:StartCustomSmoothMove(currentPos, targetPos, moveTime, gridX, gridY, onComplete)
        end
    else
        -- 距离太近，直接移动/回调
        if hasDOTween then
            self.moveTween = self.transform:DOMove(targetPos, moveTime)
                :SetEase(DG.Tweening.Ease.Linear)
                :OnUpdate(function()
                    self:UpdateGridPosition()
                    -- 动态销毁已走过的路径特效
                    self:UpdatePathEffectDestroy()
                end)
                :OnComplete(function()
                    self.moveTween = nil
                    self:SetGridPosition(gridX, gridY)
                    if onComplete then
                        onComplete()
                    end
                end)
        else
            self:SetGridPosition(gridX, gridY)
            if onComplete then onComplete() end
        end
    end
end

-- 平滑移动到网格位置
function GWHomeCompStrayDog:MoveToGridPosition(gridX, gridY, onComplete)
    if not self.transform then
        return
    end

    -- 计算目标世界坐标
    local targetWorldX, targetWorldY, targetWorldZ = gw_home_grid_data.GetPosByGridXY(gridX, gridY, true)
    local targetPos = Vector3(targetWorldX, targetWorldY, targetWorldZ)

    -- 计算朝向
    local currentPos = self.transform.position
    local direction = targetPos - currentPos
    if direction.magnitude > 0.1 then
        -- 计算朝向角度（只考虑Y轴旋转）
        local targetRotation = Quaternion.LookRotation(direction)
        self.transform.rotation = targetRotation
    end

    -- 停止之前的移动动画
    if self.moveTween then
        self.moveTween:Kill()
        self.moveTween = nil
    end

    -- 尝试使用DOTween进行平滑移动
    local moveTime = direction.magnitude / self.moveSpeed
    local success = false

    -- 检查DOTween是否可用
    if DG and Tweening and self.transform.DOMove then
        self.moveTween = self.transform:DOMove(targetPos, moveTime):SetEase(Tweening.Ease.Linear)
        if self.moveTween then
            self.moveTween:OnComplete(function()
                self.moveTween = nil
                self.currentGridX = gridX
                self.currentGridY = gridY
                if onComplete then
                    onComplete()
                end
            end)
            success = true
        end
    end

    -- 如果DOTween不可用，使用自定义的平滑移动
    if not success then
        self:StartCustomSmoothMove(currentPos, targetPos, moveTime, gridX, gridY, onComplete)
    end
end

-- 自定义平滑移动实现
function GWHomeCompStrayDog:StartCustomSmoothMove(startPos, targetPos, duration, gridX, gridY, onComplete)
    local startTime = Time.time
    local endTime = startTime + duration

    -- 停止之前的移动
    if self.customMoveTimer then
        util.RemoveDelayCall(self.customMoveTimer)
        self.customMoveTimer = nil
    end

    local function updateMove()
        local currentTime = Time.time
        if currentTime >= endTime then
            -- 移动完成
            self.transform.position = targetPos
            self.currentGridX = gridX
            self.currentGridY = gridY
            self.customMoveTimer = nil
            if onComplete then
                onComplete()
            end
        else
            -- 插值移动
            local progress = (currentTime - startTime) / duration
            local currentPos = Vector3.Lerp(startPos, targetPos, progress)
            self.transform.position = currentPos

            -- 继续下一帧更新
            self.customMoveTimer = util.DelayCallOnce(0.02, updateMove) -- 50fps更新
        end
    end

    updateMove()
end

-- 到达目标点
function GWHomeCompStrayDog:OnReachTarget()
    self.isMovingToTarget = false
    self.currentPath = nil
    self.currentPathIndex = 1

    -- 播放站立动画
    self:PlayAnimation(StandAnimation)

    -- 隐藏路径特效
    self:HidePathEffect()

    -- 如果不是跟随模式，进入休息状态
    if not self.isFollowing then
        self:StartRest()
    end
end

-- 开始休息（独立模式）
function GWHomeCompStrayDog:StartRest()
    if self.isFollowing then
        return
    end

    -- 切换到休息状态
    if self.stateMachine then
        self.stateMachine:ChangeState(gw_home_lord_state_machine.LordState.Rest)
    end

    if self.restTimer then
        util.RemoveDelayCall(self.restTimer)
    end

    -- 休息指定时间后继续巡逻
    self.restTimer = util.DelayCallOnce(self.restDuration, function()
        if not self.isInteracting and not self.isFollowing then
            self:StartPatrol()
        end
    end)
end

-- 开始交互
function GWHomeCompStrayDog:StartInteract()
    -- 切换到交互状态
    if self.stateMachine then
        self.stateMachine:ChangeState(gw_home_lord_state_machine.LordState.Interact)
    end

    self.isInteracting = true

    -- 停止当前移动
    self:StopMovement()

    -- 交互指定时间后结束
    if self.interactTimer then
        util.RemoveDelayCall(self.interactTimer)
    end

    self.interactTimer = util.DelayCallOnce(self.interactDuration, function()
        self:EndInteract()
    end)
end

-- 结束交互
function GWHomeCompStrayDog:EndInteract()
    self.isInteracting = false

    -- 如果不是跟随模式，刷新休息时间，重新开始休息
    if not self.isFollowing then
        self:StartRest()
    end
end

-- 停止移动
function GWHomeCompStrayDog:StopMovement()
    -- 停止DOTween动画
    if self.moveTween then
        self.moveTween:Kill()
        self.moveTween = nil
    end

    -- 停止转向动画
    self:StopRotation()

    -- 停止自定义移动计时器
    if self.customMoveTimer then
        util.RemoveDelayCall(self.customMoveTimer)
        self.customMoveTimer = nil
    end

    if self.moveTimer then
        util.RemoveDelayCall(self.moveTimer)
        self.moveTimer = nil
    end

    self.currentPath = nil
    self.currentPathIndex = 1
    self.isMovingToTarget = false

    -- 播放站立动画
    self:PlayAnimation(StandAnimation)

    -- 隐藏路径特效
    self:HidePathEffect()
end

-- 处理点击事件
function GWHomeCompStrayDog:OnClick()

    -- 如果已经在播放Show动画，不重复处理
    if self.isPlayingShowAnimation then
        return
    end

    local properties = {LordType = 2}
    event.Trigger(event.GAME_EVENT_REPORT, "LordClick", properties)

    -- 设置Show动画播放状态
    self.isPlayingShowAnimation = true

    -- 记录点击时的状态，用于Show动画播放完成后恢复行为
    local clickState = {
        wasMoving = self.isMovingToTarget,
        wasFollowing = self.isFollowing,
        wasInteracting = self.isInteracting
    }

    -- 立即停止所有移动，让流浪狗停下来播放Show动画
    if self.isMovingToTarget then
        GWG.GWAdmin.SwitchUtility.HomeLog("流浪狗点击时正在移动，立即停止移动播放Show动画")
        self:StopMovement()
    end

    -- 触发气泡显示
    self:TriggerBubbleOnClick()

    -- 播放Show动画
    local clickAnimation = self:GetNextClickAnimation()
    self:PlayAnimation(clickAnimation, false) -- 不循环播放

    -- 播放对应的音效
    self:PlayClickAnimationSound(clickAnimation)

    if self.ClickAnimationTimer then
        util.RemoveDelayCall(self.ClickAnimationTimer)
        self.ClickAnimationTimer = nil
    end

    -- 根据动画名称动态计算播放时间
    local animationDuration = self:GetAnimationDuration(clickAnimation)

    -- 延迟执行原有的点击逻辑，等动画播放完成
    self.ClickAnimationTimer = util.DelayCallOnce(animationDuration, function()

        -- 清除Show动画播放状态
        self.isPlayingShowAnimation = false

        self:StopMovement()
        self:StartRest()

        -- 执行原有的点击逻辑
        if clickState.wasMoving then
            -- 如果之前正在移动，停止移动转为休息
            --self:StopMovement()
            --if not clickState.wasFollowing then
            --    self:StartRest()
            --end
        elseif not clickState.wasInteracting then
            -- 如果之前正在休息，开始交互
            --self:StartInteract()
        else
            -- 如果之前正在交互，刷新交互时间
            --if self.interactTimer then
            --    util.RemoveDelayCall(self.interactTimer)
            --end
            --self.interactTimer = util.DelayCallOnce(self.interactDuration, function()
            --    self:EndInteract()
            --end)
        end
    end)
end

-- 设置手动目标点
function GWHomeCompStrayDog:SetManualTarget(gridX, gridY)
    self.manualTarget = {x = gridX, y = gridY}

    if self.isFollowing then
        -- 如果正在跟随，暂时停止跟随去执行手动目标
        self:StopMovement()
        self:MoveToTarget(gridX, gridY)
    else
        -- 打断当前状态，移动到新目标
        self:StopMovement()
        if self.restTimer then
            util.RemoveDelayCall(self.restTimer)
        end
        if self.interactTimer then
            util.RemoveDelayCall(self.interactTimer)
            self.isInteracting = false
        end

        -- 立即开始移动到目标
        self:StartPatrol()
    end
end

-- 状态机回调方法（复用领主的）
function GWHomeCompStrayDog:OnEnterIdleState()
    -- 进入空闲状态
end

function GWHomeCompStrayDog:OnLeaveIdleState()
    -- 离开空闲状态
end

function GWHomeCompStrayDog:OnEnterPatrolState()
    -- 进入巡逻状态
end

function GWHomeCompStrayDog:OnLeavePatrolState()
    -- 离开巡逻状态
end

function GWHomeCompStrayDog:OnEnterRestState()
    -- 进入休息状态
end

function GWHomeCompStrayDog:OnLeaveRestState()
    -- 离开休息状态
end

function GWHomeCompStrayDog:OnEnterInteractState()
    -- 进入交互状态
end

function GWHomeCompStrayDog:OnLeaveInteractState()
    -- 离开交互状态
end

function GWHomeCompStrayDog:OnEnterMoveToState()
    -- 进入移动状态
end

function GWHomeCompStrayDog:OnLeaveMoveToState()
    -- 离开移动状态
end

-- 更新方法
function GWHomeCompStrayDog:Update()
    if self.stateMachine then
        self.stateMachine:Update()
    end
end

-- 回收资源
function GWHomeCompStrayDog:Recycle()
    -- 停止DOTween动画
    if self.moveTween then
        self.moveTween:Kill()
        self.moveTween = nil
    end

    if self.ClickAnimationTimer then
        util.RemoveDelayCall(self.ClickAnimationTimer)
        self.ClickAnimationTimer = nil
    end
    
    -- 停止转向动画
    self:StopRotation()

    -- 停止自定义移动计时器
    if self.customMoveTimer then
        util.RemoveDelayCall(self.customMoveTimer)
        self.customMoveTimer = nil
    end

    -- 清理计时器
    if self.restTimer then
        util.RemoveDelayCall(self.restTimer)
        self.restTimer = nil
    end

    if self.interactTimer then
        util.RemoveDelayCall(self.interactTimer)
        self.interactTimer = nil
    end

    if self.patrolTimer then
        util.RemoveDelayCall(self.patrolTimer)
        self.patrolTimer = nil
    end

    if self.moveTimer then
        util.RemoveDelayCall(self.moveTimer)
        self.moveTimer = nil
    end

    if self.followCheckTimer then
        util.RemoveDelayCall(self.followCheckTimer)
        self.followCheckTimer = nil
    end

    -- 清理GPU动画组件
    if self.gpuAnimator then
        gw_gpu_animation_uitl.RemoveAnimator(self.gpuAnimator)
        self.gpuAnimator = nil
    end

    if self.entity then
        self.entity:Dispose()
        self.entity = nil
    end

    -- 清理状态机
    if self.stateMachine then
        self.stateMachine:Dispose()
        self.stateMachine = nil
    end

    -- 清理领主引用，避免内存泄漏
    self.followLordComp = nil
    self.isFollowing = false

    -- 清理Show动画状态
    self.isPlayingShowAnimation = false

    -- 清理气泡组件
    if self.bubbleComponent then
        self.bubbleComponent:ClearData()
        self.bubbleComponent = nil
        GWG.GWAdmin.SwitchUtility.HomeLog("流浪狗气泡组件已清理")
    end

    -- 清理路径特效
    self:HidePathEffect()

    -- 清理其他引用
    self.transform = nil
    self.currentPath = nil
    self.manualTarget = nil
    self.pathEffectId = nil
    self.pathEffectData = nil

    -- 调用基类回收
    gw_home_comp_base_class.Recycle(self)
end

-- 显示路径特效
function GWHomeCompStrayDog:ShowPathEffect(path)
    if not path or #path == 0 then
        return
    end

    -- 清理之前的特效
    self:HidePathEffect()

    local gw_home_effect_mgr = require "gw_home_effect_mgr"
    local pathEffectConfig = gw_home_config_mgr.GetPathEffectConfig()

    self.pathEffectId = {}
    self.pathEffectData = {}

    local currentDistance = 0
    local effectCount = 0

    -- 遍历路径点，创建虚线特效（使用与领主相同的逻辑）
    for i = 1, #path - 1 do
        local currentPoint = path[i]
        local nextPoint = path[i + 1]

        -- 转换为世界坐标进行距离计算（更精确）
        local currentWorldX, currentWorldY, currentWorldZ = gw_home_grid_data.GetPosByGridXY(currentPoint.x, currentPoint.y, true)
        local nextWorldX, nextWorldY, nextWorldZ = gw_home_grid_data.GetPosByGridXY(nextPoint.x, nextPoint.y, true)

        -- 计算世界坐标下的距离
        local segmentDistance = math.sqrt((nextWorldX - currentWorldX)^2 + (nextWorldZ - currentWorldZ)^2)

        -- 在当前段上按间隔创建特效
        local numEffects = math.max(1, math.floor(segmentDistance / pathEffectConfig.interval))

        for j = 1, numEffects do
            local t = j / (numEffects + 1) -- 在段内的插值位置

            -- 直接在世界坐标下插值（避免网格坐标转换误差）
            local worldX = currentWorldX + (nextWorldX - currentWorldX) * t
            local worldY = currentWorldY + (nextWorldY - currentWorldY) * t
            local worldZ = currentWorldZ + (nextWorldZ - currentWorldZ) * t

            -- 创建虚线特效
            local effectId = gw_home_effect_mgr.CreateEffects(
                GWG.GWConst.HomeEffectType.LordPathLine,
                nil,
                {x = worldX, y = worldY, z = worldZ}
            )

            if effectId then
                table.insert(self.pathEffectId, effectId)
                table.insert(self.pathEffectData, {
                    id = effectId,
                    position = {x = worldX, y = worldY, z = worldZ},
                    distance = currentDistance + segmentDistance * t,
                    index = effectCount
                })
                effectCount = effectCount + 1

                GWG.GWAdmin.SwitchUtility.HomeLog("流浪狗特效创建成功，ID:"..effectId.." 位置:"..worldX..","..worldY..","..worldZ.." 距离:"..currentDistance + segmentDistance * t)
            end
        end

        currentDistance = currentDistance + segmentDistance
    end

    -- 在目标点创建目标特效
    local targetPoint = path[#path]
    local targetWorldX, targetWorldY, targetWorldZ = gw_home_grid_data.GetPosByGridXY(targetPoint.x, targetPoint.y, true)

    self.targetEffectId = gw_home_effect_mgr.CreateEffects(
        GWG.GWConst.HomeEffectType.LordPathTarget,
        nil,
        {x = targetWorldX, y = targetWorldY, z = targetWorldZ}
    )

    GWG.GWAdmin.SwitchUtility.HomeLog("流浪狗路径特效创建完成，路径特效数量:"..effectCount.." 目标特效ID:"..tostring(self.targetEffectId))
end

-- 动态销毁已走过的路径特效
function GWHomeCompStrayDog:UpdatePathEffectDestroy()
    if not self.pathEffectData or not self.currentPath or not self.transform then
        return
    end

    -- 获取流浪狗当前位置
    local dogPos = self.transform.position

    -- 简化算法：直接计算流浪狗当前位置到路径起点的距离
    local startPoint = nil
    if self.currentPath and #self.currentPath > 0 then
        startPoint = self.currentPath[1]
    end

    if not startPoint then
        return
    end

    -- 将网格坐标转换为世界坐标
    local startWorldX, startWorldY, startWorldZ = gw_home_grid_data.GetPosByGridXY(startPoint.x, startPoint.y, true)

    -- 计算流浪狗当前位置到路径起点的直线距离
    local currentDistance = math.sqrt((dogPos.x - startWorldX)^2 + (dogPos.z - startWorldZ)^2)

    -- 销毁已走过的特效（添加一点缓冲距离，避免特效在流浪狗脚下消失）
    local destroyDistance = currentDistance - 0.5  -- 0.5单位的缓冲距离（比领主小一点）
    local gw_home_effect_mgr = require "gw_home_effect_mgr"
    local destroyedCount = 0

    -- 从后往前遍历，安全删除
    for i = #self.pathEffectData, 1, -1 do
        local effectData = self.pathEffectData[i]
        if effectData and effectData.distance <= destroyDistance then
            -- 销毁特效
            gw_home_effect_mgr.RemoveEffect(effectData.id)

            -- 从pathEffectId中移除
            if self.pathEffectId then
                for j = #self.pathEffectId, 1, -1 do
                    if self.pathEffectId[j] == effectData.id then
                        table.remove(self.pathEffectId, j)
                        break
                    end
                end
            end

            -- 从pathEffectData中移除
            table.remove(self.pathEffectData, i)
            destroyedCount = destroyedCount + 1
        end
    end

    if destroyedCount > 0 then
        GWG.GWAdmin.SwitchUtility.HomeLog("流浪狗动态销毁路径特效: "..destroyedCount.."个, 已走过距离: "..currentDistance)
    end
end

-- 隐藏路径特效
function GWHomeCompStrayDog:HidePathEffect()
    -- 清理路径虚线特效
    if self.pathEffectId then
        local gw_home_effect_mgr = require "gw_home_effect_mgr"

        if type(self.pathEffectId) == "table" then
            for _, effectId in ipairs(self.pathEffectId) do
                gw_home_effect_mgr.RemoveEffect(effectId)
            end
        else
            gw_home_effect_mgr.RemoveEffect(self.pathEffectId)
        end

        self.pathEffectId = nil
    end

    -- 清理路径特效数据
    if self.pathEffectData then
        self.pathEffectData = nil
    end

    -- 清理目标点特效
    if self.targetEffectId then
        local gw_home_effect_mgr = require "gw_home_effect_mgr"
        gw_home_effect_mgr.RemoveEffect(self.targetEffectId)
        self.targetEffectId = nil
    end
end

-- 清理路径中重复点/零步长点，避免0时长tween导致卡住
function GWHomeCompStrayDog:CleanPath(path)
    if not path or #path < 2 then return path end
    local cleaned = {}
    table.insert(cleaned, path[1])
    for i = 2, #path do
        local prev = cleaned[#cleaned]
        local cur = path[i]
        if not (prev.x == cur.x and prev.y == cur.y) then
            table.insert(cleaned, cur)
        end
    end
    return cleaned
end

-- 打乱点击动画播放列表
function GWHomeCompStrayDog:ShuffleClickAnimations()
    -- 复制原始动画列表
    self.clickAnimationPlaylist = {}
    for i, anim in ipairs(ClickAnimations) do
        self.clickAnimationPlaylist[i] = anim
    end
    
    -- Fisher-Yates洗牌算法
    for i = #self.clickAnimationPlaylist, 2, -1 do
        local j = math.random(i)
        self.clickAnimationPlaylist[i], self.clickAnimationPlaylist[j] = 
            self.clickAnimationPlaylist[j], self.clickAnimationPlaylist[i]
    end
    
    self.clickAnimationIndex = 1
    GWG.GWAdmin.SwitchUtility.HomeLog("流浪狗动画播放列表已打乱，顺序: " .. table.concat(self.clickAnimationPlaylist, ", "))
end

-- 获取下一个点击动画
function GWHomeCompStrayDog:GetNextClickAnimation()
    if self.clickAnimationIndex > #self.clickAnimationPlaylist then
        -- 当前循环结束，重新打乱并开始新循环
        self:ShuffleClickAnimations()
    end

    local animation = self.clickAnimationPlaylist[self.clickAnimationIndex]
    self.clickAnimationIndex = self.clickAnimationIndex + 1

    return animation
end

-- 根据动画名称计算播放时间
function GWHomeCompStrayDog:GetAnimationDuration(animationName)
    local frameCount = AnimationFrameConfig[animationName]
    if frameCount then
        -- 根据帧数和帧率计算时间（秒）
        local duration = frameCount / AnimationFrameRate
        GWG.GWAdmin.SwitchUtility.HomeLog("流浪狗动画 " .. animationName .. " 帧数: " .. frameCount .. ", 播放时间: " .. duration .. "秒")
        return duration
    else
        -- 如果没有配置，使用默认时间1秒
        GWG.GWAdmin.SwitchUtility.HomeLog("流浪狗动画 " .. animationName .. " 未找到帧数配置，使用默认时间1秒")
        return 1.0
    end
end

-- 播放点击动画对应的音效
function GWHomeCompStrayDog:PlayClickAnimationSound(animationName)
    -- 获取流浪狗音效配置
    local strayDogConfig = gw_home_config_mgr.GetStrayDogConfig()
    if not strayDogConfig or not strayDogConfig.soundIds then
        GWG.GWAdmin.SwitchUtility.HomeLog("流浪狗音效配置不存在")
        return
    end

    -- 根据动画名称获取对应的音效ID
    local soundId = nil
    if animationName == "Skill01" then
        soundId = strayDogConfig.soundIds[1] -- 汪汪叫
    elseif animationName == "Skill02" then
        soundId = strayDogConfig.soundIds[2] -- 伸懒腰
    elseif animationName == "Skill03" then
        soundId = strayDogConfig.soundIds[3] -- 玩耍
    end

    if soundId then
        -- 播放音效
        local music_contorller = require "music_contorller"
        music_contorller.PlayFxAudio(soundId)
        GWG.GWAdmin.SwitchUtility.HomeLog("流浪狗播放音效: " .. animationName .. " -> " .. soundId)
    else
        GWG.GWAdmin.SwitchUtility.HomeLog("流浪狗动画 " .. animationName .. " 没有对应的音效配置")
    end
end

return GWHomeCompStrayDog
