local require = require
local pairs = pairs
local ipairs = ipairs
local table = table
local typeof = typeof

local laymain_data = require "laymain_data"
local event = require "event"
local class = require "class"
local ui_base = require "ui_base"
local windowMgr = require "ui_window_mgr"
local hero_item = require "hero_item_new"
local player_mgr = require "player_mgr"
local game_scheme = require "game_scheme"
local face_item = require "face_item_new"
local Image = CS.UnityEngine.UI.Image
local Text = CS.UnityEngine.UI.Text
local tonumber = tonumber
local string = string
local debug = debug
local xpcall = xpcall
local gw_hero_data = require "gw_hero_data"
local Toggle 		= typeof(CS.UnityEngine.UI.Toggle)
local ScrollRectTable    = CS.UI.UGUIExtend.ScrollRectTable
local ScrollRect    = CS.UnityEngine.UI.ScrollRect

local lang = require "lang"
local flow_text = require "flow_text"
local GWG = GWG
local PlayerPrefs = CS.UnityEngine.PlayerPrefs
local containerTrans = GWMgr.HudContainer
local UIUtil = UIUtil
local util   = require "util"
local math = math
local tiny_mgr = require "minigame_mgr"
local log = log
local tostring = tostring
local UnityScreen = CS.UnityEngine.Screen
local gw_power_mgr = require "gw_power_mgr"
local UnityAxis = CS.UnityEngine.RectTransform.Axis
local gw_hero_define = require "gw_hero_define"
local event_activity_define = require "event_activity_define"
local isEditor = CS.UnityEngine.Application.isEditor

module("ui_mini_select_hero")

local window = nil
local SelectHeroWindow = {}

--可选英雄集
local canSelectHeros = nil
local HeroData = {}
preSlot = nil
local TabData = {}

--- 首充活动试玩英雄ID
local CampaignFR_TrialHeroID = 104
local CampaignFR_PowerFileWidth = 384

local heroCfgCache = {}
local SAVE_DATA = "MINI_SELECT_SAVE_DATA"
local SAVE_DATA_WITHTAG = "MINI_SELECT_SAVE_DATA_%s"
SelectHeroWindow.widget_table =
{
    closeBtn = {path = "closeButton", type = "Button"},
    fightBtn = {path = "grid_btn/fightButton", type = "Button"},
    quickFightBtn = {path = "grid_btn/quickFightBtn", type = "Button"},
    scroll_table = {path = "heroSelect/Scroll View/Viewport/Content", type = ScrollRectTable},
    scroll_table_root = {path = "heroSelect/Scroll View/Viewport/Content", type = "RectTransform"},
    scroll_rect = {path = "heroSelect/Scroll View", type = ScrollRect},
    player_root = {path = "TopPart/PowerPart/Left/PlayerIcon", type = "RectTransform"},
    enemy_avatar_icon = {path = "TopPart/PowerPart/Right/EnemyIcon/baseLayer/Image/icon", type = Image},
    level_num_txt = {path = "TopPart/PowerPart/Middle/Level/LevelNum", type = "Text"},
    player_power_txt = {path = "TopPart/PowerPart/Middle/PlayerPower/powerTxt", type = "Text"},
    enemy_power_txt = {path = "TopPart/PowerPart/Middle/EnemyPower/powerTxt", type = "Text"},
    
    campTog_1 = {path = "campTopList/All", type = Toggle},
    campTog_2 = {path = "campTopList/zhanshiCZ", type = Toggle},
    campTog_3 = {path = "campTopList/fashiCZ", type = Toggle},
    campTog_4 = {path = "campTopList/youxiaCZ", type = Toggle},
    campTog_5 = {path = "campTopList/cikeCZ", type = Toggle},
    campTog_6 = {path = "campTopList/fuzhuCZ", type = Toggle},

    typeTogRoot = {path = "TypeTogList", type = "Image"},
    typeTog_1 = {path = "TypeTogList/Tog1", type = Toggle},
    typeTog_1Img = {path = "TypeTogList/Tog1", type = "Image"},
    typeTog_2 = {path = "TypeTogList/Tog2", type = Toggle},
    typeTog_3 = {path = "TypeTogList/Tog3", type = Toggle},
    typeTog_4 = {path = "TypeTogList/Tog4", type = Toggle},
    typeTog_5 = {path = "TypeTogList/Tog5", type = Toggle},
    typeTog_6 = {path = "TypeTogList/Tog6", type = Toggle},
    typeTog_7 = {path = "TypeTogList/Tog7", type = Toggle},

    guideWin = {path = "GuideWin", type = "RectTransform"},
    HaloAndWeapon = {path = "HaloAndWeapon", type = "RectTransform"},
    weaponButton = {path = "HaloAndWeapon/weaponButton", type = "Button"},
    --weaponEffect = {path = "HaloAndWeapon/weaponButton/UI_lianyiguangxiao", type = SortingGroup},
    weaponIcon = {path = "HaloAndWeapon/weaponButton/mask/Icon", type = "Image"},
    weaponMask = {path = "HaloAndWeapon/weaponButton/mask", type = "Image"},
    weaponLevel = {path = "HaloAndWeapon/weaponButton/Level",type = "Text"},

    --- 首充活动
    Campaign_FirstRechargeRoot = { path = "Campaign_FirstRecharge", type = "RectTransform" },
    Campaign_FirstRechargeMask = { path = "Campaign_FirstRecharge/WaitMask", type = "RectTransform" },
    Campaign_FirstRecharge_Tip = { path = "Campaign_FirstRecharge/TipsBG", type = "RectTransform" },
    Campaign_FirstRecharge_TrialLabel = { path = "Campaign_FirstRecharge/TrialLabel", type = "RectTransform" },
    Campaign_FirstRecharge_GetBtn = { path = "Campaign_FirstRecharge/TrialGetBtn", type = "Button", event_name = "Campaign_GetTrailHeroClick" },
    Campaign_FirstRecharge_GetBtnSC = { path = "Campaign_FirstRecharge/TrialGetBtn2", type = "Button", event_name = "Campaign_GetTrailHeroClick" },
    Campaign_FirstRecharge_PowerInfo = { path = "heroSelect/Campaign_FirstRecharge_PowerInfo", type = "RectTransform" },
    Campaign_FirstRecharge_PowerFill = { path = "heroSelect/Campaign_FirstRecharge_PowerInfo/FillBg/FillImg", type = "RectTransform" },
    Campaign_FirstRecharge_PowerText = { path = "heroSelect/Campaign_FirstRecharge_PowerInfo/FillBg/PowerText", type = "Text" },
}

local CSelectHeroWindow = class(ui_base, nil, SelectHeroWindow)

function SelectHeroWindow:InitWindowData()
    --英雄赛选
    self.quality = nil
    self.toggleGroup = {
        self.typeTog_1,
        self.typeTog_2,
        self.typeTog_3,
        self.typeTog_4,
        self.typeTog_5,
        self.typeTog_6,
        self.typeTog_7,
    }
    --阵营筛选
    self.campIndex = nil
    self.campToggleGroup = {
        self.campTog_1,
        self.campTog_2,
        self.campTog_3,
        self.campTog_4,
        self.campTog_5,
        self.campTog_6,
    }
    
    self._cache_profile = {}
end


--设置头像
function SelectHeroWindow:SetPlayerFaceData()
    local data = {}
    --适配faceID 2025.4.2 将faceID 转换为 faceStr
    local custom_avatar_data = require "custom_avatar_data"
    local customHeadData = custom_avatar_data.GetMyAvatar()
    local faceStr = player_mgr.GetRoleFaceID() 
    if customHeadData then 
        faceStr = customHeadData.remoteUrl
    end
    data.faceID = faceStr
    data.frameID = player_mgr.GetPlayerFrameID()
    self.faceShow = self.faceShow or face_item.CFaceItem()
    self.faceShow:Init(self.player_root, nil, 1)
    self.faceShow:SetFaceInfo(data.faceID, nil)
    self.faceShow:SetFrameID(data.frameID, true)
end

function SelectHeroWindow:SetLevelView()
    local data = 
    {
        enemy_avatar_icon = self.enemy_avatar_icon,
        level_num_txt = self.level_num_txt,
        player_power_txt = self.player_power_txt,
        enemy_power_txt = self.enemy_power_txt
    }
    event.Trigger(event.MINI_SOLIDER_LEVEL,data)
end

function SelectHeroWindow:InitHeroWin()
    --英雄列表
    self.scroll_table.onItemRender=onItemRenderBottom
    self.scroll_table.onItemDispose=function(scroll_rect_item,index)
        if scroll_rect_item and scroll_rect_item.data and scroll_rect_item.data[3] then
            scroll_rect_item.data[3]:Dispose()
            scroll_rect_item.data[3] = nil
        end
    end
    self:SetFilterHero(nil,nil,nil)
    self:UpdateFingerGuide()

    if self.campaignFRFlag then
        self.Campaign_FirstRecharge_TrialLabel.gameObject:SetActive(false)
        self.Campaign_FirstRecharge_GetBtn.gameObject:SetActive(false)
        self.Campaign_FirstRecharge_GetBtnSC.gameObject:SetActive(false)
        if self.levelSrc and not self.hasCampaignFRTrialHero then
            local rootSize = self.Campaign_FirstRechargeRoot.rect.size
            -- 显示试用标记
            if self.campaignFRUseTrail then
                local tmpPos = self.levelSrc:GetSlotScreenPos(4, { x = 0.3, y = 1.6, z = 0 })
                if tmpPos then
                    tmpPos = { x = tmpPos.x, y = tmpPos.y, z = 0 }
                    tmpPos.x = (tmpPos.x / UnityScreen.width - 0.5) * rootSize.x
                    tmpPos.y = (tmpPos.y / UnityScreen.height - 0.5) * rootSize.y
                    self.Campaign_FirstRecharge_TrialLabel.gameObject:SetActive(true)
                    self.Campaign_FirstRecharge_TrialLabel.localPosition = tmpPos
                end
            else
                -- 显示获取按钮
                local tmpPos = self.levelSrc:GetSlotScreenPos(4, { x = 0, y = 1, z = 0 })
                if tmpPos then
                    tmpPos = { x = tmpPos.x, y = tmpPos.y, z = 0 }
                    tmpPos.x = (tmpPos.x / UnityScreen.width - 0.5) * rootSize.x
                    tmpPos.y = (tmpPos.y / UnityScreen.height - 0.5) * rootSize.y - 40
                    self.Campaign_FirstRecharge_GetBtn.gameObject:SetActive(true)
                    self.Campaign_FirstRecharge_GetBtn.transform.localPosition = tmpPos
                end
            end
        end
    end
end

---触发手指引导 (一键布阵)
function SelectHeroWindow:TriggerGuideOneFormation()
    if not self.GuideLevelPassId then
        local cfg = game_scheme:InitBattleProp_0(8368)
        if not cfg then
            return
        end
        self.GuideLevelPassId = cfg.szParam.data[0]
    end
    if laymain_data.GetPassLevel() > self.GuideLevelPassId then
        return
    end
    if self.guideTimer then
        util.RemoveDelayCall(self.guideTimer)
        self.guideTimer = nil
    end
    self.guideTimer = util.DelayCallOnce(0.5,function()
        if not tiny_mgr.IsQuickFightFunc() or self.campaignFRFlag then
            return
        end
        if util.IsObjNull(self.guideWin) then
            return
        end
        if util.IsNullOrEmpty(self.quickFightBtn) then
            return
        end
        if self._up_num and self._up_num < self.UpNumMax then
            return
        end
        if self._up_num >= self.hero_size then
            self.guideWin.gameObject:SetActive(false)
            return
        end
        local isGuide = false
        --根据 选中的英雄判断
--[[        for i = 1, self.hero_size do
            if i > 5 then
                break
            end
            if not util.IsNullOrEmpty(self.scroll_table) then
                local res, scroll_rect_item = xpcall(function()
                    return self.scroll_table:GetItem(i - 1)
                end, debug.traceback)
                if scroll_rect_item and scroll_rect_item.data then
                    if scroll_rect_item.data[2] and not self:IsSelected(scroll_rect_item.data[2]) then
                        isGuide = true
                        break ;
                    end
                end
            end
        end]]
        local quickFightList = self:GetQuickFightHeroList()
        for i = 1, self.hero_size do
            if not util.IsNullOrEmpty(self.scroll_table) then
                local res, scroll_rect_item = xpcall(function()
                    return self.scroll_table:GetItem(i - 1)
                end, debug.traceback)
                if scroll_rect_item and scroll_rect_item.data then
                    if scroll_rect_item.data[2] and self:IsSelected(scroll_rect_item.data[2]) then
                        local isExit = true
                        for j = 1, #quickFightList do
                            if quickFightList[j] and scroll_rect_item.data[2].heroID == quickFightList[j].heroID then
                                isExit = false
                                break
                            end
                        end
                        if isExit then
                            isGuide = true
                            break
                        end
                    end
                end
            end
        end
        if not isGuide then
            self.guideWin.gameObject:SetActive(false)
            return
        end
        self.guideWin.gameObject:SetActive(true)
        self.guideWin.gameObject.transform.position = self.quickFightBtn.transform.position
    end)
end

--手指引导
function SelectHeroWindow:UpdateFingerGuide()
    -- 活动模式，不显示手指引导
    if self.scroll_table_root.localPosition.y>100 or self.campaignFRFlag then
        self.guideWin.gameObject:SetActive(false)
        return
    end
    if self._up_num and self._up_num<self.UpNumMax then
        if self._up_num>=self.hero_size  then
            self.guideWin.gameObject:SetActive(false)
        else

            local temp={}
            for i = 1, self.hero_size do
                if  not util.IsNullOrEmpty(self.scroll_table) then
                    local res,scroll_rect_item = xpcall(function()
                        return self.scroll_table:GetItem(i-1)
                    end,debug.traceback)
                    if scroll_rect_item and scroll_rect_item.data then
                        if scroll_rect_item.data[2] and not self:IsSelected(scroll_rect_item.data[2])  then
                            table.insert(temp,scroll_rect_item)
                        end
                    end
                end
                
            end
            if #temp>0 then
                self.guideWin.gameObject:SetActive(true)
                local scroll_rect_item=temp[1]
                self.guideWin.gameObject.transform.position=scroll_rect_item.transform.position
            end

        end

    else
        if self._up_num and self._up_num>=self.UpNumMax then
            self.guideWin.gameObject:SetActive(false)
            self:TriggerGuideOneFormation()
            return
        end
        local scroll_rect_item=self.scroll_table:GetItem(0)
        if scroll_rect_item then
            self.guideWin.gameObject.transform.position=scroll_rect_item.transform.position
            self.guideWin.gameObject:SetActive(true)
        end
    end
end
function SelectHeroWindow:Init()
    local card_sprite_asset = require "card_sprite_asset"
    self.spriteAsset = card_sprite_asset.CreateSpriteAsset()
    self:InitWindowData()
    self:SubscribeEvents()
    self:LoadUpHeroData()
    self:SetPlayerFaceData()
    self:SetLevelView()
    self:SetWeaponIcon()
    --containerTrans:SetActive(false)
end

function SelectHeroWindow:LoadUpHeroData()
    local save_data = player_mgr.GetPlayerRoleID() .. 
        (self.saveTag ~= nil and string.format(SAVE_DATA_WITHTAG, self.saveTag) or SAVE_DATA)
    local save_data_str =  PlayerPrefs.GetString(save_data,"")
    --"1-127#3-247#4-128"
    local save_data_str_array =  string.split(save_data_str,"#")
    self._save_datas = {}
    if save_data_str_array then
        for i=1,#save_data_str_array do
            local save_data_str_item =   string.split(save_data_str_array[i],"-")
            if save_data_str_item then
                local slotIndex = tonumber(save_data_str_item[1])
                local heroID = tonumber(save_data_str_item[2])
                self._save_datas[heroID] = slotIndex
            end
        end
    end
    -- 把试用英雄放到4号位
    if self.campaignFRFlag then
        self._save_datas[CampaignFR_TrialHeroID] = 4
    end
end
function SelectHeroWindow:SetWeaponIcon()
    local droneId = GWG.GWHomeMgr.droneData.GetDroneId()
    if not droneId then
        self.weaponButton.gameObject:SetActive(false)
        return
    end
    self.weaponButton.gameObject:SetActive(true)
    self.weaponLevel.text = "Lv."..GWG.GWHomeMgr.droneData.OnGetDroneLv()
    local droneSkin = GWG.GWHomeMgr.droneData.GetCurAdroneSkin()
    local cfg = game_scheme:AccessoryAdorn_0(droneSkin)
    local icon = cfg and cfg.icon or game_scheme:InitBattleProp_0(8131).szParam.data[1]
    self.spriteAsset:GetSprite(tonumber(icon),function(sprite)
        self.weaponIcon.sprite = sprite
    end)
end



function SelectHeroWindow:SaveUpHeroData()
    local str = ""
    local record_tables = {}
    if not self._save_datas then
        return
    end
    for k,v in pairs(self._save_datas) do
        if v then
            if not record_tables[v] then
                str = str ..v.."-"..k.. "#"
                record_tables[v] = k 
            end
        end
    end
    local save_data = player_mgr.GetPlayerRoleID() .. 
        (self.saveTag ~= nil and string.format(SAVE_DATA_WITHTAG, self.saveTag) or SAVE_DATA)
    PlayerPrefs.SetString(save_data,str)
    PlayerPrefs.Save()
end

--- 首充活动，战斗开始时校验
function SelectHeroWindow:CampaignFR_FightCheck()
    -- 不提供试用英雄时，如果场上只有试用英雄，则不算上阵
    if not self.campaignFRUseTrail and not self.hasCampaignFRTrialHero then
        local heroCount = 0
        for k, v in pairs(self._save_datas) do
            if k ~= CampaignFR_TrialHeroID then
                heroCount = heroCount + 1
            end
        end
        if heroCount < 1 then
            flow_text.Add(lang.Get(104774))
            return
        end
    end
    -- 要校验战力
    if self.levelSrc then
        local curLvID, heroIds, allPower, limitPower = self.levelSrc:GetSlotsHeroAndPower()
        -- 先本地判断
        if allPower < limitPower then
            flow_text.Add(lang.Get(1008220))
            return
        end
        self.campaignFR_WaitCheck = true
        self.Campaign_FirstRechargeMask.gameObject:SetActive(true)
        local skipFlag = false
        -- 跳过校验，给策划配关用
        if isEditor then
            local tmpValue = PlayerPrefs.GetInt("SoldierSSortieCampaign_Skip_CheckPower", 0)
            if tmpValue == 1 then
                skipFlag = true
                self.CampaignFR_StartGame(event_activity_define.MSG_XYX_ENDLESS_SET_HERO_POWER_RSP, {errorcode = 0})
            end
        end
        if not skipFlag then
            -- 服务器校验战斗力
            local hero_first_charge_mgr = require "hero_first_charge_mgr"
            hero_first_charge_mgr.HeroPowerCheckReq(heroIds, curLvID)
        end

    end
end

function SelectHeroWindow:SubscribeEvents()

    self.onClose = function()
        -- 主动退出打点=失败
        tiny_mgr.EventReport_Fail(nil, nil, 2)
        tiny_mgr.EventReport_Fail_Hook(2)
        local common_new_pb = require "common_new_pb"
        local gw_zombie_treasure_data = require "gw_zombie_treasure_data"
        local res = gw_zombie_treasure_data.GetStarsShow()
        event.Trigger(event.BATTLEFINAL,common_new_pb.GameGoal,res)
        tiny_mgr.MiniGameClose()
        windowMgr:UnloadModule("ui_mini_select_hero")
        --GWG.GWMgr.ShowCurScene()

        --event.Trigger(event.SOLDIER_CLOSE)
    end
    self.closeBtn.onClick:AddListener(self.onClose)
    
    self.onFight = function()
        if not self._up_num or self._up_num<=0 then
            flow_text.Add(lang.Get(104774))
            return
        end
        if self.campaignFRFlag then
            self:CampaignFR_FightCheck()
        else
            event.Trigger(event.SOLDIER_FIGHT)    
            windowMgr:UnloadModule("ui_mini_select_hero")
        end
    end
    self.fightBtn.onClick:AddListener(self.onFight)
    
    self.onQuickFight = function()
        self:OnQuickFightEvent()
    end
    self.quickFightBtn.onClick:AddListener(self.onQuickFight)
    
    for i,toggle in ipairs(self.campToggleGroup) do
        self["campToggleValueChanged"..i] = function(isOn)
            if not isOn then
                return
            end
            if window and window:IsValid() then
                window:SetFilterHero(nil ,nil,i-1)
            end
        end
        toggle.onValueChanged:AddListener(self["campToggleValueChanged"..i])
    end
    
    for i, toggle in ipairs(self.toggleGroup) do
        self["toggleValueChanged"..i] = function(isOn)
            if not isOn then
                return
            end
            if window and window:IsValid() then
                window:SetFilterHero(i-1 ,i ~= 0,self.campIndex)
            end
        end
        toggle.onValueChanged:AddListener(self["toggleValueChanged"..i])
    end

    self.onRemoveHero = function(_,hero)
        if self._pause_interact then
            return
        end
        
        if  self._save_datas[hero.heroID] then
            hero.isUp = 0
            if self._cache_profile[hero.heroID] then
                self._cache_profile[hero.heroID]:SelectHero(false)
            end
            event.Trigger(event.SELECTED_HERO,hero,self._save_datas,{player_power_txt = self.player_power_txt})
            self._up_num = self._up_num - 1
            self._up_num = self._up_num < 0 and 0 or self._up_num
            if window then 
                window:UpdateFingerGuide()
            end
        end
    end
    event.Register(event.SLOT_REMOVE_HERO,self.onRemoveHero)

    self.OnWeaponButton = function()
        windowMgr:ShowModule("ui_new_magic_weapon_info_tips")
        local battle_player = require("battle_player")
        if battle_player then
            battle_player.ShowSceneLight(true)
        end

    end
    self.weaponButton.onClick:AddListener(self.OnWeaponButton)
    self.OnValueChangedScroll_table = function()
        self:UpdateFingerGuide()
    end

  self.scroll_rect.onValueChanged:AddListener(self.OnValueChangedScroll_table)

    self.Campaign_GetTrailHeroClick = function()
        if self.campaignFRUseTrail or self.hasCampaignFRTrialHero then
            return
        end
        -- 先关闭引导手指
        self.guideWin.gameObject:SetActive(false)
        --首冲购买界面
        local gw_firstrecharge_mgr = require "gw_firstrecharge_mgr"
        gw_firstrecharge_mgr.FirstRechargeOpenView(function()
            tiny_mgr.OnCloseLight()
        end, function()
            tiny_mgr:OnOpenLight()
            -- 检查是否拥有了试用英雄
            local buyFlag = self.levelSrc and self.levelSrc:CheckIsOwnedByHeroID(CampaignFR_TrialHeroID)
            -- 已购买 and 上一次显示时还没有此英雄
            if buyFlag and not self.hasCampaignFRTrialHero then
                self.Campaign_FirstRecharge_GetBtn.gameObject:SetActive(false)
                self:RefreshHeroSlot()
                self:RefreshTrailHeroState()
            end
        end)
        local ui_first_recharge = windowMgr:IsModuleExist("ui_first_recharge")
        if ui_first_recharge then
            ui_first_recharge:SetInputParam(true)
        end        
    end

    -- 英雄升级面板关闭，要刷新战力显示
    self.updateHeroData = function()
        self:RefreshHeroSlot()
        if self.campaignFRFlag and self.hasCampaignFRTrialHero then
            self:RefreshTrailHeroState()
        end
    end
    event.Register(gw_hero_define.UPDATE_HERO_PACKAGE_DATA, self.updateHeroData)

    if self.campaignFRFlag then
        self.CampaignFR_StartGame = function(eventName, msg)
            -- 防止多次回调
            if not self.campaignFR_WaitCheck then
                return
            end
            self.Campaign_FirstRechargeMask.gameObject:SetActive(false)
            self.campaignFR_WaitCheck = false
            -- 校验通过，开始游戏
            if msg.errorcode == 0 then
                event.Trigger(event.SOLDIER_FIGHT)
                windowMgr:UnloadModule("ui_mini_select_hero")
            else
                flow_text.Add(lang.Get(1008220))
            end
        end
        event.Register(event_activity_define.MSG_XYX_ENDLESS_SET_HERO_POWER_RSP, self.CampaignFR_StartGame)
    end
end

function SelectHeroWindow:UnsubscribeEvents()
    self.scroll_rect.onValueChanged:RemoveAllListeners()
    self.closeBtn.onClick:RemoveAllListeners()
    self.weaponButton.onClick:RemoveAllListeners()
    self.fightBtn.onClick:RemoveAllListeners()
    self.quickFightBtn.onClick:RemoveAllListeners()
    for i,toggle in ipairs(self.campToggleGroup) do
        toggle.onValueChanged:RemoveAllListeners()
    end

    for i, toggle in ipairs(self.toggleGroup) do
        toggle.onValueChanged:RemoveAllListeners()
    end
    event.Unregister(event.SLOT_REMOVE_HERO,self.onRemoveHero)
    event.Unregister(gw_hero_define.UPDATE_HERO_PACKAGE_DATA, self.updateHeroData)
    if self.campaignFRFlag and self.CampaignFR_StartGame ~= nil then
        event.Unregister(event_activity_define.MSG_XYX_ENDLESS_SET_HERO_POWER_RSP, self.CampaignFR_StartGame)
        self.CampaignFR_StartGame = nil
    end
end

function SelectHeroWindow:SetFilterHero(quality,active,campIndex)
    if self.campaignFRFlag then
        self.Campaign_FirstRecharge_GetBtnSC.transform:SetParent(self.Campaign_FirstRechargeRoot.transform)
        self.Campaign_FirstRecharge_GetBtnSC.gameObject:SetActive(false)
    end

    quality = quality or 0
    if quality  then
        self.quality = active and  quality or nil
    end
    
    campIndex = campIndex or 0
    if campIndex then
        self.campIndex = campIndex
    end
    local temp = {}
    HeroData = self:GetUnselectHerodata()
    for i,v in ipairs(HeroData) do
        table.insert(temp,v)

        if self._save_datas then
            local slotIndex =  window._save_datas[v.heroID]
            if self.campaignFRFlag then
                if slotIndex and (slotIndex == 3 or slotIndex == 5) then
                    v.isUp = 0
                    self._save_datas[v.heroID] = nil
                    slotIndex = nil
                    if self._cache_profile[v.heroID] then
                        self._cache_profile[v.heroID]:SelectHero(false)
                    end
                end
            end
            if slotIndex and not self:IsSelected(v)  then
                v.slotIndex = slotIndex
                v.isUp = 1
                self._up_num = self._up_num or 0
                self._up_num = self._up_num + 1
                self._up_num = self._up_num > self.UpNumMax and self.UpNumMax or self._up_num
                event.Trigger(event.SELECTED_HERO,v,self._save_datas,{player_power_txt = self.player_power_txt})
            end
        end
    end

    table.sort(temp,function(data1,data2)
        -- 把试用英雄放到第一个
        if self.campaignFRFlag then
            if data1.heroID == CampaignFR_TrialHeroID then
                return true
            elseif data2.heroID == CampaignFR_TrialHeroID then
                return false
            end
        end

        if data1.battleProp.power ~= data2.battleProp.power then
            return data1.battleProp.power > data2.battleProp.power
        end
        
        local hero1Cfg = GetHeroInCache(data1.heroID)  
        local hero2Cfg = GetHeroInCache(data2.heroID)
        if hero1Cfg.profession~=hero2Cfg.profession then
            return hero1Cfg.profession < hero2Cfg.profession
        end
        
        return data1.heroID > data2.heroID
    end)
    self.hero_size=#temp
    self.scroll_table.data= temp
    self.scroll_table:Refresh(-1,-1)
end

function SelectHeroWindow:IsSelected(hero)
    return hero.isUp == 1
end

function SelectHeroWindow:OnSelectHero(dataItem,guideTrigger)
    if dataItem.isUp == 1 then
        if self._up_num and self._up_num >= self.UpNumMax then
            flow_text.Add(lang.GetFormat(7085,self.UpNumMax))
            dataItem.isUp = 0
            return
        end
        self._up_num = self._up_num or 0
        self._up_num = self._up_num + 1
        self._up_num = self._up_num > self.UpNumMax and self.UpNumMax or self._up_num
    else
        if self._up_num then
            self._up_num = self._up_num - 1
            self._up_num = self._up_num < 0 and 0 or self._up_num
        end
    end
    dataItem.slotIndex = nil
    event.Trigger(event.SELECTED_HERO,dataItem,self._save_datas,{player_power_txt = self.player_power_txt})
    
    local profile =  self._cache_profile[dataItem.heroID]
    if profile then
        if  self:IsSelected(dataItem) then
            profile:SelectHero(true)
        else
            profile:SelectHero(false)
        end
    end
    if guideTrigger then
        self:UpdateFingerGuide()
    end
end

function onItemRenderBottom(scroll_rect_item,index,dataItem)
    if not window then return end
    local _self = window

    --local battleType = unforced_guide_mgr.GetBattleType()

    scroll_rect_item.data = scroll_rect_item.data or {index,dataItem}
    scroll_rect_item.data[1] = index
    scroll_rect_item.data[2] = dataItem
    
    local headTrans = scroll_rect_item:Get("headTrans")
    local helpTips = scroll_rect_item:Get("HelpTips")
    local queueIcon = scroll_rect_item:Get("QueueIcon")
    if window.campaignFRFlag and dataItem.heroID == CampaignFR_TrialHeroID and not window.hasCampaignFRTrialHero and window.campaignFRUseTrail then
        helpTips:SetActive(true)
        local child = helpTips.transform:GetChild(0)
        if child then
            child.gameObject:SetActive(true)
            local tmpTipsText = child:GetComponent(typeof(Text))
            if tmpTipsText then
                tmpTipsText.text = lang.Get(1008162)
            end
        end
    else
        helpTips:SetActive(false)
    end
    queueIcon:SetActive(false)
    
    local profile = scroll_rect_item.data[3] or hero_item.CHeroItem(_NAME):Init(headTrans,function (p)
        if not p then return end
        p:DisplayInfo()
    end ,0.85)
    scroll_rect_item.data[3] = profile
    window._cache_profile[dataItem.heroID] = profile
    local onClickHero= function (item)
        if window._pause_interact then
            return
        end
        if window.campaignFRFlag and dataItem.heroID == CampaignFR_TrialHeroID then
            flow_text.Add(lang.Get(1008068))
            return
        end
        dataItem.isUp = dataItem.isUp == 0 and 1 or 0
        window:OnSelectHero(dataItem,true)
    end
    
    profile:SetHero(dataItem,onClickHero,true)
    profile:GrayHeroIcon(false)
    
    if window.campaignFRFlag and dataItem.heroID == CampaignFR_TrialHeroID then
        -- 拥有英雄，或者关卡提供试用英雄
        if window.hasCampaignFRTrialHero or window.campaignFRUseTrail then
            dataItem.isUp = 1
            profile:SelectHero(true)
        else
            -- 不可用英雄，置灰
            dataItem.isUp = 1
            profile:SelectHero(false)
            -- 头像置灰
            profile:GrayHeroIcon(true)
            window.Campaign_FirstRecharge_GetBtnSC.transform:SetParent(scroll_rect_item.transform)
            window.Campaign_FirstRecharge_GetBtnSC.transform.localPosition = { x = 0, y = -40, z = 0 }
            window.Campaign_FirstRecharge_GetBtnSC.transform.localScale = { x = 1, y = 1, z = 1 }
            window.Campaign_FirstRecharge_GetBtnSC.gameObject:SetActive(true)
            window.Campaign_FirstRecharge_GetBtnSC.transform:SetAsLastSibling()
        end
    else
        if window:IsSelected(dataItem) then
            profile:SelectHero(true)
        else
            profile:SelectHero(false)
        end
    end
    
    scroll_rect_item.gameObject:SetActive(true)
    window:UpdateFingerGuide()
end


function SelectHeroWindow:GetSelectedHeroHandBookID()
    local selectedHeroHandBookID = {}
    local j,k
    for j,k in pairs(TabData) do
        if k and k.selectedHero then
            for _,v in pairs(k.selectedHero) do
                if v then
                    local cfg_select = game_scheme:Hero_0(v.heroID)
                    if cfg_select then
                        selectedHeroHandBookID[v.heroID] = cfg_select.HerohandBookID
                        selectedHeroHandBookID[cfg_select.HerohandBookID] = cfg_select.HerohandBookID--保证同个英雄不同HerohandBookID都设置成已选择状态
                    end
                end
            end
        end
    end
    return selectedHeroHandBookID
end

function GetHeroInCache(heroID)
    local heroCfg = nil
    if  heroID then
        heroCfg = heroCfgCache[heroID]
        if not heroCfg then
            heroCfg = game_scheme:Hero_0(heroID)
            if heroCfg then
                heroCfgCache[heroID] = heroCfg
            end
        end
    end
    return heroCfg
    
end

function SelectHeroWindow:BuildHero( hero )
    if self._buildHeroCache then
        if self._buildHeroCache[hero.heroID] then
            return self._buildHeroCache[hero.heroID]
        end
    end
    local hEntity =  gw_hero_data.GetHeroEntity(hero.heroSid)
    local attr = hEntity:GetHeroBaseShowAttr()
    local attrHp = attr.hp
    local attrAtk = attr.atk
    -- 和试用英雄比较，使用最大的属性值
    if self.campaignFRFlag and hero.heroID == CampaignFR_TrialHeroID and self.campaignFRTrailUnitConfig ~= nil then
        attrHp = math.max(attrHp, self.campaignFRTrailUnitConfig.HP)
        attrAtk = math.max(attrAtk, self.campaignFRTrailUnitConfig.ATK)
    end

    local Up = 0
    local heroCfg = GetHeroInCache(hero.heroID)
    local  result =
    {
        hero = hero,
        isUp = hero.isUp or 0,
        sameUp = hero.sameUp,
        numProp = {
            lv = hero.numProp.lv,
            starLv = hero.numProp.starLv
        },
        hasTeamIndex = hero.hasTeamIndex,
        teamIndex = hero.teamIndex,
        rarityType = heroCfg.rarityType,
        battleProp =
        {
            power = hEntity:GetPower(true),
            hp = attrHp,
            attack = attrAtk,
        },
        type = hero.type,
        heroID = hero.heroID,
        heroSid = hero.heroSid,
        isTrial = hero.isTrial,
        hasFinish = hero.hasFinish,
        profession = heroCfg.profession,
    }
    if not self._buildHeroCache then
        self._buildHeroCache = {}
    end
    self._buildHeroCache[result.heroID] = result
    return result
end

-- 1 肉盾 。 4 输出 5 辅助
local HeroProfessionType = 
{
    Tank = 1,
    Dps = 4,
    Support = 5,
}

function SelectHeroWindow:OnQuickFightEvent()
    if self._pause_interact then
        return
    end

    --开始上阵，先清已上阵的数据
    if self._save_datas then
        for k,v in pairs(self._save_datas) do
            local heroID = k
            if heroID then
                if self._buildHeroCache and self._buildHeroCache[heroID] then
                    local hero = self._buildHeroCache[heroID]
                    hero.isUp = 0
                    self:OnSelectHero(hero)
                end
            end
        end
    end
    
    local quickFightHeroSet = self:GetQuickFightHeroList()

    for i = 1, #quickFightHeroSet do
        self._pause_interact = true
        --防止卡顿，简单分帧下
        util.DelayCallOnce((i - 1) * 0.05, function()
            local hero = quickFightHeroSet[i]
            hero.isUp = 1
            self:OnSelectHero(hero)
            if i == #quickFightHeroSet then
                self._pause_interact = false
                self:UpdateFingerGuide()
            end
        end)
    end

end

function SelectHeroWindow:GetQuickFightHeroList()
    local quickFightHeroSet = {}
    local tankUpMax = 2
    local tankUpCounter = 0
    local dpsUpMax = 3
    local dpsUpCounter = 0
    --[[local supportUpMax = 0
    local supportUpCounter = 0]]--
    if self._tank_hero_set then
        if tankUpCounter< tankUpMax then
            local maxNum = math.min(#self._tank_hero_set,tankUpMax - tankUpCounter)
            if maxNum > 0 then
                for i =1,maxNum do
                    quickFightHeroSet[#quickFightHeroSet+1] = self._tank_hero_set[i]
                    tankUpCounter = tankUpCounter +1
                end
            end
        end
    end

    if self._dps_hero_set then
        if dpsUpCounter< dpsUpMax then
            local maxNum = math.min(#self._dps_hero_set,dpsUpMax - dpsUpCounter)
            if maxNum > 0 then
                for i =1,maxNum do
                    quickFightHeroSet[#quickFightHeroSet+1] = self._dps_hero_set[i]
                    dpsUpCounter = dpsUpCounter +1
                end
            end
        end
    end

    --[[if self._support_hero_set then
        if supportUpCounter< supportUpMax then
            local maxNum = math.min(#self._support_hero_set,supportUpMax - supportUpCounter)
            if maxNum > 0 then
                for i =1,maxNum do
                    quickFightHeroSet[#quickFightHeroSet+1] = self._support_hero_set[i]
                    supportUpCounter = supportUpCounter +1
                end
            end
        end
    end]]--

    --没上满,从全部英雄上帧剩余英雄
    if self._all_hero_set then
        if #quickFightHeroSet < self.UpNumMax and #self._all_hero_set>#quickFightHeroSet then
            for i = (#quickFightHeroSet + 1) ,#self._all_hero_set do
                if self._all_hero_set[i].isUp == 0 then
                    quickFightHeroSet[#quickFightHeroSet+1] = self._all_hero_set[i]
                    if #quickFightHeroSet >= self.UpNumMax then
                        break
                    end
                end
            end
        end
    end
    return quickFightHeroSet
end

function SelectHeroWindow:GetFilteredHero(quality,campIndex)
    local t = {}
    local dead = {}
    -- 增加 validHero 表，以避开带 metatable 的包含 hero_entity 的表格进行排序，以提高性能
    local validHero = {}
    local heroPart = {}
    local ownHeroList = gw_hero_data.GetOwnedHeroIDList()
    for i,v in ipairs(ownHeroList) do
        local heroData = gw_hero_data.GetOldHeroEntityByHeroId(v)
        if heroData then
            heroPart[v] = heroData
        end
    end

    self._all_hero_set = {}
    self._dps_hero_set = {}
    self._tank_hero_set = {}
    self._profession_cache = false
    for sid, hero in pairs(heroPart) do
        local heroID = hero.heroID
        local heroCfg = GetHeroInCache(heroID)
        local profession
        local otmHero
        if heroCfg then
            local typeV = heroCfg.type
            profession = heroCfg.profession
            local typeLimit = ( not quality) or (quality == 0 or typeV == quality)
            local campLimit = ((not campIndex) or (campIndex==0 or profession == campIndex))
            if typeLimit and campLimit then
                otmHero = self:BuildHero(hero)
                table.insert(validHero, otmHero )
            end
        end
    end
    
    if self.campaignFRFlag then
        if not heroPart[CampaignFR_TrialHeroID] then
            --- 记录是否拥有试用英雄
            self.hasCampaignFRTrialHero = false
            local heroCfg = GetHeroInCache(CampaignFR_TrialHeroID)
            local typeV = heroCfg.type
            local profession = heroCfg.profession
            local typeLimit = (not quality) or (quality == 0 or typeV == quality)
            local campLimit = ((not campIndex) or (campIndex == 0 or profession == campIndex))
            if typeLimit and campLimit then
                if self.CampaignFRFakerHero == nil then
                    local tmpFakerHero = {
                        heroID = CampaignFR_TrialHeroID,
                    }
                    self.CampaignFRFakerHero = {
                        hero = tmpFakerHero,
                        rarityType = heroCfg.rarityType,
                        battleProp =
                        {
                            power = 0,
                            hp = self.campaignFRTrailUnitConfig and self.campaignFRTrailUnitConfig.HP or 0,
                            attack = self.campaignFRTrailUnitConfig and self.campaignFRTrailUnitConfig.ATK or 0,
                        },
                        heroID = tmpFakerHero.heroID,
                        profession = heroCfg.profession,
                        -- 添加试用标记
                        isTrial = true,
                    }
                end
                table.insert(validHero, self.CampaignFRFakerHero)
            end
        else
            self.hasCampaignFRTrialHero = true
        end
    end

    for i=1,#validHero do
        local otmHero = validHero[i]
        local heroCfg = GetHeroInCache(otmHero.heroID)
        local profession = heroCfg.profession
        if not self._profession_cache then
            if otmHero then
                if profession == HeroProfessionType.Tank then
                    self._tank_hero_set = self._tank_hero_set or {}
                    self._tank_hero_set[#self._tank_hero_set+1] = otmHero
                    --[[elseif profession == HeroProfessionType.Dps then
                        self._dps_hero_set = self._dps_hero_set or {}
                        self._dps_hero_set[#self._dps_hero_set+1] = otmHero
                    else
                        self._support_hero_set = self._support_hero_set or {}
                        self._support_hero_set[#self._support_hero_set+1] = otmHero]]--
                else
                    self._dps_hero_set = self._dps_hero_set or {}
                    self._dps_hero_set[#self._dps_hero_set+1] = otmHero
                end
                self._all_hero_set = self._all_hero_set or {}
                self._all_hero_set[#self._all_hero_set+1] = otmHero
            end
        end
    end
    
    -- 职业缓存
    self._profession_cache = true
    if self._tank_hero_set then
        table.sort(self._tank_hero_set,function(h1,h2)
            return h1.battleProp.power > h2.battleProp.power
        end)
    end

    if self._dps_hero_set then
        table.sort(self._dps_hero_set,function(h1,h2)
            return h1.battleProp.power > h2.battleProp.power
        end)
    end

    --[[if self._support_hero_set then
        table.sort(self._support_hero_set,function(h1,h2)
            return h1.battleProp.power > h2.battleProp.power
        end)
    end]]--

    if self._all_hero_set then
        table.sort(self._all_hero_set,function(h1,h2)
            return h1.battleProp.power > h2.battleProp.power
        end)
    end
    
    local SortRule = function (t1, t2)
        if t1.heroID ~= t2.heroID then
            return t1.heroID < t2.heroID
        end
        return t1.heroSid < t2.heroSid
    end

    table.sort(validHero, SortRule)
    t = {}
    local i,v
    for i,v in ipairs(validHero) do
        table.insert(t, v)
    end

    for k,v in ipairs(dead) do
        table.insert(t, v)
    end
    return t
end

--[[英雄列表里英雄的数据去掉被选中的英雄不显示]]
function SelectHeroWindow:GetUnselectHerodata()
    local tempData = self:GetFilteredHero(self.quality,self.campIndex)
    local heroListData = {}
    if tempData ~= nil then
        for sid, hero in pairs(tempData) do
            table.insert(heroListData, hero)
        end
    end
    return heroListData
end

function SelectHeroWindow:OnShow()
    self.UpNumMax = self.campaignFRFlag and 3 or 5
    self:InitHeroWin()
    -- 首充活动不开启一键布阵
    local isQuickFightFunc = not self.campaignFRFlag and tiny_mgr.IsQuickFightFunc()
    self.quickFightBtn.gameObject:SetActive(isQuickFightFunc)
    --神兽按钮默认显示
    self.HaloAndWeapon.gameObject:SetActive(self.hideHaloAndWeapon ~= true)

    --设置当前通关进度
    local laymain_data = require "laymain_data"
    local curlevel = laymain_data.GetPassLevel()
    local gw_zombie_treasure_data = require "gw_zombie_treasure_data"
    gw_zombie_treasure_data.SetMiniGamePassLevel(curlevel)

    self.Campaign_FirstRechargeRoot.gameObject:SetActive(self.campaignFRFlag)
    self.Campaign_FirstRechargeMask.gameObject:SetActive(false)
    self:RefreshTrailHeroState()
end

--- 刷新试用英雄状态，购买后要立刻刷新
function SelectHeroWindow:RefreshTrailHeroState()
    self.Campaign_FirstRecharge_PowerInfo.gameObject:SetActive(self.hasCampaignFRTrialHero)
    if self.hasCampaignFRTrialHero then
        local allPower = 0
        local checkPower = 0
        if self.levelSrc and self.levelSrc.MiniLevelCfg then
            allPower = self.levelSrc.MiniLevelCfg.LevelPower or 0
            checkPower = self.levelSrc.MiniLevelCfg.LevelCheckPower or 0
        end
        local recommendPower = math.max(0, allPower - checkPower)
        local curPower = gw_power_mgr.GetHeroPowerByCfgId(CampaignFR_TrialHeroID)
        if curPower < recommendPower then
            self.Campaign_FirstRecharge_PowerText.text = string.format("<color=#FF5D5D>%s</color>/%s",
                util.NumberWithUnit2(curPower), util.NumberWithUnit2(recommendPower))
        else
            self.Campaign_FirstRecharge_PowerText.text = string.format("%s/%s", util.NumberWithUnit2(curPower),
                util.NumberWithUnit2(recommendPower))
        end
        local fillProcess = math.min(1, curPower / recommendPower)
        self.Campaign_FirstRecharge_PowerFill:SetSizeWithCurrentAnchors(UnityAxis.Horizontal, fillProcess * CampaignFR_PowerFileWidth)
    end
end

--- 主动刷新上阵英雄数据，先下阵再上阵
function SelectHeroWindow:RefreshHeroSlot()
    --开始上阵，先清已上阵的数据
    if self._save_datas then
        for k, v in pairs(self._save_datas) do
            local hero = self._buildHeroCache[k]
            if hero then
                hero.isUp = 0
                event.Trigger(event.SELECTED_HERO, hero, nil, { player_power_txt = self.player_power_txt })
                hero.isUp = 1
            end
        end
    end
    if self.CampaignFRFakerHero then
        self.CampaignFRFakerHero.isUp = 0
        event.Trigger(event.SELECTED_HERO, self.CampaignFRFakerHero, nil, { player_power_txt = self.player_power_txt })
        self.CampaignFRFakerHero.isUp = 1
    end
    -- 清空缓存
    self._buildHeroCache = nil
    self._up_num = 0
    self:SetFilterHero(nil, nil, nil)
end

function SelectHeroWindow:Close()
    if self:IsValid() then
		self:UnsubscribeEvents()
	end
    self.__base:Close()
    if self.campaignFRFlag then
        self.Campaign_FirstRecharge_GetBtnSC.transform:SetParent(self.Campaign_FirstRechargeRoot.transform)
    end
    if self.guideTimer then
        util.RemoveDelayCall(self.guideTimer)
        self.guideTimer = nil
    end
    self:SaveUpHeroData()
    --containerTrans:SetActive(true)
    -- 关闭后清空Tag,防止下次打开时使用上次保存的阵容布局
    self.saveTag = nil
    self.hideHaloAndWeapon = nil
    self.campaignFRFlag = nil
    self.levelSrc = nil
    self.campaignFRUseTrail = nil
    self.campaignFRTrailUnitConfig = nil
    self._buildHeroCache = nil
    self._up_num = 0
    self.campaignFR_WaitCheck = nil
end

function SelectHeroWindow:SetInputParam(saveTag,hideHaloAndWeapon,campaignFRFlag,levelSrc)
    -- 阵容布局本地保存的Tag，为空走默认的Key
    self.saveTag = saveTag
    self.hideHaloAndWeapon = hideHaloAndWeapon
    self.campaignFRFlag = campaignFRFlag
    self.levelSrc = levelSrc
    if self.campaignFRFlag and self.levelSrc then
        self.campaignFRTrailUnitConfig = self.levelSrc:GetCampaignFR_TrialUnitConfig()
        --- 记录是否提供试用英雄
        self.campaignFRUseTrail = self.campaignFRTrailUnitConfig ~= nil
    end
end

function Show()

    if window == nil then
        window = CSelectHeroWindow()
        window._NAME = _NAME;window:LoadUIResource("ui/prefabs/gw/buildsystem/uiminigameheroselectpanel.prefab", nil, nil,nil,true,true)
    else
        window:Show()
    end
    return window
end

function Hide()
    if window ~= nil then
        window:Hide()
    end
end


function Close()
    if window ~= nil then
        window:Close()
        window = nil
    end
end