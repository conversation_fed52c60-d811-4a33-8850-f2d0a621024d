local print = print
local require = require
local pairs = pairs     
local ipairs = ipairs
local string = string
local table = table
local tostring = tostring
local tonumber = tonumber
local type = type
local UIUtil = CS.Common_Util.UIUtil
local typeof = typeof
local GameObject    = CS.UnityEngine.GameObject
local SpriteSwitcher = CS.War.UI.SpriteSwitcher
local ScrollRect = CS.UnityEngine.UI.ScrollRect
local Image = CS.UnityEngine.UI.Image
local Common_Util = CS.Common_Util.UIUtil

local Vector3 = CS.UnityEngine.Vector3

local enum_define = require "enum_define"
local ui_base = require "ui_base"
local lang = require "lang"
local util = require "util"
local class = require "class"
local binding = require "ui_straydog_house_binding"
local GWConst            = require "gw_const"
local effect_item = require "effect_item"
local render_tool = require "render_tool"
local log = require "log"
local GWHomeMgr          = GWHomeMgr
local GWAdmin            = GWAdmin

--region View Life
module("ui_straydog_house")
local ui_path = binding.UIPath
local window = nil
local UIView = {}

UIView.widget_table = binding.WidgetTable


local effectPath = "art/effects/effects/fx_ui_liulanggou_rukou/prefabs/fx_ui_liulanggou_rukou.prefab"

function UIView:SetVCTypeUI()
    return self.__base.SetVCTypeUI(self, enum_define.enum_ui_vc_type.vc)
end

function UIView:Init()
    self:SetVCTypeUI()
    self.__base.Init(self)

    self.VData = {}

    self.renderderParamMap = {}
    self.renderderParamMap[1] = {
        resPath="animations/characters/liulanggou/edit_liulanggou.prefab",
        scale={x=1,y=1,z=1},
        position={x=-107,y=147,z=0}
    }
    self.renderderParamMap[2] = {
        resPath="animations/characters/liulanggou_02/edit_liulanggou_02.prefab",
        scale={x=0.9,y=0.9,z=0.9},
        position={x=-107,y=117,z=0}
    }
end

function UIView:OnShow()
    self.__base.OnShow(self)

    

    self.sg_Upgrade.sortingOrder = self.curOrder + 2

    self.sg_txt_Upgrade.sortingOrder = self.curOrder + 3
end

function UIView:OnHide()
    self.__base.OnHide(self)
end

function UIView:Close(data)   
    if self.VData then
        for i, v in pairs(self.VData) do
            if type(v) == "table" and v.Dispose then
                v:Dispose()
            end
        end
    end
    self.VData = nil    
    self:UnLoadEffect()
    self.curLoadLevel = 0
    self.__base.Close(self)
    self.itemList = nil
    window = nil
end
--endregion

--region View Logic

function UIView:LoadEffect(level)
    if self.curLoadLevel == level then
        return
    end

    self.curLoadLevel = level or 0
    -- self.isRenderderMap = {}--是否已经设置过了，全局就设置一次
    -- self.renderCallBackMap = {}--设置回调，还没加载出来之前，设置为回调，加载出来后调用
    -- self.renderTxInfo = nil --可以理解为camera的对象，用来渲染texture的，close要释放
    -- self.renderGameObject = false--唯一变量，保证特效和camera只加载一次
    local rederderParam = self.renderderParamMap[self.curLoadLevel]
    self.rtf_RoleParent.localPosition = rederderParam.position
    self.rtf_RoleParent.localScale = rederderParam.scale
   
    self:UnLoadEffect()
    self:DisplayParticleWithRT(self.rImg_RoleImg, rederderParam.resPath, true)
end

function UIView:DisplayParticleWithRT(rawimage, resPath, enable)
    if enable then
        if not self.isRenderderMap[rawimage] then
            --如果camera已经加载完毕，直接加载particle
            if self.renderTxInfo then
                self.renderTxInfo:SetImage(rawimage.transform, rawimage, true)
                self.isRenderderMap[rawimage] = true
                Common_Util.SetActive(rawimage, true)
            else
                --加入回调，等异步刷新
                if not self.renderCallBackMap[rawimage] then
                    self.renderCallBackMap[rawimage] = function()
                        --如果camera已经加载完毕，直接加载particle
                        if not self.isRenderderMap[rawimage] then
                            if self.renderTxInfo then
                                self.renderTxInfo:SetImage(rawimage.transform, rawimage, true)
                                self.isRenderderMap[rawimage] = true
                                Common_Util.SetActive(rawimage, true)
                            end
                        end
                    end
                end
            end
        else
            Common_Util.SetActive(rawimage, true)
        end
    else
        Common_Util.SetActive(rawimage, false)
    end
    if enable and not self.renderGameObject then
        self.renderGameObject = true
        render_tool.LoadResource(resPath, function(assetbundleName, renderGameObject)
            if not self:IsValid() then return end
            if renderGameObject then
                renderGameObject.SetScale({x=-0.5,y=0.5,z=0.5})
                self.renderTxInfo = renderGameObject.RenderGameObject(resPath, 1024, 1024, render_tool.RenderMode.Single, 0, -2, 8.3, nil, _NAME)
                local renderInfo = render_tool.GetRenderInfoByTag(resPath)
                if renderInfo and renderInfo.camera then
                    renderInfo.camera.orthographicSize = 8.3
                end
                if self.renderTxInfo then
                    for k, v in pairs(self.renderCallBackMap) do
                        v()
                    end
                else
                    log.Error("Failed to load self.renderTxInfo for path: ", resPath)
                end
            else
                log.Error("Failed to load resource for path: ", resPath)
            end
        end)
    end
    return true
end

function UIView:UnLoadEffect()
    if self.levelupEffect then
        local gw_ui_effect_utility = require "gw_ui_effect_utility"
        gw_ui_effect_utility.RecycleEffectRendererTextureByItem(self.levelupEffect)
        self.levelupEffect = nil
    end
    
    if  self.rImg_RoleImg then
        self.rImg_RoleImg.texture = nil
    end
    if self.renderTxInfo then
        local renderParam = self.renderderParamMap[self.curLoadLevel]
        render_tool.ReleaseRenderGameObject(renderParam.resPath, _NAME, self.renderTxInfo)
        self.renderTxInfo = nil
    end
    self.renderGameObject = false
    self.renderCallBackMap = {}
    self.isRenderderMap = {}
end


function UIView:RefreshView(houseData)

    self:LoadEffect(houseData.Level)

    if not houseData.IsMaxLevel then
        local gw_ui_effect_utility = require "gw_ui_effect_utility"
        self.levelupEffect = self.levelupEffect or  gw_ui_effect_utility.GetEffectRendererTexture(effectPath, function(item, renderTexture)
                        self.rImg_Effect:SetActive(true)
                        self.rImg_Effect.texture = renderTexture
                        self.rImg_Effect.transform.localScale = Vector3(2.8, 2.8, 2.8)
                        self.rImg_Effect.transform.localPosition = Vector3(0, 0, 0)
                    end)
    end
   
    --狗屋等级
    self.txt_Level.text = "LV." ..houseData.Level
    --1级狗屋icon
    UIUtil.SetActive(self.tf_HouseIcon_1, houseData.Level == 1)
    --高级狗屋icon
    UIUtil.SetActive(self.tf_HouseIcon_2, houseData.Level > 1)
    --小狗icon
    self.ss_DogIcon:Switch(houseData.Level - 1)
    --是否有奖励可领取
    local canGetAward = houseData.RewardIdList and #houseData.RewardIdList > 0
    --奖励组
    UIUtil.SetActive(self.tf_Award, canGetAward)
    --无奖励组
    UIUtil.SetActive(self.tf_NoAward, not canGetAward)
    --升级按钮
    UIUtil.SetActive(self.btn_Upgrade, not houseData.IsMaxLevel)
    --升级按钮红点，升级材料足够显示
    local buildType = GWConst.enBuildingType.enBuildingType_DogHouse
    local buildingData = GWHomeMgr.buildingData.GetBuildingDataByBuildingType(buildType)
    UIUtil.SetActive(self.tf_UpgradeRed, buildingData and GWAdmin.GWHomeCfgUtil.GetBuildingUpgradeData(buildingData, true))
    --奖励按钮红点文本
    self.txt_RewardNum.text = tostring(#houseData.RewardIdList)
    --奖励列表
    if canGetAward then
        if self.itemList then
            for i = 1, #self.itemList do
                GameObject.Destroy(self.itemList[i])
            end
        end
        self.itemList = {}
        for i = 1, #houseData.RewardIdList do
            local reward_mgr = require "reward_mgr"
            local rewardItem = GameObject.Instantiate(self.tf_RewardItem, self.rtf_RewardParent.transform)
            rewardItem.gameObject:SetActive(true)
            table.insert(self.itemList, rewardItem.gameObject)
            local quality = reward_mgr.GetRewardMaxQuality(houseData.RewardIdList[i])
            local spriteIndex = 0
            if quality >= 5 then
                spriteIndex = 2
            elseif quality >= 4 then
                spriteIndex = 1
            else
                spriteIndex = 0
            end
            rewardItem:GetComponent(typeof(SpriteSwitcher)):Switch(spriteIndex)
            rewardItem:GetComponent(typeof(Image)):SetNativeSize()
        end
        local scrollRect = self.rtf_RechargeParent:GetComponent(typeof(ScrollRect))
        if scrollRect then
            scrollRect.horizontalNormalizedPosition = #self.itemList >= 5 and 0 or 0.5
        end
    end
end

--endregion

--region View Static 
local CUIView = class(ui_base, nil, UIView)

function Show(data)
    if window == nil then
        window = CUIView()
        window._NAME = _NAME

        if data and type(data) == "table" then
            local tempPath = data.uiPath or ui_path
            local tempParent = data.uiParent or nil
			window:LoadUIResource(tempPath, nil, tempParent, nil, nil, false)
        else
			window:LoadUIResource(ui_path, nil, nil, nil,nil, false)
        end
    end
    window:Show()
    return window
end

function Close(data)
    if window ~= nil then
        window:Close(data)
        window = nil
    end
end

function Hide()
    if window ~= nil then
        window:Hide()
    end
end
--endregion