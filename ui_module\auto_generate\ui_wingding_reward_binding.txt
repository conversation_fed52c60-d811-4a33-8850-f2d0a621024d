local require = require
local typeof = typeof

local Button = CS.UnityEngine.UI.Button
local ScrollRectTable = CS.UI.UGUIExtend.ScrollRectTable


module("ui_wingding_reward_binding")

UIPath = "ui/prefabs/gw/activity/halloweenactivity/wingding/uiwingdingreward.prefab"

WidgetTable ={
	btn_closeBtn = { path = "btn_closeBtn", type = Button, event_name = "OnBtnCloseBtnClickedProxy", backEvent = true},
	srt_Content = { path = "Panel/rewardList/ViewPort/srt_Content", type = ScrollRectTable, },

}
